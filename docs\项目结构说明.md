# 数懒平台项目结构说明

## 整体项目结构

```
agent-crawl/
├── browser-viewer/                 # 浏览器插件 (已完成)
│   ├── src/                       # 插件源码
│   ├── dist/                      # 构建输出
│   ├── package.json               # 插件依赖
│   └── 数懒浏览器代理-技术方案.md   # 插件技术文档
├── backend/                       # Python后端服务
│   ├── app/                       # 应用代码
│   ├── tests/                     # 测试代码
│   ├── requirements.txt           # Python依赖
│   └── README.md                  # 后端说明
├── frontend/                      # React前端应用
│   ├── src/                       # 前端源码
│   ├── public/                    # 静态资源
│   ├── package.json               # 前端依赖
│   └── README.md                  # 前端说明
├── docs/                          # 项目文档
│   ├── 数懒平台-系统架构方案.md     # 系统架构文档
│   ├── 数懒浏览器插件-技术方案.md   # 插件技术文档
│   └── 项目结构说明.md             # 本文档
├── scripts/                       # 构建和部署脚本
├── docker/                        # Docker配置文件
├── README.md                      # 项目总览
└── .gitignore                     # Git忽略文件
```

## 后端服务结构 (Python + FastAPI)

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI应用入口
│   ├── core/                      # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py             # 配置管理
│   │   ├── security.py           # 安全认证
│   │   ├── database.py           # 数据库连接
│   │   └── dependencies.py       # 依赖注入
│   ├── api/                       # API路由层
│   │   ├── __init__.py
│   │   ├── v1/                   # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── auth.py           # 认证接口
│   │   │   ├── tasks.py          # 任务管理接口
│   │   │   ├── users.py          # 用户管理接口
│   │   │   ├── data.py           # 数据处理接口
│   │   │   └── websocket.py      # WebSocket接口
│   │   └── deps.py               # API依赖
│   ├── services/                  # 业务服务层
│   │   ├── __init__.py
│   │   ├── task_orchestrator.py  # 任务编排服务
│   │   ├── task_scheduler.py     # 任务调度服务
│   │   ├── data_processor.py     # 数据处理服务
│   │   ├── ai_service.py         # AI服务
│   │   ├── notification_service.py # 通知服务
│   │   └── auth_service.py       # 认证服务
│   ├── models/                    # 数据模型层
│   │   ├── __init__.py
│   │   ├── user.py               # 用户模型
│   │   ├── task.py               # 任务模型
│   │   ├── data.py               # 数据模型
│   │   └── base.py               # 基础模型
│   ├── schemas/                   # Pydantic模式
│   │   ├── __init__.py
│   │   ├── user.py               # 用户模式
│   │   ├── task.py               # 任务模式
│   │   ├── data.py               # 数据模式
│   │   └── common.py             # 通用模式
│   ├── utils/                     # 工具函数
│   │   ├── __init__.py
│   │   ├── logger.py             # 日志工具
│   │   ├── redis_client.py       # Redis客户端
│   │   ├── message_queue.py      # 消息队列
│   │   └── storage.py            # 存储工具
│   └── workers/                   # 后台任务
│       ├── __init__.py
│       ├── task_worker.py        # 任务处理器
│       ├── data_worker.py        # 数据处理器
│       └── scheduler.py          # 调度器
├── tests/                         # 测试代码
│   ├── __init__.py
│   ├── conftest.py               # 测试配置
│   ├── test_api/                 # API测试
│   ├── test_services/            # 服务测试
│   └── test_models/              # 模型测试
├── alembic/                       # 数据库迁移
│   ├── versions/                 # 迁移版本
│   ├── env.py                    # 迁移环境
│   └── alembic.ini               # 迁移配置
├── docker/                        # Docker配置
│   ├── Dockerfile                # 应用镜像
│   ├── docker-compose.yml        # 开发环境
│   └── docker-compose.prod.yml   # 生产环境
├── requirements.txt               # 生产依赖
├── requirements-dev.txt           # 开发依赖
├── pyproject.toml                # 项目配置
├── .env.example                  # 环境变量示例
└── README.md                     # 后端说明
```

## 前端应用结构 (React + TypeScript)

```
frontend/
├── src/
│   ├── components/               # 组件库
│   │   ├── Common/              # 通用组件
│   │   │   ├── LoadingSpinner.tsx
│   │   │   ├── ErrorBoundary.tsx
│   │   │   └── ConfirmDialog.tsx
│   │   ├── Layout/              # 布局组件
│   │   │   ├── Layout.tsx
│   │   │   ├── Header.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   └── Footer.tsx
│   │   ├── Task/                # 任务相关组件
│   │   │   ├── TaskList.tsx
│   │   │   ├── TaskCard.tsx
│   │   │   ├── TaskDetail.tsx
│   │   │   ├── TaskForm.tsx
│   │   │   └── TaskProgress.tsx
│   │   ├── Data/                # 数据可视化组件
│   │   │   ├── DataChart.tsx
│   │   │   ├── DataTable.tsx
│   │   │   └── DataExport.tsx
│   │   └── User/                # 用户相关组件
│   │       ├── UserProfile.tsx
│   │       ├── UserSettings.tsx
│   │       └── UserAvatar.tsx
│   ├── pages/                   # 页面组件
│   │   ├── Dashboard/           # 仪表板
│   │   │   ├── Dashboard.tsx
│   │   │   └── DashboardCharts.tsx
│   │   ├── Tasks/               # 任务管理
│   │   │   ├── TaskManagement.tsx
│   │   │   ├── TaskDetail.tsx
│   │   │   └── TaskCreate.tsx
│   │   ├── Data/                # 数据可视化
│   │   │   ├── DataVisualization.tsx
│   │   │   └── DataAnalysis.tsx
│   │   ├── User/                # 用户中心
│   │   │   ├── UserProfile.tsx
│   │   │   └── UserSettings.tsx
│   │   ├── Auth/                # 认证页面
│   │   │   ├── Login.tsx
│   │   │   └── Register.tsx
│   │   └── Settings/            # 系统设置
│   │       └── Settings.tsx
│   ├── stores/                  # 状态管理
│   │   ├── authStore.ts         # 认证状态
│   │   ├── taskStore.ts         # 任务状态
│   │   ├── userStore.ts         # 用户状态
│   │   ├── themeStore.ts        # 主题状态
│   │   └── index.ts             # 状态导出
│   ├── services/                # API服务
│   │   ├── api.ts               # API客户端
│   │   ├── authService.ts       # 认证服务
│   │   ├── taskService.ts       # 任务服务
│   │   ├── dataService.ts       # 数据服务
│   │   └── websocketService.ts  # WebSocket服务
│   ├── hooks/                   # 自定义Hook
│   │   ├── useAuth.ts           # 认证Hook
│   │   ├── useTasks.ts          # 任务Hook
│   │   ├── useWebSocket.ts      # WebSocket Hook
│   │   └── useLocalStorage.ts   # 本地存储Hook
│   ├── types/                   # 类型定义
│   │   ├── auth.ts              # 认证类型
│   │   ├── task.ts              # 任务类型
│   │   ├── user.ts              # 用户类型
│   │   ├── data.ts              # 数据类型
│   │   └── common.ts            # 通用类型
│   ├── utils/                   # 工具函数
│   │   ├── constants.ts         # 常量定义
│   │   ├── helpers.ts           # 辅助函数
│   │   ├── formatters.ts        # 格式化函数
│   │   └── validators.ts        # 验证函数
│   ├── styles/                  # 样式文件
│   │   ├── globals.css          # 全局样式
│   │   ├── variables.css        # CSS变量
│   │   └── components.css       # 组件样式
│   ├── App.tsx                  # 主应用组件
│   ├── main.tsx                 # 应用入口
│   └── vite-env.d.ts           # Vite类型声明
├── public/                      # 静态资源
│   ├── index.html              # HTML模板
│   ├── favicon.ico             # 网站图标
│   └── assets/                 # 静态资源
├── tests/                       # 测试代码
│   ├── setup.ts                # 测试设置
│   ├── components/             # 组件测试
│   ├── pages/                  # 页面测试
│   └── utils/                  # 工具测试
├── package.json                # 项目依赖
├── tsconfig.json               # TypeScript配置
├── vite.config.ts              # Vite配置
├── tailwind.config.js          # Tailwind配置
├── .eslintrc.js                # ESLint配置
├── .prettierrc                 # Prettier配置
└── README.md                   # 前端说明
```

## 浏览器插件结构 (已完成)

```
browser-viewer/
├── src/
│   ├── background/             # 后台脚本
│   ├── content/                # 内容脚本
│   ├── popup/                  # 弹出页面
│   ├── options/                # 设置页面
│   ├── core/                   # 核心模块
│   └── utils/                  # 工具函数
├── dist/                       # 构建输出
├── public/                     # 静态资源
├── tests/                      # 测试代码
├── package.json                # 项目依赖
├── manifest.json               # 插件清单
├── vite.config.ts              # 构建配置
└── 数懒浏览器代理-技术方案.md    # 技术文档
```

## 技术栈对应关系

### 后端 (Python + FastAPI)
- **Web框架**: FastAPI (高性能异步框架)
- **数据库**: PostgreSQL + SQLAlchemy ORM
- **缓存**: Redis (会话、队列、缓存)
- **消息队列**: RabbitMQ + Celery
- **AI/ML**: OpenAI API + Transformers + OpenCV
- **认证**: JWT + OAuth2
- **部署**: Docker + Kubernetes

### 前端 (React + TypeScript)
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design
- **状态管理**: Zustand
- **数据获取**: React Query
- **图表库**: ECharts
- **样式**: Tailwind CSS

### 浏览器插件 (TypeScript + Vue)
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: RxJS
- **标准**: Manifest V3
- **兼容性**: WebExtension Polyfill

## 开发流程

1. **后端开发**: 基于FastAPI的微服务架构，继承browser-viewer的任务调度设计
2. **前端开发**: React组件化开发，与后端API集成
3. **插件集成**: 扩展现有browser-viewer，添加平台通信能力
4. **测试部署**: 容器化部署，CI/CD自动化

## 数据流向

```
浏览器插件 → 后端API → 数据库/缓存
     ↓           ↓         ↓
前端界面 ← WebSocket ← 消息队列
```

这个结构设计充分利用了Python生态系统的AI/ML能力，同时保持了与browser-viewer插件的技术一致性和设计理念的延续。
