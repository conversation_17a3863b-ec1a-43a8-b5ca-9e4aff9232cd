/**
 * Image Processing Pipeline - 通过消息将处理任务委托给 Offscreen Document。
 */
import { MESSAGE_TYPES } from '../../shared/constants';
import type { ContentSegment, ImageSegment } from '../../shared/types';
import { createLogger, Logger } from '../../shared/utils';

export class ImageProcessingPipeline {
  private readonly _logger: Logger;

  constructor() {
    this._logger = createLogger('[ImageProcessingPipeline]');
    this._logger.info('ImageProcessingPipeline initialized, will delegate to offscreen document.');
  }

  /**
   * 将图像分段的处理任务委托给 Offscreen Document。
   * @param segments 要处理的图像分段数组。
   * @returns 一个解析为内容分段数组的Promise。
   */
  async processSegments(segments: ImageSegment[]): Promise<ContentSegment[]> {
    this._logger.info(`Delegating processing of ${segments.length} image segments to offscreen document...`);

    if (segments.length === 0) {
      return [];
    }
    
    try {
      // 通过消息机制将整个批次任务委托给后台的 Offscreen Document 处理。
      // 这样做可以避免在不稳定的 Worker 环境中处理扩展 API，并简化并发管理。
      const processedSegments: ContentSegment[] = await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.PROCESS_IMAGE_SEGMENTS,
        payload: { segments },
      });
      
      this._logger.info(`Successfully processed ${processedSegments.length} segments via offscreen document.`);
      
      // 排序步骤现在也可以在 offscreen 环境中完成，但为确保接口一致性，在此保留。
      processedSegments.sort((a, b) => a.position.y - b.position.y);

      return processedSegments;
    } catch (error) {
      this._logger.error('Failed to process segments via offscreen document:', error);
      // 在失败时返回一个空数组，以防止下游流程中断。
      return [];
    }
  }
} 