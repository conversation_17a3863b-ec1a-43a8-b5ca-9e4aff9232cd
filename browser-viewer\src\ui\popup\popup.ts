/**
 * Popup UI - 弹出界面逻辑
 */

import { MESSAGE_TYPES } from '@/shared/constants';
import { BatchTask, ServiceMessage, TaskConfig, UrlStatus } from '@/shared/types';
import { createLogger, Logger } from '@/shared/utils';
import '../main.css';

// 模式切换常量
declare const __DEV_MODE__: boolean;
const DEV_MODE = typeof __DEV_MODE__ !== 'undefined' ? __DEV_MODE__ : true;

class Popup {
  private readonly _logger: Logger = createLogger('Popup');
  
  // Service Worker communication
  private _port: chrome.runtime.Port | null = null;

  // UI Elements
  private _statusDot!: HTMLDivElement;
  private _statusText!: HTMLSpanElement;
  
  // Dev mode elements
  private _devModeSection!: HTMLDivElement;
  private _devUrls!: HTMLTextAreaElement;
  private _createTaskBtn!: HTMLButtonElement;

  // Controls
  private _startBtn!: HTMLButtonElement;
  private _pauseBtn!: HTMLButtonElement;
  private _clearBtn!: HTMLButtonElement;
  
  // Task list
  private _taskListDiv!: HTMLDivElement;

  // State
  private _tasks: BatchTask[] = [];
  private _lastActionTime?: number;
  private readonly _selectedBatchIds: Set<string> = new Set();
  private readonly _openBatchIds: Set<string> = new Set();

  constructor() {
    document.addEventListener('DOMContentLoaded', this._initialize.bind(this));
  }

  private _initialize(): void {
    this._logger.info('Initializing Popup...');
    this._initializeUI();
    this._connectToServiceWorker();
    this._requestInitialData();

    // 当点击外部区域时，关闭已展开的 <details>
    document.addEventListener('click', (event) => {
      const openDetails = this._taskListDiv.querySelector<HTMLDetailsElement>('details[open]');
      if (openDetails && !openDetails.contains(event.target as Node)) {
        openDetails.open = false;
      }
    });
  }

  private _initializeUI(): void {
    this._statusDot = document.getElementById('statusDot') as HTMLDivElement;
    this._statusText = document.getElementById('statusText') as HTMLSpanElement;
    this._taskListDiv = document.getElementById('taskList') as HTMLDivElement;

    // 根据模式初始化UI
    if (DEV_MODE) {
      this._initializeDevModeUI();
    } else {
      this._hideDevModeUI();
    }

    // Task list
    this._taskListDiv.addEventListener('change', (e) => {
      const target = e.target as HTMLInputElement;
      if (target.classList.contains('task-checkbox')) {
        const batchId = target.dataset.batchId;
        if (batchId) {
          if (target.checked) {
            this._selectedBatchIds.add(batchId);
          } else {
            this._selectedBatchIds.delete(batchId);
          }
          this._updateActionButtons();
        }
      }
    });

    this._taskListDiv.addEventListener('toggle', (e) => {
      const detailsElement = e.target as HTMLDetailsElement;
      if (detailsElement.tagName === 'DETAILS') {
        const batchId = detailsElement.dataset.batchId;
        if (batchId) {
          if (detailsElement.open) {
            this._logger.debug(`Toggled open details for batch: ${batchId}`);
            this._openBatchIds.add(batchId);
          } else {
            this._logger.debug(`Toggled close details for batch: ${batchId}`);
            this._openBatchIds.delete(batchId);
          }
        }
      }
    }, true);

    this._taskListDiv.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const downloadBtn = target.closest('.download-btn');

      if (downloadBtn) {
        const taskId = downloadBtn.getAttribute('data-task-id');
        const url = downloadBtn.getAttribute('data-url');
        
        if (taskId && url) {
          this._logger.info(`Requesting download for assets of URL: ${url} in task: ${taskId}`);
          this._sendMessage(this._createMessage(MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS, { taskId, url }));
        }
      }

      // 处理收起按钮的点击
      const collapseTrigger = target.closest('.collapse-trigger');
      if (collapseTrigger) {
        const details = collapseTrigger.closest('details');
        if (details) {
          details.open = false;
        }
      }
    });
  }

  private _initializeDevModeUI(): void {
    this._logger.info('Initializing Dev Mode UI...');

    // 显示开发模式UI容器
    this._devModeSection = document.getElementById('devModeSection') as HTMLDivElement;
    if (this._devModeSection) this._devModeSection.style.display = 'block';

    // 获取开发模式的UI元素
    this._devUrls = document.getElementById('devUrls') as HTMLTextAreaElement;
    this._createTaskBtn = document.getElementById('createTaskBtn') as HTMLButtonElement;
    
    // 全局控制按钮
    this._startBtn = document.getElementById('startBtn') as HTMLButtonElement;
    this._pauseBtn = document.getElementById('pauseBtn') as HTMLButtonElement;
    this._clearBtn = document.getElementById('clearBtn') as HTMLButtonElement;
    
    // 绑定事件监听
    if (this._createTaskBtn) {
      this._createTaskBtn.addEventListener('click', () => this._createBatchTask());
    }
    if (this._startBtn) {
      this._startBtn.addEventListener('click', () => this._handleTaskAction('start'));
    }
    if (this._pauseBtn) {
      this._pauseBtn.addEventListener('click', () => this._handleTaskAction('pause'));
    }
    if (this._clearBtn) {
      this._clearBtn.addEventListener('click', () => this._handleTaskAction('clear'));
    }
  }

  private _hideDevModeUI(): void {
    this._logger.info('Hiding Dev Mode UI...');
    
    // 隐藏开发模式UI容器
    this._devModeSection = document.getElementById('devModeSection') as HTMLDivElement;
    if (this._devModeSection) {
      this._devModeSection.style.display = 'none';
    }
  }

  private _connectToServiceWorker(): void {
    try {
      this._port = chrome.runtime.connect({ name: 'popup' });
      this._updateStatusWhenConnected();

      this._port.onDisconnect.addListener(() => {
        this._logger.warn('Disconnected from service worker.');
        this._updateStatusWhenDisconnected();
        this._port = null;
        // 灰色状态点
        if (this._statusDot) this._statusDot.classList.remove('active');
      });

      this._port.onMessage.addListener((message: ServiceMessage) => {
        this._logger.debug('Message from service worker:', message);
        if (message.type === MESSAGE_TYPES.TASKS_UPDATED) {
          const newTasks = message.payload as BatchTask[];
          this._logger.info(`Received ${newTasks.length} tasks update from service worker`);
          
          // 记录任务状态变化
          newTasks.forEach(task => {
            const oldTask = this._tasks.find(t => t.batchId === task.batchId);
            if (oldTask && oldTask.status !== task.status) {
              this._logger.info(`Task ${task.batchId} status changed: ${oldTask.status} -> ${task.status}`);
            }
          });
          
          this._tasks = newTasks;
          this._renderTasks();
        } else if (message.type === MESSAGE_TYPES.SYSTEM_STATUS) {
          if (message.payload && typeof message.payload === 'object') {
            const status = message.payload as { connected: boolean; isInitialized?: boolean };
            if (status.connected) {
              this._updateStatusWhenConnected(status.isInitialized);
            } else {
              this._updateStatusWhenDisconnected();
            }
          }
        }
      });
    } catch (error) {
      this._logger.error('Could not connect to service worker:', error);
      this._updateStatusWhenDisconnected();
    }

    // 连接后立即请求初始任务列表
    this._requestInitialData();
  }
  
  private _createMessage(type: string, payload?: unknown): ServiceMessage {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2)}`,
      type,
      payload,
      timestamp: Date.now()
    };
  }

  private _sendMessage(message: ServiceMessage): void {
    if (!this._port) {
      this._logger.error('Cannot send message: port is not connected.');
      // 尝试重新连接
      this._connectToServiceWorker();
      if (!this._port) return;
    }
    this._port.postMessage(message);
  }

  private _requestInitialData(): void {
    this._sendMessage(this._createMessage(MESSAGE_TYPES.GET_TASKS_STATUS));
  }

  private _createBatchTask(): void {
    const urls = this._devUrls.value
      .split(/[\n,]+/)
      .map(url => url.trim())
      .filter(Boolean);

    if (urls.length === 0) {
      this._logger.warn('No URLs provided for new batch task.');
      return;
    }

    this._logger.info(`Creating batch task with ${urls.length} URLs:`, urls);

    const taskConfig: TaskConfig = {
      urls,
      renderMode: 'headless',
      behaviorProfile: 'normal',
      outputFormat: 'markdown',
      priority: 10,
    };
    
    this._sendMessage(this._createMessage(MESSAGE_TYPES.CREATE_DEV_TASK, taskConfig));
    
    this._devUrls.value = '';
  }

  private _handleTaskAction(action: 'start' | 'pause' | 'clear'): void {
    const now = Date.now();
    const THROTTLE_DELAY = 500;

    if (this._lastActionTime && now - this._lastActionTime < THROTTLE_DELAY) {
      this._logger.warn('Action throttled to prevent duplicate requests');
      return;
    }
    this._lastActionTime = now;

    const hasSelection = this._selectedBatchIds.size > 0;
    const actionKey = `${action}_${hasSelection ? 'selected' : 'all'}`;

    // 在节流期间禁用所有按钮以防止重复点击
    this._setAllActionButtonsDisabled(true);
    setTimeout(() => {
      this._lastActionTime = undefined;
      // 节流结束后，根据当前任务的实际状态更新按钮的可用性
      this._updateActionButtons();
    }, THROTTLE_DELAY);

    const messageType = {
      start: hasSelection ? MESSAGE_TYPES.START_SELECTED_TASKS : MESSAGE_TYPES.START_ALL_TASKS,
      pause: hasSelection ? MESSAGE_TYPES.PAUSE_SELECTED_TASKS : MESSAGE_TYPES.PAUSE_ALL_TASKS,
      clear: hasSelection ? MESSAGE_TYPES.CLEAR_SELECTED_TASKS : MESSAGE_TYPES.CLEAR_ALL_TASKS,
    }[action];

    let payload: { batchIds?: string[] } = {};

    if (hasSelection) {
      const allSelectedIds = Array.from(this._selectedBatchIds);
      let eligibleIds: string[];

      switch (action) {
        case 'start':
          eligibleIds = this._tasks
            .filter(t => allSelectedIds.includes(t.batchId) && ['pending', 'paused', 'failed'].includes(t.status))
            .map(t => t.batchId);
          break;
        case 'pause':
          eligibleIds = this._tasks
            .filter(t => allSelectedIds.includes(t.batchId) && t.status === 'running')
            .map(t => t.batchId);
          break;
        case 'clear':
          eligibleIds = allSelectedIds;
          break;
        default:
          eligibleIds = [];
      }

      if (eligibleIds.length === 0) {
        this._logger.warn(`No eligible tasks in selection for action '${action}'. Aborting.`);
        return;
      }
      payload = { batchIds: eligibleIds };
    }

    this._logger.info(`Executing ${actionKey} action with payload:`, payload);
    this._sendMessage(this._createMessage(messageType, payload));

    if (action === 'clear' && hasSelection) {
      this._selectedBatchIds.clear();
      this._updateActionButtons();
    }
  }

  private _setAllActionButtonsDisabled(disabled: boolean): void {
    if (this._startBtn) this._startBtn.disabled = disabled;
    if (this._pauseBtn) this._pauseBtn.disabled = disabled;
    if (this._clearBtn) this._clearBtn.disabled = disabled;
  }

  private _updateActionButtons(): void {
    // 如果按钮不存在（例如在非开发模式下），则不执行任何操作。
    if (!this._startBtn) {
      return;
    }
    
    const hasSelection = this._selectedBatchIds.size > 0;
    
    (this._startBtn.querySelector('.btn-text') as HTMLSpanElement).textContent = hasSelection ? '开始任务' : '开始所有';
    (this._pauseBtn.querySelector('.btn-text') as HTMLSpanElement).textContent = hasSelection ? '暂停任务' : '暂停所有';
    (this._clearBtn.querySelector('.btn-text') as HTMLSpanElement).textContent = hasSelection ? '清除任务' : '清除所有';

    const tasksToConsider = hasSelection
      ? this._tasks.filter(t => this._selectedBatchIds.has(t.batchId))
      : this._tasks;

    // 如果考虑中的任务列表为空（例如，没有任务或没有选中的任务），则禁用开始和暂停按钮。
    if (tasksToConsider.length === 0) {
      this._startBtn.disabled = true;
      this._pauseBtn.disabled = true;
      this._clearBtn.disabled = this._tasks.length === 0;
      return;
    }

    // 只有当任务列表中至少有一个可启动的任务时，才启用"开始"按钮。
    const isAnyStartable = tasksToConsider.some(t => ['pending', 'paused', 'failed'].includes(t.status));
    this._startBtn.disabled = !isAnyStartable;

    // 只有当任务列表中至少有一个可暂停的任务时，才启用"暂停"按钮。
    const isAnyPausable = tasksToConsider.some(t => t.status === 'running');
    this._pauseBtn.disabled = !isAnyPausable;

    // 仅当完全没有任何任务时，才禁用"清除"按钮。
    this._clearBtn.disabled = this._tasks.length === 0;
  }

  private _renderTasks(): void {
    this._taskListDiv.innerHTML = ''; // Clear previous content
    if (this._tasks.length === 0) {
      this._taskListDiv.innerHTML = '<div class="no-tasks">暂无任务</div>';
      return;
    }

    this._tasks.forEach(task => {
      const taskElement = this._createTaskElement(task);
      this._taskListDiv.appendChild(taskElement);
    });
    
    // 重新绑定事件监听器
    this._bindCheckboxEvents();
    this._bindDownloadEvents();
    this._updateActionButtons();
  }

  private _bindCheckboxEvents(): void {
    // 清除旧的事件监听器(通过重新获取元素)
    const checkboxes = this._taskListDiv.querySelectorAll('.task-checkbox');
    
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', (event: Event) => {
        const target = event.target as HTMLInputElement;
        const batchId = target.dataset.batchId as string;
        
        if (target.checked) {
          this._selectedBatchIds.add(batchId);
          this._logger.debug(`Batch ${batchId} selected`);
        } else {
          this._selectedBatchIds.delete(batchId);
          this._logger.debug(`Batch ${batchId} deselected`);
        }
        
        this._updateActionButtons();
      });
    });
  }

  private _bindDownloadEvents(): void {
    const downloadBtns = this._taskListDiv.querySelectorAll('.download-btn');
    
    downloadBtns.forEach(btn => {
      btn.addEventListener('click', (event: Event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const target = event.target as HTMLElement;
        const downloadBtn = target.closest('.download-btn') as HTMLButtonElement;
        
        if (downloadBtn.disabled) return;
        
        const url = downloadBtn.dataset.url;
        const taskId = downloadBtn.dataset.taskId;
        
        if (url && taskId) {
          this._logger.info(`Requesting download for task ${taskId}, URL: ${url}`);
          this._sendMessage(this._createMessage(MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS, { taskId, url }));
        } else {
          this._logger.error('Failed to get task ID or URL for download');
        }
      });
    });
  }

  private _createTaskElement(task: BatchTask): HTMLElement {
    const details = document.createElement('details');
    details.dataset.batchId = task.batchId;

    if (this._openBatchIds.has(task.batchId)) {
      details.open = true;
    }

    const progressPercent = task.progress.total > 0
      ? (task.progress.processed / task.progress.total) * 100
      : 0;

    details.innerHTML = `
      <summary>
        <div class="task-selection">
          <input type="checkbox" class="task-checkbox" data-batch-id="${task.batchId}" ${this._selectedBatchIds.has(task.batchId) ? 'checked' : ''}>
        </div>
        <div class="task-info">
          <div class="task-info-header">
            <span class="task-id">批任务ID: ${task.batchId.substring(0, 8)}...</span>
            <span class="task-status ${task.status.toLowerCase()}">${task.status}</span>
          </div>
          <div class="progress-bar-container" title="${task.progress.processed} / ${task.progress.total} URLs">
            <div class="progress-bar" style="width: ${progressPercent}%"></div>
            <span class="progress-text">${task.progress.processed}/${task.progress.total}</span>
          </div>
        </div>
      </summary>
      <div class="task-details">
        <ul>
          ${this._renderUrlList(task.urlStatuses)}
        </ul>
      </div>
      <div class="collapse-trigger" title="收起">
        <i class="fas fa-chevron-up"></i>
      </div>
    `;
    return details;
  }
  
  private _renderUrlList(urlStatuses: UrlStatus[]): string {
    if (!urlStatuses || urlStatuses.length === 0) {
      return '<li>无URL详情</li>';
    }

    return urlStatuses.map(urlStatus => `
      <li class="url-item" data-url="${urlStatus.url}">
        <span class="url-text" title="${urlStatus.url}">${urlStatus.url}</span>
        <span class="url-status ${urlStatus.status.toLowerCase()}">${urlStatus.status}</span>
        <button 
          class="download-btn" 
          data-url="${urlStatus.url}" 
          data-task-id="${urlStatus.taskId}" 
          title="下载截图" 
          ${urlStatus.status !== 'succeeded' ? 'disabled' : ''}
        >
          <i class="fas fa-download"></i>
        </button>
      </li>
    `).join('');
  }

  private _updateStatusWhenConnected(isInitialized?: boolean): void {
    this._statusText.textContent = isInitialized ? '服务就绪' : '正在初始化...';
    this._statusDot.classList.remove('disconnected');
    this._statusDot.classList.add('connected');
  }

  private _updateStatusWhenDisconnected(): void {
    this._statusText.textContent = '服务离线';
    this._statusDot.classList.remove('connected');
    this._statusDot.classList.add('disconnected');
  }
}

// 初始化 Popup 实例
const popupInstance = new Popup();

// 导出实例以便在需要时访问
export { popupInstance };
