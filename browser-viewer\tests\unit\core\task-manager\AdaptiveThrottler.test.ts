/**
 * AdaptiveThrottler 单元测试
 * 测试自适应节流机制功能
 */

import { AdaptiveThrottler } from '@/core/task-manager/AdaptiveThrottler';
import type { PerformanceMetrics } from '@/shared/types';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('AdaptiveThrottler', () => {
  let throttler: AdaptiveThrottler;
  let mockAction1: ReturnType<typeof vi.fn>;
  let mockAction2: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    throttler = new AdaptiveThrottler();
    mockAction1 = vi.fn();
    mockAction2 = vi.fn();
  });

  describe('基础功能', () => {
    it('应该正确初始化', () => {
      expect(throttler).toBeDefined();
      expect(throttler['_currentLevel']).toBe(0);
    });

    it('应该注册节流级别动作', () => {
      throttler.on(1, mockAction1);
      throttler.on(1, mockAction2);
      
      expect(throttler['_actions'].get(1)).toHaveLength(2);
      expect(throttler['_actions'].get(1)).toContain(mockAction1);
      expect(throttler['_actions'].get(1)).toContain(mockAction2);
    });

    it('应该支持多个级别的动作', () => {
      throttler.on(0, mockAction1);
      throttler.on(1, mockAction2);
      
      expect(throttler['_actions'].get(0)).toHaveLength(1);
      expect(throttler['_actions'].get(1)).toHaveLength(1);
    });
  });

  describe('节流级别调整', () => {
    it('应该在CPU使用率高时提升节流级别', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.75, // 超过 LEVEL_1 阈值 (0.7)
        memory: 500, // 正常范围
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(1, mockAction1);
      throttler.adjust(metrics);

      expect(throttler['_currentLevel']).toBe(1);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });

    it('应该在内存使用率高时提升节流级别', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.3, // 正常范围
        memory: 1500, // 超过 LEVEL_1 阈值 (1024)
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(1, mockAction1);
      throttler.adjust(metrics);

      expect(throttler['_currentLevel']).toBe(1);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });

    it('应该在CPU和内存都高时提升到最高级别', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.9, // 超过 LEVEL_2 阈值 (0.85)
        memory: 3000, // 超过 LEVEL_2 阈值 (2048)
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(2, mockAction1);
      throttler.adjust(metrics);

      expect(throttler['_currentLevel']).toBe(2);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });

    it('应该在指标正常时保持基础级别', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.3, // 低于所有阈值
        memory: 400, // 低于所有阈值
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(0, mockAction1);
      throttler.adjust(metrics);

      expect(throttler['_currentLevel']).toBe(0);
      // 由于级别没有变化（从0到0），不会执行动作
      expect(mockAction1).not.toHaveBeenCalled();
    });
  });

  describe('动作执行', () => {
    it('应该执行对应级别的所有动作', () => {
      const mockAction3 = vi.fn();
      
      throttler.on(1, mockAction1);
      throttler.on(1, mockAction2);
      throttler.on(1, mockAction3);

      const metrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.adjust(metrics);

      expect(mockAction1).toHaveBeenCalledTimes(1);
      expect(mockAction2).toHaveBeenCalledTimes(1);
      expect(mockAction3).toHaveBeenCalledTimes(1);
    });

    it('应该只在级别变化时执行动作', () => {
      throttler.on(1, mockAction1);

      const metrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      // 第一次调整
      throttler.adjust(metrics);
      expect(mockAction1).toHaveBeenCalledTimes(1);

      // 相同级别，不应再次执行
      throttler.adjust(metrics);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });

    it('应该在级别下降时执行新级别的动作', () => {
      throttler.on(0, mockAction1);
      throttler.on(1, mockAction2);

      // 先提升到级别1
      const highMetrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };
      throttler.adjust(highMetrics);
      expect(mockAction2).toHaveBeenCalledTimes(1);

      // 降低到级别0
      const lowMetrics: PerformanceMetrics = {
        cpu: 0.3,
        memory: 400,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };
      throttler.adjust(lowMetrics);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });
  });

  describe('边界情况处理', () => {
    it('应该处理精确阈值边界', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.7, // 正好等于 LEVEL_1 阈值
        memory: 1024, // 正好等于 LEVEL_1 阈值
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(0, mockAction1);
      throttler.on(1, mockAction2);
      throttler.adjust(metrics);

      // 应该保持在级别0，因为是 > 比较
      expect(throttler['_currentLevel']).toBe(0);
      // 由于级别没有变化（从0到0），不会执行动作
      expect(mockAction1).not.toHaveBeenCalled();
      expect(mockAction2).not.toHaveBeenCalled();
    });

    it('应该处理超出阈值的边界情况', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.70001, // 刚好超过 LEVEL_1 阈值
        memory: 1024.1, // 刚好超过 LEVEL_1 阈值
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.on(1, mockAction1);
      throttler.adjust(metrics);

      expect(throttler['_currentLevel']).toBe(1);
      expect(mockAction1).toHaveBeenCalledTimes(1);
    });

    it('应该处理不存在动作的级别', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      // 不注册任何动作直接调整
      expect(() => throttler.adjust(metrics)).not.toThrow();
      expect(throttler['_currentLevel']).toBe(1);
    });
  });

  describe('性能优化', () => {
    it('应该快速处理频繁的指标更新', () => {
      const metrics: PerformanceMetrics = {
        cpu: 0.3,
        memory: 400,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      const startTime = performance.now();
      
      // 大量快速调用
      for (let i = 0; i < 1000; i++) {
        throttler.adjust(metrics);
      }
      
      const duration = performance.now() - startTime;
      expect(duration).toBeLessThan(50); // 应该在50ms内完成
    });

    it('应该有效管理内存中的动作映射', () => {
      // 注册大量动作
      for (let level = 0; level <= 10; level++) {
        for (let i = 0; i < 100; i++) {
          throttler.on(level, vi.fn());
        }
      }

      expect(throttler['_actions'].size).toBe(11);
      expect(throttler['_actions'].get(5)).toHaveLength(100);
    });
  });

  describe('日志和调试', () => {
    it('应该记录级别变化', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const metrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.adjust(metrics);

      // 检查是否有级别变化日志
      expect(throttler['_currentLevel']).toBe(1);
      
      consoleSpy.mockRestore();
    });

    it('应该记录动作执行信息', () => {
      const consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => {});
      
      throttler.on(1, mockAction1);
      throttler.on(1, mockAction2);

      const metrics: PerformanceMetrics = {
        cpu: 0.75,
        memory: 500,
        network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now()
      };

      throttler.adjust(metrics);

      consoleSpy.mockRestore();
    });
  });

  describe('实际使用场景', () => {
    it('应该模拟CPU密集型任务的节流', () => {
      let taskDelay = 0;
      let maxConcurrency = 10;

      throttler.on(0, () => {
        taskDelay = 0;
        maxConcurrency = 10;
      });

      throttler.on(1, () => {
        taskDelay = 100;
        maxConcurrency = 5;
      });

      throttler.on(2, () => {
        taskDelay = 500;
        maxConcurrency = 2;
      });

      // 模拟正常负载
      throttler.adjust({ cpu: 0.3, memory: 400, network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now() });
      expect(taskDelay).toBe(0);
      expect(maxConcurrency).toBe(10);

      // 模拟中等负载
      throttler.adjust({ cpu: 0.75, memory: 500, network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now() });
      expect(taskDelay).toBe(100);
      expect(maxConcurrency).toBe(5);

      // 模拟高负载
      throttler.adjust({ cpu: 0.9, memory: 3000, network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now() });
      expect(taskDelay).toBe(500);
      expect(maxConcurrency).toBe(2);
    });

    it('应该模拟内存压力下的缓存清理', () => {
      let cacheCleared = false;
      let compressionEnabled = false;

      throttler.on(1, () => {
        compressionEnabled = true;
      });

      throttler.on(2, () => {
        cacheCleared = true;
      });

      // 内存压力触发缓存清理
      throttler.adjust({ cpu: 0.3, memory: 2500, network: 0,
        fps: 60,
        latency: 10,
        timestamp: Date.now() });
      
      expect(compressionEnabled).toBe(false); // 跳过级别1直接到级别2
      expect(cacheCleared).toBe(true);
    });
  });
});