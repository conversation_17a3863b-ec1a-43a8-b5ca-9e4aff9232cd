import webExtension from '@samrum/vite-plugin-web-extension';
import vue from '@vitejs/plugin-vue';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';
import { defineConfig, type UserConfig } from 'vite';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import { manifest } from './manifest.config';
import packageJson from './package.json' with { type: 'json' };

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 从 manifest.json 中提取版本号，或从 package.json 中获取
const version = manifest.version ?? packageJson.version;

export default defineConfig(({ mode }: { mode: string }) => {
  const isDev = mode === 'development';
  
  console.log(`\n🚀 Building in ${mode} mode`);
  console.log(`📦 Version: ${version}`);
  console.log(`📦 SourceMap: ${isDev ? 'enabled' : 'disabled'}`);
  console.log(`🔧 Minify: ${!isDev ? 'enabled' : 'disabled'}`);
  
  const config: UserConfig = {
    plugins: [
      vue(),
      webExtension({
        manifest: manifest as chrome.runtime.ManifestV3,
      }),
      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/modern-screenshot/dist/worker.js',
            dest: 'assets/js',
          },
        ],
      }),
      // tailwindcss 作为 PostCSS 插件应该在 css.postcss.plugins 中配置
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@/background': resolve(__dirname, 'src/background'),
        '@/content-scripts': resolve(__dirname, 'src/content-scripts'),
        '@/core': resolve(__dirname, 'src/core'),
        '@/pages': resolve(__dirname, 'src/pages'),
        '@/services': resolve(__dirname, 'src/services'),
        '@/shared': resolve(__dirname, 'src/shared'),
        '@/ui': resolve(__dirname, 'src/ui'),
        '@/workers': resolve(__dirname, 'src/workers'),
      },
    },
    build: {
      sourcemap: isDev ? 'inline' : false,
      minify: isDev ? false : 'terser',
      reportCompressedSize: !isDev,
      
      rollupOptions: {
        input: {
          'popup/index': resolve(__dirname, 'popup.html'),
          'options/index': resolve(__dirname, 'options.html'),
          'offscreen/index': resolve(__dirname, 'offscreen.html'),
          'content-script/index': resolve(__dirname, 'src/content-scripts/main.ts'),
        },
        output: {
          entryFileNames: '[name].js',
          chunkFileNames: 'assets/js/[name].js',
          assetFileNames: 'assets/[name].[ext]',
          // 代码分割优化
          manualChunks: {
            'vendor': ['rxjs', 'webextension-polyfill'],
            'behavior': ['@/core/behavior-engine/BehaviorEngine', '@/core/behavior-engine/HumanBehaviorSimulator'],
            'capture': ['@/core/capture-system/CaptureSystem', '@/core/capture-system/ImageProcessingPipeline'],
            'task-manager': ['@/core/task-manager/TaskScheduler', '@/core/task-manager/Task'],
          },
        },
        treeshake: !isDev
      },

      outDir: resolve(__dirname, 'dist'),
      emptyOutDir: true,
      target: 'esnext',
      
      chunkSizeWarningLimit: isDev ? 1000 : 500,
    },
    define: {
      __DEV_MODE__: isDev,
      __PROD__: !isDev,
      __VERSION__: JSON.stringify(version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },
    
    server: {
      port: 5173,
      host: true,
      open: false,
      hmr: {
        port: 5174,
      },
      fs: {
        allow: ['..']
      }
    },
    
    optimizeDeps: {
      include: ['vue', 'rxjs'],
      exclude: ['@types/chrome', '@types/webextension-polyfill']
    },
    
    esbuild: {
      drop: isDev ? [] : ['console', 'debugger'],
      legalComments: 'none'
    },

    worker: {
      format: 'es',
      rollupOptions: {
        output: {
          entryFileNames: 'assets/[name].js',
          chunkFileNames: 'assets/[name].js',
          assetFileNames: 'assets/[name].[ext]',
        },
      },
    },

    test: {
      globals: true,
    },
  };

  return config;
});