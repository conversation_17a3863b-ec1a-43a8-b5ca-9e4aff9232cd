"""Initial migration - Create users, tasks, and data tables

Revision ID: 0001
Revises:
Create Date: 2025-07-14 12:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0001"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table(
        "users",
        sa.Column("id", sa.String(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("is_deleted", sa.<PERSON>an(), nullable=False),
        sa.Column("username", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=100), nullable=False),
        sa.Column("full_name", sa.String(length=100), nullable=True),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("is_admin", sa.<PERSON>olean(), nullable=False),
        sa.Column("last_login", sa.DateTime(), nullable=True),
        sa.Column("avatar_url", sa.String(length=255), nullable=True),
        sa.Column("bio", sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_index(op.f("ix_users_username"), "users", ["username"], unique=True)

    # Create tasks table
    op.create_table(
        "tasks",
        sa.Column("id", sa.String(length=36), nullable=False),
        sa.Column("batch_id", sa.String(length=36), nullable=True),
        sa.Column("user_id", sa.String(length=36), nullable=False),
        sa.Column("config", sa.JSON(), nullable=False),
        sa.Column("urls", sa.JSON(), nullable=False),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING",
                "RUNNING",
                "PAUSED",
                "COMPLETED",
                "FAILED",
                "CANCELLED",
                name="taskstatus",
            ),
            nullable=True,
        ),
        sa.Column(
            "priority",
            sa.Enum("LOW", "NORMAL", "HIGH", "URGENT", name="taskpriority"),
            nullable=True,
        ),
        sa.Column("total_urls", sa.Integer(), nullable=True),
        sa.Column("processed_urls", sa.Integer(), nullable=True),
        sa.Column("succeeded_urls", sa.Integer(), nullable=True),
        sa.Column("failed_urls", sa.Integer(), nullable=True),
        sa.Column("progress_percentage", sa.Float(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("assigned_worker", sa.String(length=100), nullable=True),
        sa.Column("execution_time", sa.Float(), nullable=True),
        sa.Column("retry_count", sa.Integer(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("memory_usage", sa.Float(), nullable=True),
        sa.Column("cpu_usage", sa.Float(), nullable=True),
        sa.Column("metadata_info", sa.JSON(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_tasks_batch_id"), "tasks", ["batch_id"], unique=False)
    op.create_index(op.f("ix_tasks_created_at"), "tasks", ["created_at"], unique=False)
    op.create_index(op.f("ix_tasks_id"), "tasks", ["id"], unique=False)
    op.create_index(op.f("ix_tasks_status"), "tasks", ["status"], unique=False)
    op.create_index(op.f("ix_tasks_user_id"), "tasks", ["user_id"], unique=False)

    # Create task_results table
    op.create_table(
        "task_results",
        sa.Column("id", sa.String(length=36), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=False),
        sa.Column("url", sa.Text(), nullable=False),
        sa.Column("extracted_data", sa.JSON(), nullable=True),
        sa.Column("processed_data", sa.JSON(), nullable=True),
        sa.Column("screenshots", sa.JSON(), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=False),
        sa.Column("processing_time", sa.Float(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["tasks.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_task_results_id"), "task_results", ["id"], unique=False)
    op.create_index(
        op.f("ix_task_results_task_id"), "task_results", ["task_id"], unique=False
    )

    # Create task_logs table
    op.create_table(
        "task_logs",
        sa.Column("id", sa.String(length=36), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=False),
        sa.Column("level", sa.String(length=10), nullable=False),
        sa.Column("message", sa.Text(), nullable=False),
        sa.Column("details", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["tasks.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_task_logs_created_at"), "task_logs", ["created_at"], unique=False
    )
    op.create_index(op.f("ix_task_logs_id"), "task_logs", ["id"], unique=False)
    op.create_index(
        op.f("ix_task_logs_task_id"), "task_logs", ["task_id"], unique=False
    )

    # Create data table
    op.create_table(
        "data",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "data_type",
            sa.Enum(
                "TEXT",
                "IMAGE",
                "TABLE",
                "FORM",
                "LINK",
                "DOCUMENT",
                "SCREENSHOT",
                "OTHER",
                name="datatype",
            ),
            nullable=False,
        ),
        sa.Column(
            "status",
            sa.Enum(
                "RAW",
                "PROCESSING",
                "PROCESSED",
                "FAILED",
                "ARCHIVED",
                name="datastatus",
            ),
            nullable=True,
        ),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("task_id", sa.String(), nullable=True),
        sa.Column("raw_data", sa.JSON(), nullable=True),
        sa.Column("processed_data", sa.JSON(), nullable=True),
        sa.Column("metadata", sa.JSON(), nullable=True),
        sa.Column("file_path", sa.String(length=500), nullable=True),
        sa.Column("file_size", sa.Integer(), nullable=True),
        sa.Column("file_type", sa.String(length=50), nullable=True),
        sa.Column("checksum", sa.String(length=64), nullable=True),
        sa.Column("processing_time", sa.Float(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("access_count", sa.Integer(), nullable=True),
        sa.Column("last_accessed", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["tasks.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_data_data_type"), "data", ["data_type"], unique=False)
    op.create_index(op.f("ix_data_status"), "data", ["status"], unique=False)
    op.create_index(op.f("ix_data_task_id"), "data", ["task_id"], unique=False)
    op.create_index(op.f("ix_data_user_id"), "data", ["user_id"], unique=False)


def downgrade() -> None:
    op.drop_index(op.f("ix_data_user_id"), table_name="data")
    op.drop_index(op.f("ix_data_task_id"), table_name="data")
    op.drop_index(op.f("ix_data_status"), table_name="data")
    op.drop_index(op.f("ix_data_data_type"), table_name="data")
    op.drop_table("data")
    op.drop_index(op.f("ix_task_logs_task_id"), table_name="task_logs")
    op.drop_index(op.f("ix_task_logs_id"), table_name="task_logs")
    op.drop_index(op.f("ix_task_logs_created_at"), table_name="task_logs")
    op.drop_table("task_logs")
    op.drop_index(op.f("ix_task_results_task_id"), table_name="task_results")
    op.drop_index(op.f("ix_task_results_id"), table_name="task_results")
    op.drop_table("task_results")
    op.drop_index(op.f("ix_tasks_user_id"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_status"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_id"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_created_at"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_batch_id"), table_name="tasks")
    op.drop_table("tasks")
    op.drop_index(op.f("ix_users_username"), table_name="users")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_table("users")

    # Drop enums
    op.execute("DROP TYPE IF EXISTS taskstatus")
    op.execute("DROP TYPE IF EXISTS taskpriority")
    op.execute("DROP TYPE IF EXISTS datatype")
    op.execute("DROP TYPE IF EXISTS datastatus")
