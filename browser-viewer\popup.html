<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数懒孪生代理</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="src/ui/main.css">
  <style>
    :root {
      --primary-color: #007AFF;
      --primary-hover: #0051D0;
      --success-color: #30D158;
      --error-color: #FF453A;
      --warn-color: #FF9F0A;
      --paused-color: #8E8E93;
      
      /* Apple-style color palette */
      --bg-main: #F2F2F7;
      --bg-header: rgba(255, 255, 255, 0.95);
      --bg-content: rgba(255, 255, 255, 0.8);
      --bg-input: rgba(255, 255, 255, 0.9);
      --bg-card: rgba(255, 255, 255, 0.85);
      --text-primary: #1D1D1F;
      --text-secondary: #6D6D70;
      --text-tertiary: #8E8E93;
      --border-color: rgba(0, 0, 0, 0.06);
      --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
      --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
      --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 400px;
      min-height: 550px;
      font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #F2F2F7 0%, #E5E5EA 100%);
      color: var(--text-primary);
      display: flex;
      flex-direction: column;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .header {
      position: relative;
      padding: 1rem 1.25rem;
      background: var(--bg-header);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--border-color);
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--shadow-light);
    }
    
    .title-container {
      display: flex;
      align-items: center;
      gap: 0.875rem;
    }
    
    .header h1 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      letter-spacing: -0.02em;
      color: var(--text-primary);
    }
    
    #settingsLink {
      position: absolute;
      top: 50%;
      right: 1.25rem;
      transform: translateY(-50%);
      color: var(--text-secondary);
      text-decoration: none;
      font-size: 1.125rem;
      transition: all 0.2s ease;
      padding: 0.375rem;
      border-radius: 6px;
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.75rem;
      color: var(--text-secondary);
      font-weight: 500;
    }
    
    .status-dot {
      width: 7px;
      height: 7px;
      border-radius: 50%;
      background-color: var(--paused-color);
      transition: all 0.3s ease;
    }
    
    .status-dot.connected {
      background-color: var(--success-color);
      box-shadow: 0 0 6px rgba(48, 209, 88, 0.4);
    }
    
    .status-dot.disconnected {
      background-color: var(--error-color);
      box-shadow: 0 0 6px rgba(255, 69, 58, 0.4);
    }

     #settingsLink:hover {
      color: var(--primary-color);
      background-color: rgba(0, 122, 255, 0.1);
      transform: translateY(-50%) scale(1.05);
     }
    
    .content {
      padding: 1rem;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      gap: 0.875rem;
      overflow: hidden;
    }

    /* Dev Mode Section */
    #devModeSection {
      background: var(--bg-content);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      padding: 1rem;
      border-radius: 12px;
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-card);
    }

    #devModeSection label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.01em;
    }

    #devUrls {
      width: 100%;
      height: 72px;
      padding: 0.625rem;
      border-radius: 8px;
      border: 1px solid var(--border-color);
      background: var(--bg-input);
      color: var(--text-primary);
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.75rem;
      resize: vertical;
      margin-bottom: 0.75rem;
      transition: all 0.2s ease;
      outline: none;
    }

    #devUrls:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
    }

    #createTaskBtn {
      width: 100%;
    }

    /* Main Controls */
    .controls {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 0.625rem;
    }
    
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.375rem;
      padding: 0.75rem 0.875rem;
      background: var(--bg-card);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      color: var(--text-primary);
      cursor: pointer;
      font-size: 0.75rem;
      font-weight: 600;
      transition: all 0.2s ease;
      outline: none;
      letter-spacing: -0.01em;
      box-shadow: var(--shadow-card);
    }
    
    .btn:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
      box-shadow: var(--shadow-medium);
    }

    .btn:active {
      transform: translateY(0);
      transition: transform 0.1s ease;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: var(--bg-card);
      transform: none;
      box-shadow: var(--shadow-card);
    }
    
    .btn.primary {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      border-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }
     .btn.primary:hover {
       background: linear-gradient(135deg, var(--primary-hover) 0%, #003d82 100%);
       box-shadow: 0 4px 16px rgba(0, 122, 255, 0.4);
       transform: translateY(-2px);
     }

    /* Task List */
    .task-list-container {
      flex-grow: 1;
      overflow-y: auto;
      background: var(--bg-content);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 12px;
      padding: 0.75rem;
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-card);
    }

    #taskList details {
      background: var(--bg-card);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 10px;
      margin-bottom: 0.625rem;
      border: 1px solid var(--border-color);
      transition: all 0.2s ease;
      box-shadow: var(--shadow-light);
    }
    
    #taskList details[open] {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
    }

    #taskList details summary {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      cursor: pointer;
      list-style: none;
      border-radius: 10px;
      transition: background-color 0.2s ease;
    }

    #taskList details summary:hover {
      background: rgba(255, 255, 255, 0.7);
    }

    #taskList details summary::-webkit-details-marker {
      display: none;
    }

    .task-selection {
      margin-right: 0.75rem;
    }
    
    .task-info {
      flex-grow: 1;
    }

    details[open] > summary {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    .collapse-trigger {
      display: none;
      padding: 0.375rem;
      text-align: center;
      cursor: pointer;
      color: var(--text-tertiary);
      border-top: 1px solid var(--border-color);
    }
    details[open] .collapse-trigger {
      display: block;
    }
    .collapse-trigger:hover {
      background-color: rgba(0, 0, 0, 0.05);
      color: var(--primary-color);
    }

    .task-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .task-id {
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.6875rem;
      color: var(--text-tertiary);
      font-weight: 500;
    }
    
    .task-status {
      font-size: 0.6875rem;
      font-weight: 600;
      padding: 0.1875rem 0.5rem;
      border-radius: 6px;
      text-transform: capitalize;
      letter-spacing: 0.02em;
    }
    .task-status.running { background-color: var(--primary-color); color: white; box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3); }
    .task-status.completed { background-color: var(--success-color); color: white; box-shadow: 0 1px 3px rgba(48, 209, 88, 0.3); }
    .task-status.failed { background-color: var(--error-color); color: white; box-shadow: 0 1px 3px rgba(255, 69, 58, 0.3); }
    .task-status.paused { background-color: var(--paused-color); color: white; box-shadow: 0 1px 3px rgba(142, 142, 147, 0.3); }
    .task-status.pending { background-color: var(--warn-color); color: white; box-shadow: 0 1px 3px rgba(255, 159, 10, 0.3); }
    

    .progress-bar-container {
      width: 100%;
      height: 10px;
      background-color: rgba(142, 142, 147, 0.2);
      border-radius: 7px;
      overflow: hidden;
      position: relative;
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      transition: width 0.3s ease;
      border-radius: 7px;
    }

    .progress-text {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 0.6875rem;
      color: white;
      font-weight: 600;
      text-shadow: 0 0 2px rgba(0,0,0,0.5);
    }

    /* Task Details */
    .task-details {
      padding: 0 0.75rem 0.75rem 0.75rem;
      border-top: 1px solid var(--border-color);
      margin-top: 0.5rem;
      padding-top: 0.75rem;
    }

    .task-details ul {
      list-style: none;
      max-height: 140px;
      overflow-y: auto;
    }

    .url-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      font-size: 0.75rem;
      border-bottom: 1px solid var(--border-color);
    }
    .url-item:last-child {
      border-bottom: none;
    }

    .url-text {
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 0.75rem;
      color: var(--text-primary);
    }

    .url-status {
      font-size: 0.625rem;
      padding: 0.1875rem 0.375rem;
      border-radius: 4px;
      margin-right: 0.625rem;
      color: white;
      font-weight: 600;
      letter-spacing: 0.02em;
    }
    .url-status.pending { background-color: var(--paused-color); box-shadow: 0 1px 3px rgba(142, 142, 147, 0.3); }
    .url-status.running { background-color: var(--primary-color); color: white; box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3); }
    .url-status.succeeded { background-color: var(--success-color); box-shadow: 0 1px 3px rgba(48, 209, 88, 0.3); }
    .url-status.failed { background-color: var(--error-color); box-shadow: 0 1px 3px rgba(255, 69, 58, 0.3); }

    .download-btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.625rem;
      font-weight: 600;
      background: transparent;
      border: 1px solid var(--primary-color);
      color: var(--primary-color);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      letter-spacing: 0.01em;
    }
    .download-btn:disabled {
      border-color: var(--paused-color);
      color: var(--paused-color);
      cursor: not-allowed;
    }
    .download-btn:hover:not(:disabled) {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }
    
    .no-tasks {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80px;
      color: var(--text-tertiary);
      font-style: italic;
      font-size: 0.875rem;
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 3px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--text-tertiary);
      border-radius: 1.5px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--text-secondary);
    }

  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="title-container">
        <h1>数懒智能</h1>
        <div id="statusIndicator" class="status-indicator" title="服务状态">
          <div id="statusDot" class="status-dot"></div>
          <span id="statusText">未连接</span>
        </div>
      </div>
      <a href="options.html" target="_blank" id="settingsLink" title="打开设置">
        <i class="fas fa-cog"></i>
      </a>
    </header>

    <main class="content">
      <!-- Dev Mode Section -->
      <section id="devModeSection">
        <label for="devUrls">目标URL</label>
        <textarea id="devUrls" placeholder="https://example.com/page1&#10;https://example.com/page2"></textarea>
        <button id="createTaskBtn" class="btn primary">
          <i class="fas fa-plus"></i> 创建批处理任务
        </button>
      </section>

      <!-- Main Controls -->
      <section class="controls">
        <button id="startBtn" class="btn"><i class="fas fa-play"></i> <span class="btn-text">开始所有</span></button>
        <button id="pauseBtn" class="btn"><i class="fas fa-pause"></i> <span class="btn-text">暂停所有</span></button>
        <button id="clearBtn" class="btn"><i class="fas fa-trash"></i> <span class="btn-text">清除所有</span></button>
      </section>

      <!-- Task List -->
      <div class="task-list-container">
        <div id="taskList">
          <!-- Task items will be dynamically inserted here -->
          <div class="no-tasks">暂无任务</div>
        </div>
      </div>
    </main>
  </div>
  <script type="module" src="/src/ui/popup/popup.ts"></script>
</body>
</html>