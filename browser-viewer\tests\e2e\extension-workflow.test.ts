/**
 * 浏览器插件端到端测试
 * 测试完整的插件工作流程
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TEST_MESSAGE_TYPES, TEST_TIMEOUTS, TEST_URLS } from '../fixtures/test-constants';
import { TestUtils } from '../utils/test-helpers';

// E2E测试需要模拟完整的浏览器环境
interface ExtensionContext {
  runtime: {
    id: string;
    onMessage: {
      addListener: ReturnType<typeof vi.fn>;
      removeListener: ReturnType<typeof vi.fn>;
    };
    sendMessage: ReturnType<typeof vi.fn>;
    connect: ReturnType<typeof vi.fn>;
  };
  tabs: {
    create: ReturnType<typeof vi.fn>;
    update: ReturnType<typeof vi.fn>;
    query: ReturnType<typeof vi.fn>;
  };
  storage: {
    local: {
      get: ReturnType<typeof vi.fn>;
      set: ReturnType<typeof vi.fn>;
    };
  };
}

interface MockBackgroundScript {
  serviceWorker: {
    taskScheduler: {
      createTask: ReturnType<typeof vi.fn>;
      getActiveTasks: ReturnType<typeof vi.fn>;
      pauseTask: ReturnType<typeof vi.fn>;
      resumeTask: ReturnType<typeof vi.fn>;
      cancelTask: ReturnType<typeof vi.fn>;
    };
    messageHandler: {
      handleMessage: ReturnType<typeof vi.fn>;
    };
  };
}

interface MockContentScript {
  behaviorEngine: {
    simulateScroll: ReturnType<typeof vi.fn>;
    simulateClick: ReturnType<typeof vi.fn>;
    simulateHumanBehavior: ReturnType<typeof vi.fn>;
  };
  captureSystem: {
    captureViewport: ReturnType<typeof vi.fn>;
    processCapture: ReturnType<typeof vi.fn>;
  };
}

interface MockPopup {
  ui: {
    updateTaskList: ReturnType<typeof vi.fn>;
    showProgress: ReturnType<typeof vi.fn>;
    showError: ReturnType<typeof vi.fn>;
    showSuccess: ReturnType<typeof vi.fn>;
  };
  taskManager: {
    createNewTask: ReturnType<typeof vi.fn>;
    startTask: ReturnType<typeof vi.fn>;
    pauseTask: ReturnType<typeof vi.fn>;
  };
}

describe('插件端到端工作流程测试', () => {
  let extensionContext: ExtensionContext;
  let mockBackgroundScript: MockBackgroundScript;
  let mockContentScript: MockContentScript;
  let mockPopup: MockPopup;
  let mockTabs: any[];

  beforeEach(() => {
    // 初始化模拟标签页数组
    mockTabs = [{ id: 1, url: 'about:blank' }];
    // 设置完整的插件环境模拟
    extensionContext = {
      runtime: {
        id: 'test-extension-id',
        onMessage: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
        onInstalled: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
        sendMessage: vi.fn().mockResolvedValue({ success: true }),
        connect: vi.fn().mockReturnValue({
          postMessage: vi.fn(),
          onMessage: { addListener: vi.fn() },
          onDisconnect: { addListener: vi.fn() },
        }),
      } as any,
      tabs: {
        create: vi.fn().mockImplementation((createProperties) => {
          const newTab = { id: Math.floor(Math.random() * 1000) + 2, url: createProperties.url };
          mockTabs.push(newTab);
          return Promise.resolve(newTab);
        }),
        update: vi.fn().mockResolvedValue({ id: 1 }),
        query: vi.fn().mockImplementation(() => Promise.resolve([...mockTabs])),
        remove: vi.fn().mockImplementation((tabId) => {
          const index = mockTabs.findIndex(tab => tab.id === tabId);
          if (index !== -1) {
            mockTabs.splice(index, 1);
          }
          return Promise.resolve();
        }),
      } as any,
      storage: {
        local: {
          get: vi.fn().mockResolvedValue({}),
          set: vi.fn().mockResolvedValue(undefined),
        },
      },
    };

    // 模拟Background Script
    mockBackgroundScript = {
      serviceWorker: {
        taskScheduler: {
          createTask: vi.fn().mockResolvedValue('task-123'),
          getActiveTasks: vi.fn().mockReturnValue([]),
          pauseTask: vi.fn().mockResolvedValue(undefined),
          resumeTask: vi.fn().mockResolvedValue(undefined),
          cancelTask: vi.fn().mockResolvedValue(undefined),
        },
        messageHandler: {
          handleMessage: vi.fn().mockResolvedValue({ success: true }),
        },
      },
    };

    // 模拟Content Script
    mockContentScript = {
      behaviorEngine: {
        simulateScroll: vi.fn().mockResolvedValue(undefined),
        simulateClick: vi.fn().mockResolvedValue(undefined),
        simulateHumanBehavior: vi.fn().mockResolvedValue(undefined),
      },
      captureSystem: {
        captureViewport: vi.fn().mockResolvedValue(TestUtils.createMockArrayBuffer()),
        processCapture: vi.fn().mockResolvedValue('Captured content'),
      },
    };

    // 模拟Popup UI
    mockPopup = {
      ui: {
        updateTaskList: vi.fn(),
        showProgress: vi.fn(),
        showError: vi.fn(),
        showSuccess: vi.fn(),
      },
      taskManager: {
        createNewTask: vi.fn().mockResolvedValue('task-123'),
        startTask: vi.fn().mockResolvedValue(undefined),
        pauseTask: vi.fn().mockResolvedValue(undefined),
      },
    };

    // 设置全局chrome对象
    Object.assign(globalThis.chrome, extensionContext);
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('任务完整生命周期', () => {
    it('应该能创建、启动、暂停、恢复和完成任务', async () => {
      // 1. 通过Popup创建任务
      const taskConfig = TestUtils.createMockTaskConfig({
        urls: [TEST_URLS.VALID, TEST_URLS.HTTPS],
        behaviorProfile: 'normal',
        outputFormat: 'text',
      });

      const taskId = await mockPopup.taskManager.createNewTask(taskConfig) as string;
      expect(taskId).toBeDefined();
      expect(mockPopup.taskManager.createNewTask).toHaveBeenCalledWith(taskConfig);

      // 2. 启动任务
      await mockPopup.taskManager.startTask(taskId);
      expect(mockPopup.taskManager.startTask).toHaveBeenCalledWith(taskId);

      // 3. 验证任务在调度器中活跃
      const activeTasks = mockBackgroundScript.serviceWorker.taskScheduler.getActiveTasks();
      expect(activeTasks).toBeDefined();

      // 4. 暂停任务
      await mockPopup.taskManager.pauseTask(taskId);
      expect(mockPopup.taskManager.pauseTask).toHaveBeenCalledWith(taskId);

      // 5. 恢复任务
      await mockBackgroundScript.serviceWorker.taskScheduler.resumeTask(taskId);
      expect(mockBackgroundScript.serviceWorker.taskScheduler.resumeTask)
        .toHaveBeenCalledWith(taskId);

      // 6. 取消任务
      await mockBackgroundScript.serviceWorker.taskScheduler.cancelTask(taskId);
      expect(mockBackgroundScript.serviceWorker.taskScheduler.cancelTask)
        .toHaveBeenCalledWith(taskId);
    });

    it('应该处理任务失败和重试', async () => {
      const taskConfig = TestUtils.createMockTaskConfig({
        urls: [TEST_URLS.INVALID],
        retryCount: 3,
      });

      // 模拟任务失败
      mockBackgroundScript.serviceWorker.taskScheduler.createTask
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce('task-retry-123');

      // 第一次尝试失败
      try {
        await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        if (error instanceof Error) {
          expect(error.message).toBe('Network error');
        }
      }

      // 重试成功
      const taskId = await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);
      expect(taskId).toBe('task-retry-123');
    });
  });

  describe('无头模式工作流程', () => {
    it('应该在无头模式下成功处理任务', async () => {
      const taskConfig = TestUtils.createMockTaskConfig({
        urls: [TEST_URLS.VALID],
        renderMode: 'headless',
      });

      // 创建无头任务
      await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);

      // 模拟Offscreen文档处理
      const offscreenProcessor = {
        executeHeadlessTask: vi.fn().mockResolvedValue({
          success: true,
          results: ['Processed content from headless mode'],
        }),
      };

      await offscreenProcessor.executeHeadlessTask(taskConfig);
      expect(offscreenProcessor.executeHeadlessTask).toHaveBeenCalledWith(taskConfig);
    });

    it('应该在无头模式下处理多个URL', async () => {
      const urls = [TEST_URLS.VALID, TEST_URLS.HTTPS, TEST_URLS.LOCAL];

      const processedUrls: string[] = [];
      
      // 模拟处理每个URL
      for (const url of urls) {
        try {
          // 模拟iframe加载
          await TestUtils.sleep(100); // 模拟加载时间
          processedUrls.push(url);
        } catch (error) {
          console.warn('Failed to process URL:', url, error);
        }
      }

      expect(processedUrls).toHaveLength(urls.length);
      expect(processedUrls).toEqual(urls);
    });
  });

  describe('可视化模式工作流程', () => {
    it('应该在可视化模式下创建标签页并处理任务', async () => {
      const taskConfig = TestUtils.createMockTaskConfig({
        urls: [TEST_URLS.VALID],
        renderMode: 'visual',
      }) as { urls: string[]; renderMode: string; };

      // 创建可视化任务
      await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);

      // 模拟创建可视化标签页
      const tab = await chrome.tabs.create({ url: TEST_URLS.VALID, active: false });
      expect(tab.id).toBeDefined();

      // 模拟注入内容脚本
      if (chrome.scripting && tab.id) {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content-script.js'],
        });
      }

      // 模拟行为引擎工作
      await mockContentScript.behaviorEngine.simulateHumanBehavior();
      expect(mockContentScript.behaviorEngine.simulateHumanBehavior).toHaveBeenCalled();
    });

    it('应该在可视化模式下捕获和处理页面内容', async () => {
      const tabId = 1;

      // 模拟页面滚动
      await mockContentScript.behaviorEngine.simulateScroll(tabId, {
        distance: 1000,
        speed: 'normal',
      });

      // 模拟视口捕获
      const captureData = await mockContentScript.captureSystem.captureViewport() as ArrayBuffer;
      expect(captureData).toBeInstanceOf(ArrayBuffer);

      // 模拟内容处理
      const processedContent = await mockContentScript.captureSystem.processCapture(captureData) as string;
      expect(processedContent).toBe('Captured content');
    });
  });

  describe('异常处理和恢复', () => {
    it('应该检测并处理验证码', async () => {
      const exceptionService = {
        detectCaptcha: vi.fn().mockResolvedValue(true),
        pauseTask: vi.fn().mockResolvedValue(undefined),
        notifyUser: vi.fn().mockResolvedValue(undefined),
      };

      // 模拟检测到验证码
      const hasCaptcha = await exceptionService.detectCaptcha() as boolean;
      expect(hasCaptcha).toBe(true);

      if (hasCaptcha) {
        // 暂停任务
        await exceptionService.pauseTask();
        expect(exceptionService.pauseTask).toHaveBeenCalled();

        // 通知用户
        await exceptionService.notifyUser();
        expect(exceptionService.notifyUser).toHaveBeenCalled();
      }
    });

    it('应该检测并处理重定向循环', async () => {
      const navigationHistory = [
        { url: TEST_URLS.VALID, timestamp: Date.now() },
        { url: TEST_URLS.HTTPS, timestamp: Date.now() + 1000 },
        { url: TEST_URLS.VALID, timestamp: Date.now() + 2000 }, // 重复
      ];

      const detectLoopLogic = (history: Array<{url: string; timestamp: number}>) => {
        const urls = history.map((h) => h.url);
        const uniqueUrls = new Set(urls);
        return uniqueUrls.size < urls.length;
      };

      const exceptionService = {
        detectRedirectLoop: vi.fn().mockImplementation(detectLoopLogic),
        createSnapshot: vi.fn().mockResolvedValue('snapshot-123'),
        restoreSnapshot: vi.fn().mockResolvedValue(undefined),
      };

      const hasLoop = await exceptionService.detectRedirectLoop(navigationHistory) as boolean;
      expect(hasLoop).toBe(true);

      if (hasLoop) {
        const snapshotId = await exceptionService.createSnapshot() as string;
        await exceptionService.restoreSnapshot(snapshotId);
        
        expect(exceptionService.createSnapshot).toHaveBeenCalled();
        expect(exceptionService.restoreSnapshot).toHaveBeenCalledWith(snapshotId);
      }
    });
  });

  describe('用户界面交互', () => {
    it('应该更新Popup中的任务状态', async () => {
      const taskStatus = {
        id: 'task-123',
        status: 'running',
        progress: 0.6,
        currentUrl: TEST_URLS.VALID,
        processedUrls: 3,
        totalUrls: 5,
      };

      await mockPopup.ui.updateTaskList([taskStatus]);
      expect(mockPopup.ui.updateTaskList).toHaveBeenCalledWith([taskStatus]);

      await mockPopup.ui.showProgress(taskStatus.progress);
      expect(mockPopup.ui.showProgress).toHaveBeenCalledWith(taskStatus.progress);
    });

    it('应该处理用户的任务控制操作', async () => {
      const taskId = 'task-123';

      // 用户点击暂停按钮
      await mockPopup.taskManager.pauseTask(taskId);
      expect(mockPopup.taskManager.pauseTask).toHaveBeenCalledWith(taskId);

      // 用户点击开始按钮
      await mockPopup.taskManager.startTask(taskId);
      expect(mockPopup.taskManager.startTask).toHaveBeenCalledWith(taskId);
    });

    it('应该显示错误信息和成功通知', async () => {
      const error = new Error('Task execution failed');
      await mockPopup.ui.showError(error.message);
      expect(mockPopup.ui.showError).toHaveBeenCalledWith(error.message);

      const successMessage = 'Task completed successfully';
      await mockPopup.ui.showSuccess(successMessage);
      expect(mockPopup.ui.showSuccess).toHaveBeenCalledWith(successMessage);
    });
  });

  describe('消息传递和通信', () => {
    it('应该在组件间正确传递消息', async () => {
      // Popup向Background发送消息
      const popupMessage = {
        type: TEST_MESSAGE_TYPES.TASK_START,
        data: { taskId: 'task-123' },
      };

      await chrome.runtime.sendMessage(popupMessage);
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(popupMessage);

      // Background处理消息
      await mockBackgroundScript.serviceWorker.messageHandler.handleMessage(popupMessage);
      expect(mockBackgroundScript.serviceWorker.messageHandler.handleMessage)
        .toHaveBeenCalledWith(popupMessage);
    });

    it('应该处理长连接通信', () => {
      const port = chrome.runtime.connect({ name: 'task-control' });
      
      const message = {
        type: TEST_MESSAGE_TYPES.STATUS_UPDATE,
        data: { status: 'running', progress: 0.5 },
      };

      port.postMessage(message);
      expect(port.postMessage).toHaveBeenCalledWith(message);
    });

    it('应该处理任务失败和重试', async () => {
      const taskConfig = TestUtils.createMockTaskConfig({
        urls: [TEST_URLS.INVALID],
        retryCount: 3,
      });

      // 模拟任务失败
      mockBackgroundScript.serviceWorker.taskScheduler.createTask
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce('task-retry-123');

      // 第一次尝试失败
      try {
        await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        if (error instanceof Error) {
          expect(error.message).toBe('Network error');
        }
      }

      // 重试成功
      const taskId = await mockBackgroundScript.serviceWorker.taskScheduler.createTask(taskConfig);
      expect(taskId).toBe('task-retry-123');
    });

    it('应该处理跨服务/上下文的消息传递', async () => {
      // Popup -> Background
      const taskConfig = TestUtils.createMockTaskConfig();
      await extensionContext.runtime.sendMessage({
        type: TEST_MESSAGE_TYPES.CREATE_TASK,
        payload: taskConfig as unknown,
      });

      expect(extensionContext.runtime.sendMessage).toHaveBeenCalledWith({
        type: TEST_MESSAGE_TYPES.CREATE_TASK,
        payload: taskConfig as unknown,
      });
    });
  });

  describe('性能和资源管理', () => {
    it('应该在高负载下保持响应性', async () => {
      const measure = new TestUtils.PerformanceMeasure();
      
      // 创建多个并发任务
      const taskPromises = Array.from({ length: 5 }, (_, i) =>
        mockBackgroundScript.serviceWorker.taskScheduler.createTask(
          TestUtils.createMockTaskConfig({ urls: [`${TEST_URLS.VALID}?task=${i}`] })
        )
      );

      const results = await Promise.all(taskPromises);
      expect(results).toBeDefined();
      
      measure.mark('concurrent_tasks_created');
      expect(measure.getMeasurement('concurrent_tasks_created')).toBeLessThan(TEST_TIMEOUTS.MEDIUM);
    });

    it('应该正确管理标签页生命周期', async () => {
      const initialTabCount = (await chrome.tabs.query({})).length;
      
      // 创建任务标签页
      const tab = await chrome.tabs.create({ url: TEST_URLS.VALID });
      
      // 验证标签页创建
      const afterCreateCount = (await chrome.tabs.query({})).length;
      expect(afterCreateCount).toBe(initialTabCount + 1);
      
      // 清理标签页
      if (tab.id) {
        await chrome.tabs.remove(tab.id);
      }
      
      // 验证标签页清理
      const afterRemoveCount = (await chrome.tabs.query({})).length;
      expect(afterRemoveCount).toBe(initialTabCount);
    });
  });

  describe('数据持久化', () => {
    it('应该保存和恢复任务状态', async () => {
      const taskConfig = TestUtils.createMockTaskConfig() as unknown as Record<string, unknown>;
      const taskState = {
        id: 'task-123',
        config: taskConfig,
        status: 'paused' as const,
        progress: 0.4,
        processedUrls: ['url1', 'url2'],
      };

      // 保存状态
      await chrome.storage.local.set({ [`task_${taskState.id}`]: taskState });
      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        [`task_${taskState.id}`]: taskState,
      });

      // 恢复状态
      const restored = await chrome.storage.local.get(`task_${taskState.id}`);
      expect(restored).toBeDefined();
    });

    it('应该处理存储配额限制', async () => {
      // 模拟存储空间不足
      chrome.storage.local.set = vi.fn().mockRejectedValue(
        new Error('QUOTA_EXCEEDED_ERR')
      );

      try {
        await chrome.storage.local.set({ largeData: 'x'.repeat(10000) });
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        if (error instanceof Error) {
          expect(error.message).toBe('QUOTA_EXCEEDED_ERR');
        }
      }
    });
  });

  describe('扩展安装和更新', () => {
    it('应该正确处理扩展安装', () => {
      const installHandler = vi.fn();
      (extensionContext.runtime as any).onInstalled?.addListener(installHandler);

      // 触发安装监听器
      
      // 在实际测试中，这需要更复杂的模拟
      expect((extensionContext.runtime as any).onInstalled?.addListener).toHaveBeenCalledWith(installHandler);
    });

    it('应该处理扩展更新', async () => {
      const updateHandler = vi.fn();
      (extensionContext.runtime as any).onInstalled?.addListener(updateHandler);

      // 触发更新监听器
      
      expect((extensionContext.runtime as any).onInstalled?.addListener).toHaveBeenCalledWith(updateHandler);
    });
  });
});