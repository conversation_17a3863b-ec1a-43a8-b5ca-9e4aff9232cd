/**
 * 任务调度器工厂
 * 
 * 提供TaskScheduler的统一入口
 */

import { WorkerWindowManager } from '@/background/WorkerWindowManager';
import { createLogger } from '@/shared/utils';
import { TaskScheduler } from './interfaces';
import { SERVICE_TOKENS } from './services/ServiceContainer';
import { createConfiguredContainer } from './services/ServiceRegistration';

const logger = createLogger('TaskSchedulerFactory');

/**
 * 创建任务调度器实例
 */
export function createTaskScheduler(
  workerWindowManager: WorkerWindowManager,
): TaskScheduler {
  // 创建依赖注入容器并注册所有服务
  const container = createConfiguredContainer(workerWindowManager);
    
    // 从容器获取调度器实例
    return container.get<TaskScheduler>(SERVICE_TOKENS.TASK_SCHEDULER);
}

/**
 * 验证调度器实现是否正常工作
 */
export function validateTaskScheduler(scheduler: TaskScheduler): boolean {
  try {
    logger.info('验证任务调度器实现...');
    
    // 基本功能检查
    const initialTaskCount = scheduler.getRunningTasksCount();
    const allTasks = scheduler.getAllTasks();
    
    logger.info(`初始运行任务数: ${initialTaskCount}`);
    logger.info(`总任务数: ${allTasks.length}`);
    
    // 检查Observable是否正常
    const metricsSubscription = scheduler.taskMetrics$.subscribe(metrics => {
      logger.debug(`任务指标更新: ${metrics.length} 个任务`);
    });
    
    const eventsSubscription = scheduler.taskEvents$.subscribe(event => {
      logger.debug(`任务事件: ${event.type}`);
    });
    
    // 清理订阅
    setTimeout(() => {
      metricsSubscription.unsubscribe();
      eventsSubscription.unsubscribe();
    }, 1000);
    
    logger.info('任务调度器验证完成');
    return true;
  } catch (error) {
    logger.error('任务调度器验证失败:', error);
    return false;
  }
}