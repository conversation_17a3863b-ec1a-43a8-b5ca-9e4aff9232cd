/**
 * Image processing utility functions, inspired by best practices from mcp-chrome.
 * These functions are designed to run efficiently in browser environments,
 * including Service Workers and Content Scripts, by leveraging OffscreenCanvas.
 */

/**
 * Creates an ImageBitmap from a data URL. This is a necessary first step
 * for using images with OffscreenCanvas.
 * @param dataUrl The data URL of the image.
 * @returns A Promise that resolves to an ImageBitmap.
 */
export async function createImageBitmapFromUrl(dataUrl: string): Promise<ImageBitmap> {
  const response = await fetch(dataUrl);
  const blob = await response.blob();
  return createImageBitmap(blob);
}

/**
 * Defines the options for image compression.
 */
export interface ImageCompressionOptions {
  /**
   * The scaling factor for dimensions (e.g., 0.7 for 70%). Defaults to 1.0.
   */
  scale?: number;
  /**
   * The quality for lossy formats like JPEG (0.0 to 1.0). Defaults to 0.85.
   */
  quality?: number;
  /**
   * The target image format. Defaults to 'image/jpeg'.
   */
  format?: 'image/jpeg' | 'image/webp';
}

/**
 * Compresses an image by scaling it and converting it to a target format with a specific quality.
 * This is highly effective for reducing the size of screenshot data before transmission.
 *
 * @param imageDataUrl The original image data URL (e.g., from dom-to-png or captureVisibleTab).
 * @param options Compression options.
 * @returns A promise that resolves to an object containing the compressed image data URL and its MIME type.
 */
export async function compressImage(
  imageDataUrl: string,
  options: ImageCompressionOptions,
): Promise<{ dataUrl: string; mimeType: string }> {
  const { scale = 1.0, quality = 0.85, format = 'image/jpeg' } = options;

  // 1. Create an ImageBitmap from the original data URL for efficient drawing.
  const imageBitmap = await createImageBitmapFromUrl(imageDataUrl);

  // 2. Calculate the new dimensions based on the scale factor.
  const newWidth = Math.round(imageBitmap.width * scale);
  const newHeight = Math.round(imageBitmap.height * scale);

  // 3. Use OffscreenCanvas for performance, as it doesn't need to be in the DOM.
  const canvas = new OffscreenCanvas(newWidth, newHeight);
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get 2D context from OffscreenCanvas');
  }

  // 4. Draw the original image onto the smaller canvas, effectively resizing it.
  ctx.drawImage(imageBitmap, 0, 0, newWidth, newHeight);

  // 5. Export the canvas content to the target format with the specified quality.
  const blob = await canvas.convertToBlob({ type: format, quality });

  // 6. Convert the resulting blob back to a data URL for transmission.
  const dataUrl = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });

  return { dataUrl, mimeType: format };
} 