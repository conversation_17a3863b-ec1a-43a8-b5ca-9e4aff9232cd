/**
 * 服务注册配置
 * 
 * 用于配置依赖注入容器中的所有服务
 */

import { WorkerWindowManager } from '@/background/WorkerWindowManager';
import { ExceptionService } from '@/services/exception-service/ExceptionService';
import { StateSnapshot } from '@/services/exception-service/StateSnapshot';
import { NotificationService } from '@/services/notification-service/NotificationService';
import { PrivacyService } from '@/services/privacy-service';
import { SecurityService } from '@/services/security-service';
import { AdaptiveThrottler } from '../AdaptiveThrottler';
import { ResourceMonitor } from '../ResourceMonitor';

import { EnhancedExceptionHandler } from '../../exception-handling/ExceptionHandler';
import { TypedMessenger } from '../../messaging/TypedMessenger';
import { UnifiedStateStore } from '../../state-management/UnifiedStateStore';
import {
  BatchOperationServiceImpl,
  TaskControlServiceImpl,
  TaskQueryServiceImpl,
  TaskSchedulerRefactored,
  TaskSubmissionServiceImpl
} from '../TaskSchedulerRefactored';
import { SlotManagerImpl } from './SlotManagerImpl';
import { TaskQueueServiceImpl } from './TaskQueueServiceImpl';
import { UnifiedTaskStateManager } from './UnifiedTaskStateManager';

import {
  SERVICE_TOKENS,
  ServiceContainer,
  ServiceContainerImpl
} from './ServiceContainer';

/**
 * 注册所有核心服务到容器
 */
export function registerCoreServices(container: ServiceContainer, workerWindowManager: WorkerWindowManager): void {
  // 注册外部依赖
  container.registerInstance(SERVICE_TOKENS.WORKER_WINDOW_MANAGER, workerWindowManager);
  
  // 注册单例服务
  container.registerSingleton(SERVICE_TOKENS.PRIVACY_SERVICE, () => PrivacyService.getInstance());
  container.registerSingleton(SERVICE_TOKENS.SECURITY_SERVICE, () => SecurityService.getInstance());
  
  // 注册其他核心服务
  container.registerSingleton(SERVICE_TOKENS.EXCEPTION_SERVICE, () => new ExceptionService());
  container.registerSingleton(SERVICE_TOKENS.NOTIFICATION_SERVICE, () => new NotificationService());
  container.registerSingleton(SERVICE_TOKENS.RESOURCE_MONITOR, () => new ResourceMonitor());
  container.registerSingleton(SERVICE_TOKENS.ADAPTIVE_THROTTLER, () => new AdaptiveThrottler());
  
  // 第二阶段新增服务
  container.registerSingleton(SERVICE_TOKENS.UNIFIED_STATE_STORE, (c) => 
    new UnifiedStateStore({}, c.get(SERVICE_TOKENS.SECURITY_SERVICE))
  );
  container.registerSingleton(SERVICE_TOKENS.TYPED_MESSENGER, () => 
    new TypedMessenger('CoreServices')
  );
  container.registerSingleton(SERVICE_TOKENS.ENHANCED_EXCEPTION_HANDLER, () => 
    new EnhancedExceptionHandler()
  );
  
  // StateSnapshot 依赖 PrivacyService
  container.registerSingleton(SERVICE_TOKENS.STATE_SNAPSHOT, (c) => 
    new StateSnapshot(c.get(SERVICE_TOKENS.PRIVACY_SERVICE))
  );
}

/**
 * 注册任务管理相关服务到容器
 */
export function registerTaskManagementServices(container: ServiceContainer): void {
  // 槽位管理器
  container.registerSingleton(SERVICE_TOKENS.SLOT_MANAGER, (c) => 
    new SlotManagerImpl(c.get(SERVICE_TOKENS.WORKER_WINDOW_MANAGER))
  );
  
  // 任务状态管理器 - 使用统一状态存储的新实现
  container.registerSingleton(SERVICE_TOKENS.TASK_STATE_MANAGER, (c) => 
    new UnifiedTaskStateManager(c.get(SERVICE_TOKENS.UNIFIED_STATE_STORE))
  );
  
  // 任务队列服务
  container.registerSingleton(SERVICE_TOKENS.TASK_QUEUE_SERVICE, () => 
    new TaskQueueServiceImpl()
  );
  
  // 任务控制服务
  container.registerSingleton(SERVICE_TOKENS.TASK_CONTROL_SERVICE, (c) => 
    new TaskControlServiceImpl(c.get(SERVICE_TOKENS.SLOT_MANAGER))
  );
  
  // 任务提交服务
  container.registerSingleton(SERVICE_TOKENS.TASK_SUBMISSION_SERVICE, (c) => 
    new TaskSubmissionServiceImpl(
      c.get(SERVICE_TOKENS.TASK_QUEUE_SERVICE),
      c.get(SERVICE_TOKENS.TASK_STATE_MANAGER)
    )
  );
  
  // 任务查询服务
  container.registerSingleton(SERVICE_TOKENS.TASK_QUERY_SERVICE, (c) => 
    new TaskQueryServiceImpl(
      c.get(SERVICE_TOKENS.TASK_STATE_MANAGER),
      c.get(SERVICE_TOKENS.SLOT_MANAGER)
    )
  );
  
  // 批量操作服务
  container.registerSingleton(SERVICE_TOKENS.BATCH_OPERATION_SERVICE, (c) => 
    new BatchOperationServiceImpl(
      c.get(SERVICE_TOKENS.TASK_QUEUE_SERVICE),
      c.get(SERVICE_TOKENS.TASK_CONTROL_SERVICE),
      c.get(SERVICE_TOKENS.TASK_STATE_MANAGER)
    )
  );
}

/**
 * 注册主任务调度器
 */
export function registerTaskScheduler(container: ServiceContainer): void {
  container.registerSingleton(SERVICE_TOKENS.TASK_SCHEDULER, (c) => 
    new TaskSchedulerRefactored(c)
  );
}

/**
 * 完整的服务注册函数
 */
export function registerAllServices(container: ServiceContainer, workerWindowManager: WorkerWindowManager): void {
  registerCoreServices(container, workerWindowManager);
  registerTaskManagementServices(container);
  registerTaskScheduler(container);
}

/**
 * 创建并配置完整的服务容器
 */
export function createConfiguredContainer(workerWindowManager: WorkerWindowManager): ServiceContainer {
  const container = new ServiceContainerImpl();
  
  registerAllServices(container, workerWindowManager);
  
  return container;
}