/* 
  可视化模式下覆盖在目标页面上的蒙版样式 
*/
#agent-mask {
  position: fixed;
  inset: 0;
  z-index: 2147483647; /* 确保在最顶层 */
  pointer-events: all; /* 拦截所有鼠标事件 */
  cursor: not-allowed; /* 显示禁止光标 */
  
  /* 半透明遮罩，高亮区域由 --x 和 --y 控制 */
  background: radial-gradient(
    circle at var(--x, 50%) var(--y, 50%),
    transparent 0%,
    rgba(0, 10, 40, 0.02) 100%
  );
  
  /* 当前操作区域高亮 */
  .viewport-highlight {
    position: absolute;
    border: 2px solid #00f3ff;
    box-shadow: 0 0 15px rgba(0, 243, 255, 0.5);
    transition: all 0.3s ease;
    pointer-events: none; /* 高亮框自身不拦截事件 */
  }

  /* 暂未使用的操作指示器样式 */
  .action-indicator {
    position: absolute;
    pointer-events: none;
  }
} 