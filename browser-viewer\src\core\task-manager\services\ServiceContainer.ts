/**
 * 依赖注入容器实现
 * 
 * 职责：
 * - 管理服务的创建和生命周期
 * - 提供依赖注入功能
 * - 支持单例和工厂模式
 */

import { createLogger } from '@/shared/utils';

// 服务标识符类型
export type ServiceToken = string | symbol;

// 服务工厂函数类型
export type ServiceFactory<T> = (container: ServiceContainer) => T;

// 服务注册选项
export interface ServiceRegistration<T> {
  factory: ServiceFactory<T>;
  singleton?: boolean;
  dependencies?: ServiceToken[];
}

// 依赖注入容器接口
export interface ServiceContainer {
  /**
   * 注册服务
   */
  register<T>(token: ServiceToken, registration: ServiceRegistration<T>): void;
  
  /**
   * 注册单例服务
   */
  registerSingleton<T>(token: ServiceToken, factory: ServiceFactory<T>): void;
  
  /**
   * 注册瞬态服务（每次获取都创建新实例）
   */
  registerTransient<T>(token: ServiceToken, factory: ServiceFactory<T>): void;
  
  /**
   * 注册实例（直接注册已创建的实例）
   */
  registerInstance<T>(token: ServiceToken, instance: T): void;
  
  /**
   * 获取服务实例
   */
  get<T>(token: ServiceToken): T;
  
  /**
   * 检查服务是否已注册
   */
  has(token: ServiceToken): boolean;
  
  /**
   * 解析服务（如果未注册则返回 undefined）
   */
  resolve<T>(token: ServiceToken): T | undefined;
  
  /**
   * 创建子容器
   */
  createChild(): ServiceContainer;
  
  /**
   * 清理容器
   */
  dispose(): void;
}

// 服务容器实现
export class ServiceContainerImpl implements ServiceContainer {
  private readonly _logger = createLogger('ServiceContainer');
  private readonly _services = new Map<ServiceToken, ServiceRegistration<any>>();
  private readonly _instances = new Map<ServiceToken, any>();
  private readonly _parent?: ServiceContainer;
  private _disposed = false;

  constructor(parent?: ServiceContainer) {
    this._parent = parent;
  }

  register<T>(token: ServiceToken, registration: ServiceRegistration<T>): void {
    if (this._disposed) {
      throw new Error('Cannot register services in disposed container');
    }
    
    this._services.set(token, registration);
    
    // 如果之前有缓存的实例，清除它
    if (this._instances.has(token)) {
      this._instances.delete(token);
    }
    
    this._logger.debug(`服务已注册: ${String(token)} (singleton: ${registration.singleton ?? false})`);
  }

  registerSingleton<T>(token: ServiceToken, factory: ServiceFactory<T>): void {
    this.register(token, {
      factory,
      singleton: true
    });
  }

  registerTransient<T>(token: ServiceToken, factory: ServiceFactory<T>): void {
    this.register(token, {
      factory,
      singleton: false
    });
  }

  registerInstance<T>(token: ServiceToken, instance: T): void {
    if (this._disposed) {
      throw new Error('Cannot register services in disposed container');
    }
    
    this._instances.set(token, instance);
    this._logger.debug(`实例已注册: ${String(token)}`);
  }

  get<T>(token: ServiceToken): T {
    const instance = this.resolve<T>(token);
    if (instance === undefined) {
      throw new Error(`Service not found: ${String(token)}`);
    }
    return instance;
  }

  has(token: ServiceToken): boolean {
    return this._instances.has(token) || 
           this._services.has(token) || 
           (this._parent?.has(token) ?? false);
  }

  resolve<T>(token: ServiceToken): T | undefined {
    if (this._disposed) {
      throw new Error('Cannot resolve services from disposed container');
    }

    // 1. 检查是否有直接注册的实例
    if (this._instances.has(token)) {
      return this._instances.get(token);
    }

    // 2. 检查是否有服务注册
    const registration = this._services.get(token);
    if (registration) {
      return this._createInstance(token, registration);
    }

    // 3. 检查父容器
    if (this._parent) {
      return this._parent.resolve<T>(token);
    }

    return undefined;
  }

  createChild(): ServiceContainer {
    return new ServiceContainerImpl(this);
  }

  dispose(): void {
    if (this._disposed) {
      return;
    }

    // 清理所有实例（如果它们有 dispose 方法的话）
    for (const [token, instance] of this._instances.entries()) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          instance.dispose();
          this._logger.debug(`已清理服务实例: ${String(token)}`);
        } catch (error) {
          this._logger.error(`清理服务实例失败: ${String(token)}`, error);
        }
      }
    }

    this._services.clear();
    this._instances.clear();
    this._disposed = true;
    
    this._logger.info('服务容器已清理');
  }

  private _createInstance<T>(token: ServiceToken, registration: ServiceRegistration<T>): T {
    // 检查循环依赖（简单实现）
    if (this._isCreating(token)) {
      throw new Error(`Circular dependency detected for service: ${String(token)}`);
    }

    try {
      this._markAsCreating(token);
      
      const instance = registration.factory(this);
      
      // 如果是单例，缓存实例
      if (registration.singleton !== false) {
        this._instances.set(token, instance);
      }
      
      this._logger.debug(`服务实例已创建: ${String(token)}`);
      return instance;
    } finally {
      this._unmarkAsCreating(token);
    }
  }

  // 简单的循环依赖检测
  private readonly _creating = new Set<ServiceToken>();

  private _isCreating(token: ServiceToken): boolean {
    return this._creating.has(token);
  }

  private _markAsCreating(token: ServiceToken): void {
    this._creating.add(token);
  }

  private _unmarkAsCreating(token: ServiceToken): void {
    this._creating.delete(token);
  }
}

// 服务标识符常量
export const SERVICE_TOKENS = {
  // 核心服务
  WORKER_WINDOW_MANAGER: Symbol('WorkerWindowManager'),
  PRIVACY_SERVICE: Symbol('PrivacyService'),
  SECURITY_SERVICE: Symbol('SecurityService'),
  EXCEPTION_SERVICE: Symbol('ExceptionService'),
  STATE_SNAPSHOT: Symbol('StateSnapshot'),
  NOTIFICATION_SERVICE: Symbol('NotificationService'),
  RESOURCE_MONITOR: Symbol('ResourceMonitor'),
  ADAPTIVE_THROTTLER: Symbol('AdaptiveThrottler'),
  
  // 任务管理服务
  SLOT_MANAGER: Symbol('SlotManager'),
  TASK_STATE_MANAGER: Symbol('TaskStateManager'),
  TASK_QUEUE_SERVICE: Symbol('TaskQueueService'),
  TASK_CONTROL_SERVICE: Symbol('TaskControlService'),
  TASK_SUBMISSION_SERVICE: Symbol('TaskSubmissionService'),
  TASK_QUERY_SERVICE: Symbol('TaskQueryService'),
  BATCH_OPERATION_SERVICE: Symbol('BatchOperationService'),
  BATCH_MANAGER: Symbol('BatchManager'),
  
  // 主调度器
  TASK_SCHEDULER: Symbol('TaskScheduler'),
  
  // 第二阶段新增服务
  UNIFIED_STATE_STORE: Symbol('UnifiedStateStore'),
  TYPED_MESSENGER: Symbol('TypedMessenger'),
  ENHANCED_EXCEPTION_HANDLER: Symbol('EnhancedExceptionHandler'),
};

// 全局容器实例
let globalContainer: ServiceContainer | null = null;

/**
 * 获取全局服务容器
 */
export function getGlobalContainer(): ServiceContainer {
  if (!globalContainer) {
    globalContainer = new ServiceContainerImpl();
  }
  return globalContainer;
}

/**
 * 设置全局服务容器
 */
export function setGlobalContainer(container: ServiceContainer): void {
  globalContainer = container;
}

/**
 * 清理全局服务容器
 */
export function disposeGlobalContainer(): void {
  if (globalContainer) {
    globalContainer.dispose();
    globalContainer = null;
  }
}

/**
 * 便捷的服务获取函数
 */
export function getService<T>(token: ServiceToken): T {
  return getGlobalContainer().get<T>(token);
}

/**
 * 便捷的服务解析函数
 */
export function resolveService<T>(token: ServiceToken): T | undefined {
  return getGlobalContainer().resolve<T>(token);
}