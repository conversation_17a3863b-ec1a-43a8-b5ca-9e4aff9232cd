/**
 * 任务调度中心 - 核心调度器
 *
 * @description
 * 核心设计原则 (Slot-based):
 * 1. 槽位是物理执行单元 (Slot as Physical Execution Unit):
 *    - 每个槽位对应一个长期存在的浏览器标签页。
 *    - 调度器在初始化时创建所有槽位，在关闭时销毁。
 * 2. 任务是原子工作单元 (Task as Atomic Work Unit):
 *    - 一个 `Task` 实例仅负责处理单个URL。
 *    - 任务被分配到槽位中执行，不管理自身标签页的生命周期。
 * 3. 智能队列与同源亲和性 (Intelligent Queue with Domain Affinity):
 *    - 调度器维护一个待处理URL的总队列。
 *    - 当槽位空闲时，优先从队列中寻找与该槽位上一个任务同域名的任务，以最大化缓存利用率。
 */

import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, takeUntil, throttleTime } from 'rxjs/operators';

import {
  AgentEvent,
  TabId,
  TaskConfig,
  TaskId,
  TaskMetrics
} from '@/shared/types';

import {
  MESSAGE_TYPES,
  STORAGE_KEYS
} from '@/shared/constants';

import {
  createLogger,
  generateTaskId,
  getCurrentTimestamp,
  getStorageData,
  setStorageData
} from '@/shared/utils';

import { WorkerWindowManager } from '@/background/WorkerWindowManager';
import { ExceptionService } from '@/services/exception-service/ExceptionService';
import { StateSnapshot } from '@/services/exception-service/StateSnapshot';
import { NotificationService } from '@/services/notification-service/NotificationService';
import { PrivacyService } from '@/services/privacy-service';
import { SecurityService } from '@/services/security-service';
import { AdaptiveThrottler } from './AdaptiveThrottler';
import { ResourceMonitor } from './ResourceMonitor';
import { Task } from './Task';

const MAX_COMPLETED_BATCHS_TO_KEEP = 25;

/**
 * 代表一个并发槽位的状态。
 */
type SlotState = {
  id: number;
  tabId: TabId;
  isBusy: boolean;
  currentTask: Task | null;
  lastDomain: string | null;
};

export interface TaskScheduler {
  submitBatch(configs: TaskConfig[]): void;
  pauseTask(taskId: TaskId): Promise<void>;
  resumeTask(taskId: TaskId): Promise<void>;
  cancelTask(taskId: TaskId): void;
  getTaskMetrics(taskId: TaskId): TaskMetrics | null;
  getTask(taskId: TaskId): Task | undefined;
  getAllTasks(): TaskMetrics[];
  getRunningTasksCount(): number;
  shutdown(): Promise<void>;
  getTaskByTabId(tabId: number): Task | undefined;
  startAllPendingTasks(): Promise<void>;
  pauseAllTasks(): Promise<void>;
  clearAllTasks(): Promise<void>;
  startSelectedTasks(batchIds: string[]): Promise<void>;
  pauseSelectedTasks(batchIds: string[]): Promise<void>;
  clearSelectedTasks(batchIds: string[]): Promise<void>;
}

export class TaskSchedulerImpl implements TaskScheduler {
  private readonly _logger = createLogger('TaskScheduler');
  private readonly _destroy$ = new Subject<void>();
  
  // 任务管理
  private readonly _tasks = new Map<TaskId, Task>();
  private readonly _taskMetricsInternal$ = new BehaviorSubject<Map<TaskId, TaskMetrics>>(new Map());
  private readonly _taskEventsInternal$ = new Subject<AgentEvent>();
  
  // -- 调度模型核心数据结构 --
  private readonly _slots: SlotState[] = [];
  private readonly _pendingQueue: TaskConfig[] = [];
  private _initialMaxConcurrency: number;
  private _maxConcurrency: number;
  
  // 服务依赖
  private readonly _exceptionService: ExceptionService;
  private readonly _stateSnapshot: StateSnapshot;
  private readonly _notificationService: NotificationService;
  private readonly _securityService: SecurityService;
  private readonly _privacyService: PrivacyService;
  private readonly _workerWindowManager: WorkerWindowManager;
  private readonly _resourceMonitor: ResourceMonitor;
  private readonly _adaptiveThrottler: AdaptiveThrottler;

  // 状态管理
  private _isShuttingDown = false;
  
  // --- 公开的 Observable ---
  public get taskMetrics$(): Observable<TaskMetrics[]> {
    return this._taskMetricsInternal$.asObservable().pipe(
      map(metricsMap => Array.from(metricsMap.values()))
    );
  }
  public get taskEvents$(): Observable<AgentEvent> {
    return this._taskEventsInternal$.asObservable();
  }

  constructor(workerWindowManager: WorkerWindowManager) {
    // 初始化服务实例
    this._privacyService = PrivacyService.getInstance();
    this._securityService = SecurityService.getInstance();
    this._exceptionService = new ExceptionService();
    this._stateSnapshot = new StateSnapshot(this._privacyService);
    this._notificationService = new NotificationService();
    this._resourceMonitor = new ResourceMonitor();
    this._adaptiveThrottler = new AdaptiveThrottler();
    this._workerWindowManager = workerWindowManager;

    this._initialMaxConcurrency = 2;
    this._maxConcurrency = 2;
  }
  public async startAllPendingTasks(): Promise<void> {
    this._logger.info('Manually triggering start for all unfinished tasks.');

    // --- Resume all paused tasks ---
    const resumePromises: Promise<void>[] = [];
    const tasksToRetry: TaskConfig[] = [];
    const tasksToProcess = Array.from(this._tasks.values());

    for (const task of tasksToProcess) {
        const status = task.getStatus();
        if (status === 'paused') {
            this._logger.info(`Resuming paused task ${task._id}.`);
            resumePromises.push(task.resume());
        } else if (status === 'failed' || status === 'cancelled') {
            this._logger.info(`Queueing failed/cancelled task ${task._id} for retry.`);
            tasksToRetry.push(task._config);

            // Clean up old instance
            task.purgeData();
            this._tasks.delete(task._id);
            const metricsMap = this._taskMetricsInternal$.value;
            metricsMap.delete(task._id);
            this._taskMetricsInternal$.next(new Map(metricsMap));
        }
    }
    
    await Promise.all(resumePromises);

    // Add retries to the front of the queue
    if (tasksToRetry.length > 0) {
        this._pendingQueue.unshift(...tasksToRetry);
        this._logger.info(`Added ${tasksToRetry.length} tasks to the queue for retry.`);
    }

    // --- Schedule all pending tasks ---
    await this._scheduleTasks();
    await this._saveStateToStorage();
  }

  public async initializeScheduler(): Promise<void> {
    this._logger.info('插件任务调度器正在连接...');
    const initialConcurrency = Math.max(2, Math.floor(navigator.hardwareConcurrency / 2));
    this._initialMaxConcurrency = initialConcurrency;
    this._maxConcurrency = initialConcurrency;
    
    this._setupThrottlingActions();
    this._resourceMonitor.metrics$
      .pipe(throttleTime(1000), takeUntil(this._destroy$))
      .subscribe(metrics => this._adaptiveThrottler.adjust(metrics));
    
    await this._restoreStateFromStorage();
    this._logger.info('插件任务调度器已连接。');
  }
  
  /**
   * 接收一批任务配置，将它们拆分为原子任务并加入待处理队列。
   * @param configs 任务配置数组。
   */
  public submitBatch(configs: TaskConfig[]): void {
    if (this._isShuttingDown) {
      this._logger.warn('Scheduler is shutting down, rejecting new batch.');
      return;
    }
    
    let addedCount = 0;
    const currentMetricsMap = this._taskMetricsInternal$.value;
    const newMetricsMap = new Map(currentMetricsMap);
    
    for (const config of configs) {
      // 为每个传入的 config (代表一个批处理) 生成一个唯一的 batchId
      const batchId = config.batchId ?? crypto.randomUUID();

      if (config.urls && config.urls.length > 0) {
        for (const url of config.urls) {
          // 为每个URL创建一个独立的任务配置，并附加相同的 batchId
          const singleUrlConfig: TaskConfig = {
            ...config,
            urls: [url],
            batchId: batchId, // 确保每个子任务都带有批处理ID
          };
          this._pendingQueue.push(singleUrlConfig);
          addedCount++;
          
          // 为新任务创建初始指标
          const tempId = `pending-${batchId}-${url}-${Math.random()}`;
          // 检查指标是否已存在（例如，对于重新排队的任务）
          const taskExists = Array.from(newMetricsMap.values()).some(m =>
            m.config && m.config.batchId === batchId && m.config.urls[0] === url
          );

          if (!taskExists) {
              newMetricsMap.set(tempId, {
                  id: tempId,
                  batchId: batchId,
                  status: 'pending',
                  startTime: getCurrentTimestamp(),
                  totalPages: 1,
                  processedPages: 0,
                  config: singleUrlConfig,
                  endTime: undefined,
                  cpuUsage: 0, memoryUsage: 0, networkUsage: 0,
                  errors: []
              });
          }
        }
      }
    }

    if (newMetricsMap.size > currentMetricsMap.size) {
      this._taskMetricsInternal$.next(newMetricsMap);
    }
    
    this._logger.info(`Added ${addedCount} new tasks from ${configs.length} batch(es) to the pending queue. Total pending: ${this._pendingQueue.length}.`);
    
    // 持久化更新后的任务队列
    void this._saveStateToStorage();
  }

  /**
   * 核心调度逻辑：为所有空闲的槽位分配最合适的任务。
   */
  private async _scheduleTasks(): Promise<void> {
    if (this._isShuttingDown) return;
    
    this._logger.debug('Running scheduling cycle...');

    // 按需创建或扩容槽位
    const hasPendingTasks = this._pendingQueue.length > 0;
    if (hasPendingTasks) {
      const runningTasks = this.getRunningTasksCount();
      const currentTotalSlots = this._slots.length;
      // 计算理论上需要的槽位总数
      const requiredSlots = Math.min(this._maxConcurrency, runningTasks + this._pendingQueue.length);
      
      if (currentTotalSlots < requiredSlots) {
        const slotsToCreate = requiredSlots - currentTotalSlots;
        this._logger.info(`需要 ${slotsToCreate} 个新槽位以满足任务需求 (目标: ${requiredSlots}, 当前: ${currentTotalSlots})。`);
        await this._addSlots(slotsToCreate);
      }
    }
    
    const freeSlots = this._slots.filter(s => !s.isBusy);
    
    // 如果有任务要执行并且监控器当前未运行，则启动它
    const isAboutToStartWork = this._pendingQueue.length > 0 && freeSlots.length > 0;
    if (isAboutToStartWork && !this._resourceMonitor.isMonitoring()) {
        this._logger.info('Tasks are about to start. Starting resource monitor.');
        this._resourceMonitor.start();
    }
    
    if (freeSlots.length === 0) {
      this._logger.debug('No free slots available.');
      return;
    }
    
    if (this._pendingQueue.length === 0) {
      this._logger.debug('Pending queue is empty.');
      // 如果队列为空，且没有正在运行的任务，说明所有工作都已完成，关闭所有槽位。
      if (this.getRunningTasksCount() === 0) {
        this._logger.info('All tasks are complete and queue is empty. Shutting down all slots.');
        await this._shutdownAllSlots();
      }
      return;
    }
    
    for (const slot of freeSlots) {
      if (this._pendingQueue.length === 0) break; // 如果队列在此期间被耗尽

      const taskConfig = this._findBestTaskForSlot(slot);
      if (taskConfig) {
        this._logger.info(`Assigning task for URL ${taskConfig.urls[0]} to Slot ${slot.id} (Tab ${slot.tabId}).`);
        
        // 当任务从"待处理"变为"运行中"时，移除其临时指标
        const metricsMap = this._taskMetricsInternal$.value;
        const tempMetricEntry = Array.from(metricsMap.entries()).find(
          ([, metric]) =>
            metric.status === 'pending' &&
            metric.config &&
            metric.config.batchId === taskConfig.batchId &&
            metric.config.urls[0] === taskConfig.urls[0]
        );

        if (tempMetricEntry) {
          const newMetricsMap = new Map(metricsMap);
          newMetricsMap.delete(tempMetricEntry[0]); // 用临时ID删除
          this._taskMetricsInternal$.next(newMetricsMap);
          this._logger.debug(`Removed temporary metric for starting task: ${taskConfig.urls[0]}`);
        }
        
        slot.isBusy = true;
        const taskId = generateTaskId();
        const task = new Task(
          taskId,
          taskConfig,
          this._exceptionService,
          this._stateSnapshot,
          this._notificationService,
          this._privacyService,
          this._securityService,
        );
        
        this._tasks.set(taskId, task);
        slot.currentTask = task;
        
        this._monitorTask(task);
        
        // 启动任务，并传入它应该使用的标签页ID
        void task.start(slot.tabId);
      }
    }
  }

  /**
   * 为给定的槽位寻找最合适的下一个任务。
   * 优先寻找同域名的任务以利用缓存。
   * @param slot 空闲的槽位。
   * @returns 找到的任务配置，如果队列为空则返回 null。
   */
  private _findBestTaskForSlot(slot: SlotState): TaskConfig | null {
    if (this._pendingQueue.length === 0) return null;
    
    // 1. 尝试同源亲和性匹配
    if (slot.lastDomain) {
      const affinityIndex = this._pendingQueue.findIndex(config => {
        try {
          return new URL(config.urls[0]).hostname === slot.lastDomain;
        } catch {
          return false; // 无效URL，不匹配
        }
      });
      
      if (affinityIndex !== -1) {
        this._logger.info(`Found affinity task for domain ${slot.lastDomain} for Slot ${slot.id}.`);
        const [taskConfig] = this._pendingQueue.splice(affinityIndex, 1);
        return taskConfig;
      }
    }
    
    // 2. 如果没有亲和性匹配，则从队列中寻找优先级最高的任务
    this._logger.info(`Slot ${slot.id} 没有找到亲和性任务，开始搜索优先级最高的任务...`);
    if (this._pendingQueue.length === 0) return null;

    let highestPriorityIndex = 0;
    // 默认为普通优先级10，undefined的配置也视为10
    let highestPriority = this._pendingQueue[0].priority ?? 10;

    for (let i = 1; i < this._pendingQueue.length; i++) {
      const currentPriority = this._pendingQueue[i].priority ?? 10;
      if (currentPriority < highestPriority) {
        highestPriority = currentPriority;
        highestPriorityIndex = i;
      }
    }
    
    this._logger.info(`选中了队列中优先级最高的任务 (优先级: ${highestPriority})。`);
    const [taskConfig] = this._pendingQueue.splice(highestPriorityIndex, 1);
    return taskConfig;
  }

  private async _handleTaskCompletion(task: Task): Promise<void> {
    const taskId = task._id;
    this._logger.info(`任务 ${taskId} 已到达终结状态: ${task.getStatus()}`);

    const slotIndex = this._slots.findIndex(s => s.currentTask?._id === taskId);
    if (slotIndex > -1) {
      const slot = this._slots[slotIndex];
      try {
        const url = task._config.urls[0];
        slot.lastDomain = new URL(url).hostname;
      } catch {
        slot.lastDomain = null;
      }
      slot.isBusy = false;
      slot.currentTask = null;

      // --- 平滑缩容 ---
      if (this._slots.length > this._maxConcurrency) {
        this._logger.warn(`平滑缩容: 当前槽位总数 ${this._slots.length} > 预期并发数 ${this._maxConcurrency}。正在关闭刚空闲的槽位 ${slot.id}。`);
        this._slots.splice(slotIndex, 1);
        await this._workerWindowManager.removeTab(slot.tabId)
          .catch(e => this._logger.error(`关闭缩容槽位的标签页 ${slot.tabId} 失败:`, e));
        
        // 缩容后，我们还需检查是否所有任务都已完成
        if (this.getRunningTasksCount() === 0 && this._pendingQueue.length === 0) {
          this._logger.info('Final task completed during scale-down. Shutting down remaining slots.');
          await this._shutdownAllSlots();
        }
        return;
      }
      
      this._logger.info(`槽位 ${slot.id} (Tab ${slot.tabId}) 已空闲。最后处理域名: ${slot.lastDomain}.`);
      
      // 在调度新任务前，检查是否所有工作都已完成
      if (this.getRunningTasksCount() === 0 && this._pendingQueue.length === 0) {
        this._logger.info('Final task completed and queue is empty. Shutting down all slots.');
        await this._shutdownAllSlots();
      } else {
        void this._scheduleTasks();
      }
    } else {
      this._logger.warn(`无法为已完成的任务 ${taskId} 找到对应槽位。可能发生在关闭期间。`);
    }

    this._updateTaskMetrics(taskId, {
      status: task.getStatus(),
      endTime: getCurrentTimestamp(),
    });

    // 任务完成后，进行一次清理
    this._pruneCompletedTasks();
  }
  
  async shutdown(): Promise<void> {
    if (this._isShuttingDown) return;
    
    this._logger.info('Shutting down TaskScheduler...');
    this._isShuttingDown = true;
    
    // 1. 清空待处理队列，不再接受新任务
    this._pendingQueue.length = 0;
    
    // 2. 取消所有正在运行的任务
    const runningTasks = this._slots.filter(s => s.isBusy && s.currentTask).map(s => s.currentTask!);
    await Promise.all(runningTasks.map(task => task.cancel()));
    
    // 3. 关闭所有槽位和标签页
    await this._shutdownAllSlots();
    
    // 4. 保存最终状态 (空的队列)
    await this._saveStateToStorage();
    
    // 停止监控，清理订阅等
    if (this._resourceMonitor.isMonitoring()) {
      this._resourceMonitor.stop();
    }
    this._destroy$.next();
    this._destroy$.complete();
    
    this._logger.info('插件任务调度器已关闭。');
  }
  
  // --- 状态持久化 ---

  private async _restoreStateFromStorage(): Promise<void> {
    try {
      const encryptedData = await getStorageData<string | null>(STORAGE_KEYS.TASKS, null);
      if (!encryptedData) {
        this._logger.info('未找到先前任务队列，跳过恢复。');
        return;
      }
      
      const decryptedJson = await this._securityService.decryptData(encryptedData);
      if (!decryptedJson) {
        this._logger.warn('Failed to decrypt task queue.');
        return;
      }
  
      // 存储 TaskConfig 数组
      const restoredQueue = JSON.parse(decryptedJson) as TaskConfig[];
      
      const validatedQueue = restoredQueue.filter(config => {
        if (!config || !Array.isArray(config.urls) || config.urls.length === 0) {
          this._logger.warn('发现存储中存在无效的任务配置，已丢弃:', config);
          return false;
        }
        return true;
      });

      this._pendingQueue.push(...validatedQueue);
      
      this._logger.info(`成功恢复了 ${validatedQueue.length} 个任务到待处理队列。`);
      
      // 初始化后如果队列中有任务，则等待手动触发
      if (this._pendingQueue.length > 0) {
        this._logger.info('待处理任务已从存储中恢复，等待手动开始。');
      }
      
    } catch (error) {
      this._logger.error('Failed to restore task state:', error);
      this._pendingQueue.length = 0; // 清空以防数据损坏
    }
  }
  
  private async _saveStateToStorage(): Promise<void> {
    try {
      // 现在只保存待处理的队列
      if (this._pendingQueue.length === 0) {
        await setStorageData(STORAGE_KEYS.TASKS, null);
        this._logger.info('Pending queue is empty, storage cleared.');
        return;
      }
      const jsonToEncrypt = JSON.stringify(this._pendingQueue);
      const encryptedData = await this._securityService.encryptData(jsonToEncrypt);
      await setStorageData(STORAGE_KEYS.TASKS, encryptedData);
      this._logger.info(`${this._pendingQueue.length} pending tasks saved.`);
    } catch (error) {
      this._logger.error('Error saving task state:', error);
    }
  }

  public getTaskByTabId(tabId: number): Task | undefined {
    const slot = this._slots.find(s => s.tabId === tabId);
    return slot?.currentTask || undefined;
  }
  
  public getRunningTasksCount(): number {
    return this._slots.filter(s => s.isBusy).length;
  }
  
  async pauseAllTasks(): Promise<void> {
    this._logger.info('Pausing all active tasks...');
    const tasksToPause = this._slots.filter(s => s.isBusy && s.currentTask).map(s => s.currentTask!);
    const pausePromises = tasksToPause.map(task => this.pauseTask(task._id));
    await Promise.all(pausePromises);
    this._logger.info('All active tasks paused.');
  }

  async clearAllTasks(): Promise<void> {
    this._logger.info('Clearing all non-running tasks and pending queue...');

    // 1. Clear the pending queue completely
    const pendingCount = this._pendingQueue.length;
    this._pendingQueue.length = 0;
    if (pendingCount > 0) {
      this._logger.info('Pending queue cleared.');
    }

    // 2. Identify non-running tasks and prepare a new metrics map with only running tasks.
    const tasksToPurge: Task[] = [];
    const newMetricsMap = new Map<TaskId, TaskMetrics>();
    const currentMetricsMap = this._taskMetricsInternal$.value;

    for (const task of this._tasks.values()) {
        if (task.getStatus() === 'running') {
            // Keep running tasks and their metrics
            if (currentMetricsMap.has(task._id)) {
                newMetricsMap.set(task._id, currentMetricsMap.get(task._id)!);
            }
        } else {
            // Mark non-running tasks for purging
            tasksToPurge.push(task);
        }
    }
    
    // 3. Purge data for non-running tasks and remove them from the main task map
    if (tasksToPurge.length > 0) {
        this._logger.info(`Found ${tasksToPurge.length} non-running tasks to clear and purge.`);
        for (const task of tasksToPurge) {
            task.purgeData();
            this._tasks.delete(task._id);
            this._logger.debug(`Cleared and purged task ${task._id}.`);
        }
    }

    // 4. Update the metrics subject with the new map, which now only contains running tasks
    // and excludes all pending, completed, failed, etc., tasks.
    this._taskMetricsInternal$.next(newMetricsMap);

    await this._saveStateToStorage();
    this._logger.info('Finished clearing non-running tasks.');
  }

  /**
   * 监听任务状态变化并处理生命周期
   * @param task 要监听的任务
   */
  private _monitorTask(task: Task): void {
    task.events$.pipe(takeUntil(this._destroy$)).subscribe(event => {
      if (event.type === 'statusChange' && ['completed', 'failed', 'cancelled'].includes(event.payload.newStatus)) {
        void this._handleTaskCompletion(task);
      } else if (event.type === 'statusChange') {
        this._updateTaskMetrics(task._id, { status: event.payload.newStatus });
        this._taskEventsInternal$.next({
          type: MESSAGE_TYPES.TASK_STATUS_CHANGE,
          data: { taskId: task._id, newStatus: event.payload.newStatus },
          timestamp: getCurrentTimestamp(),
          source: 'Task'
        });
      }
    });
  }
  
  private _updateTaskMetrics(taskId: TaskId, newMetrics: Partial<TaskMetrics>): void {
    const currentMetricsMap = this._taskMetricsInternal$.value;
    const existingMetrics = currentMetricsMap.get(taskId);
    
    if (existingMetrics) {
      const updatedMetrics: TaskMetrics = {
        ...existingMetrics,
        ...newMetrics,
      };
      
      const newMetricsMap = new Map(currentMetricsMap);
      newMetricsMap.set(taskId, updatedMetrics);
      this._taskMetricsInternal$.next(newMetricsMap);
    } else {
      // 任务启动时可能还没有指标，需要创建
      const task = this._tasks.get(taskId);
      if (task) {
          const serialized = task.serialize();
          const initialMetrics: TaskMetrics = {
              id: taskId,
              batchId: task.batchId,
              status: task.getStatus(),
              startTime: serialized.startedAt ?? serialized.createdAt,
              endTime: serialized.completedAt,
              processedPages: serialized.processedUrls.length,
              totalPages: serialized.config.urls.length,
              cpuUsage: 0, memoryUsage: 0, networkUsage: 0,
              errors: [],
              config: serialized.config,
              ...newMetrics,
          };
          const newMetricsMap = new Map(currentMetricsMap);
          newMetricsMap.set(taskId, initialMetrics);
          this._taskMetricsInternal$.next(newMetricsMap);
      } else {
        this._logger.warn(`Attempted to update metrics for a non-existent task: ${taskId}`);
      }
    }
  }
  
  private _setupThrottlingActions(): void {
    this._logger.info('正在设置节流响应动作...');
    
    // 级别0: 恢复正常
    this._adaptiveThrottler.on(0, () => {
      this._logger.info('节流级别 0: 恢复正常并发和任务行为。');
      void this._setDynamicMaxConcurrency(this._initialMaxConcurrency);
      this._broadcastThrottlingLevel(0);
    });
    
    // 级别1: 轻度节流
    this._adaptiveThrottler.on(1, () => {
      this._logger.info('节流级别 1: 轻度节流，减少并发并降低任务强度。');
      const newConcurrency = Math.max(1, Math.floor(this._initialMaxConcurrency * 0.75));
      void this._setDynamicMaxConcurrency(newConcurrency);
      this._broadcastThrottlingLevel(1);
    });
    
    // 级别2: 深度节流
    this._adaptiveThrottler.on(2, () => {
      this._logger.warn('节流级别 2: 深度节流，大幅减少并发并暂停非关键任务。');
      const newConcurrency = Math.max(1, Math.floor(this._initialMaxConcurrency * 0.5));
      void this._setDynamicMaxConcurrency(newConcurrency);
      this._broadcastThrottlingLevel(2);
    });
  }

  /**
   * 动态调整最大并发数（槽位总数）。
   * @param newMax - 最大并发数目标。
   */
  private async _setDynamicMaxConcurrency(newMax: number): Promise<void> {
    const currentTotalSlots = this._slots.length;
    const oldMaxConcurrency = this._maxConcurrency;
    this._maxConcurrency = newMax; // 更新目标并发数

    this._logger.info(`动态并发调整: 合理槽位数： ${oldMaxConcurrency} -> ${this._maxConcurrency}。当前槽位总数: ${currentTotalSlots}。`);

    if (newMax > currentTotalSlots) {
      // 扩容：立刻新增总槽位不超过 (运行中任务数 + 待处理任务数) 的槽位
      const runningTasks = this.getRunningTasksCount();
      const pendingTasks = this._pendingQueue.length;
      const totalRequiredSlots = Math.min(newMax, runningTasks + pendingTasks);
      
      const delta = totalRequiredSlots - currentTotalSlots;

      if (delta > 0) {
          this._logger.info(`需要扩容，可增加 ${delta} 个新槽位。`);
          await this._addSlots(delta);
          // 扩容后，立即触发调度以利用新槽位
          void this._scheduleTasks();
      } else {
        this._logger.info(`动态并发调整：合理并发数增加，但当前无更多任务，暂不扩容。`);
      }
    } else if (newMax < currentTotalSlots) {
      // --- 缩容：不立即操作，等待任务完成 ---
      this._logger.info(`需要缩容，${currentTotalSlots - newMax} 个槽位在任务完成后将被关闭。`);
      // 实际的缩容操作将在 _handleTaskCompletion 中进行
    }
  }

  private _broadcastThrottlingLevel(level: number): void {
    this._logger.info(`向所有活动任务广播节流级别： ${level}。`);
    for (const slot of this._slots) {
      if (slot.isBusy && slot.currentTask) {
        slot.currentTask.applyThrottling(level);
      }
    }
  }

  getTask(taskId: TaskId): Task | undefined {
    return this._tasks.get(taskId);
  }

  getTaskMetrics(taskId: TaskId): TaskMetrics | null {
    return this._taskMetricsInternal$.value.get(taskId) || null;
  }

  getAllTasks(): TaskMetrics[] {
    return Array.from(this._taskMetricsInternal$.value.values());
  }

  async pauseTask(taskId: TaskId): Promise<void> {
    const task = this._tasks.get(taskId);
    if (!task) throw new Error(`Task ${taskId} not found`);
    if (task.getStatus() === 'running') {
      await task.pause();
    }
  }

  async resumeTask(taskId: TaskId): Promise<void> {
    const task = this._tasks.get(taskId);
    if (!task) throw new Error(`Task ${taskId} not found`);
    if (task.getStatus() === 'paused') {
      await task.resume();
    }
  }

  cancelTask(taskId: TaskId): void {
    const task = this._tasks.get(taskId);
    if (!task) {
      this._logger.warn(`Task ${taskId} not found for cancellation.`);
      return;
    }
    task.cancel();
  }

  public async startSelectedTasks(batchIds: string[]): Promise<void> {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('startSelectedTasks called with no batchIds.');
      return;
    }
    this._logger.info(`Prioritizing and starting tasks for batches: ${batchIds.join(', ')}`);

    // --- Part 1: Handle existing tasks (paused, failed) ---
    const tasksToRetry: TaskConfig[] = [];
    const resumePromises: Promise<void>[] = [];

    const tasksToProcess = Array.from(this._tasks.values());
    for (const task of tasksToProcess) {
        if (task.batchId && batchIds.includes(task.batchId)) {
            const status = task.getStatus();
            if (status === 'paused') {
                this._logger.info(`Resuming paused task ${task._id} from batch ${task.batchId}.`);
                resumePromises.push(task.resume());
            } else if (status === 'failed' || status === 'cancelled') {
                this._logger.info(`Queueing task ${task._id} from batch ${task.batchId} for retry.`);
                tasksToRetry.push(task._config); // Get original config
                
                // Clean up the old failed/cancelled task
                task.purgeData();
                this._tasks.delete(task._id);
                const metricsMap = this._taskMetricsInternal$.value;
                metricsMap.delete(task._id);
                this._taskMetricsInternal$.next(new Map(metricsMap));
            }
        }
    }

    await Promise.all(resumePromises);

    // --- Part 2: Handle pending queue ---
    const selectedPendingConfigs: TaskConfig[] = [];
    const otherPendingConfigs: TaskConfig[] = [];

    for (const config of this._pendingQueue) {
        if (config.batchId && batchIds.includes(config.batchId)) {
            selectedPendingConfigs.push(config);
        } else {
            otherPendingConfigs.push(config);
        }
    }

    // --- Part 3: Combine and schedule ---
    // New tasks from retries + pending tasks from selected batches go to the front.
    this._pendingQueue.length = 0;
    this._pendingQueue.push(...tasksToRetry, ...selectedPendingConfigs, ...otherPendingConfigs);
    
    this._logger.info(`Queue updated. Retrying ${tasksToRetry.length}, prioritized ${selectedPendingConfigs.length} pending.`);

    await this._scheduleTasks();
    await this._saveStateToStorage();
  }

  public async pauseSelectedTasks(batchIds: string[]): Promise<void> {
    if (!batchIds || batchIds.length === 0) {
        this._logger.warn('pauseSelectedTasks called with no batchIds.');
        return;
    }
    this._logger.info(`Pausing tasks for selected batches: ${batchIds.join(', ')}`);

    // Find running tasks that belong to the selected batches by looking at active slots
    const tasksToPause = this._slots
        .filter(slot => 
            slot.isBusy && 
            slot.currentTask?.batchId && 
            batchIds.includes(slot.currentTask.batchId)
        )
        .map(slot => slot.currentTask!);

    if (tasksToPause.length === 0) {
        this._logger.info('No running tasks found in the selected batches to pause.');
        return;
    }

    this._logger.info(`Found ${tasksToPause.length} running tasks to pause.`);
    const pausePromises = tasksToPause.map(task => this.pauseTask(task._id));
    await Promise.all(pausePromises);
    this._logger.info(`Paused ${tasksToPause.length} tasks from selected batches.`);
  }

  public async clearSelectedTasks(batchIds: string[]): Promise<void> {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('clearSelectedTasks called with no batchIds.');
      return;
    }
    this._logger.info(`Clearing selected non-running tasks for batches: ${batchIds.join(', ')}`);

    // 1. Filter and remove tasks from the pending queue
    const originalPendingCount = this._pendingQueue.length;
    const remainingPending = this._pendingQueue.filter(config => !config.batchId || !batchIds.includes(config.batchId));
    if (this._pendingQueue.length > remainingPending.length) {
      this._pendingQueue.length = 0;
      this._pendingQueue.push(...remainingPending);
      const pendingRemovedCount = originalPendingCount - this._pendingQueue.length;
      this._logger.info(`Removed ${pendingRemovedCount} pending tasks from selected batches.`);
    }

    // 2. Identify and remove non-running tasks and pending metrics from the maps
    const tasksToPurge: Task[] = [];
    const metricsMap = this._taskMetricsInternal$.value;
    const newMetricsMap = new Map(metricsMap);

    // Remove metrics for pending tasks belonging to the selected batches
    for (const [metricId, metric] of newMetricsMap.entries()) {
      if (metric.status === 'pending' && metric.batchId && batchIds.includes(metric.batchId)) {
        newMetricsMap.delete(metricId);
      }
    }
    
    // Identify non-running realized tasks for removal
    for (const task of this._tasks.values()) {
      if (task.batchId && batchIds.includes(task.batchId) && task.getStatus() !== 'running') {
        tasksToPurge.push(task);
      }
    }
    
    if (tasksToPurge.length > 0) {
        this._logger.info(`Found ${tasksToPurge.length} non-running tasks in selected batches to clear.`);
        for (const task of tasksToPurge) {
            task.purgeData();
            this._tasks.delete(task._id);
            newMetricsMap.delete(task._id); // Also remove from the metrics map
            this._logger.debug(`Cleared and purged task ${task._id}.`);
        }
    }
    
    // Update with the cleaned map
    this._taskMetricsInternal$.next(newMetricsMap);

    await this._saveStateToStorage();
    this._logger.info(`Finished clearing selected tasks for batches: ${batchIds.join(', ')}`);
  }

  /**
   * 清理旧的已完成【批处理任务】记录，防止内存无限增长。
   * 此方法现在按 `batchId` 工作。
   */
  private _pruneCompletedTasks(): void {
    const metricsMap = this._taskMetricsInternal$.value;
    if (metricsMap.size === 0) return;

    // 1. 按 batchId 对所有任务进行分组
    const batches = new Map<string, TaskMetrics[]>();
    for (const metric of metricsMap.values()) {
      if (!batches.has(metric.batchId)) {
        batches.set(metric.batchId, []);
      }
      batches.get(metric.batchId)!.push(metric);
    }

    // 2. 识别出完全完成的批处理任务
    const completedBatches: { batchId: string, completedAt: number }[] = [];
    for (const [batchId, tasks] of batches.entries()) {
      const isBatchComplete = tasks.every(t => ['completed', 'failed', 'cancelled'].includes(t.status));
      if (isBatchComplete) {
        // 计算批处理的最后完成时间（即批内最晚结束的任务的时间）
        const lastCompletedTime = Math.max(...tasks.map(t => t.endTime ?? 0));
        completedBatches.push({ batchId, completedAt: lastCompletedTime });
      }
    }

    // 3. 如果完成的批处理任务数超过限制，则进行清理
    if (completedBatches.length > MAX_COMPLETED_BATCHS_TO_KEEP) {
      this._logger.info(`已完成的批处理任务数 (${completedBatches.length}) 超过限制 (${MAX_COMPLETED_BATCHS_TO_KEEP})，开始清理...`);
      
      // 4. 按完成时间升序排序，最早完成的在前
      completedBatches.sort((a, b) => a.completedAt - b.completedAt);
      
      const batchesToRemoveCount = completedBatches.length - MAX_COMPLETED_BATCHS_TO_KEEP;
      const batchesToRemove = completedBatches.slice(0, batchesToRemoveCount);
      
      const newMetricsMap = new Map(metricsMap);
      let tasksRemovedCount = 0;
      for (const batchToRemove of batchesToRemove) {
        const tasksInBatch = batches.get(batchToRemove.batchId) || [];
        for (const taskMetric of tasksInBatch) {
          const taskToPurge = this._tasks.get(taskMetric.id);
          if (taskToPurge) {
            taskToPurge.purgeData();
          }
          newMetricsMap.delete(taskMetric.id);
          this._tasks.delete(taskMetric.id);
          tasksRemovedCount++;
        }
        this._logger.debug(`已清理旧批处理任务: ${batchToRemove.batchId}`);
      }
      
      this._taskMetricsInternal$.next(newMetricsMap);
      this._logger.info(`清理完成，移除了 ${batchesToRemove.length} 个旧批处理任务 (共 ${tasksRemovedCount} 个子任务)。`);
    }
  }

  /**
   * 按需增加指定数量的槽位和对应的浏览器标签页。
   * @param count 要增加的槽位数量。
   */
  private async _addSlots(count: number): Promise<void> {
    if (count <= 0) return;

    this._logger.info(`正在尝试添加 ${count} 个新槽位...`);
    
    for (let i = 0; i < count; i++) {
      // 为了确保ID的唯一性，我们总是基于现有最大ID进行递增
      const newSlotId = (this._slots.length > 0 ? Math.max(...this._slots.map(s => s.id)) : -1) + 1;
      
      try {
        const tab = await this._workerWindowManager.createTab({ active: false, url: 'about:blank' });
        if (tab.id) {
          this._slots.push({
            id: newSlotId,
            tabId: tab.id,
            isBusy: false,
            currentTask: null,
            lastDomain: null,
          });
          this._logger.info(`新槽位 ${newSlotId} (Tab ID ${tab.id}) 已创建并添加。`);
        } else {
          this._logger.error(`为新槽位创建标签页失败，未返回 tab ID。`);
        }
      } catch (error) {
        this._logger.error(`创建新槽位 ${newSlotId} 时出错:`, error);
      }
    }
  }
  
  /**
   * 关闭所有活动的槽位及其关联的标签页。
   * 这通常在所有任务完成后调用。
   */
  private async _shutdownAllSlots(): Promise<void> {
    // 停止资源监控，因为所有任务都将结束
    if (this._resourceMonitor.isMonitoring()) {
      this._logger.info('All work is done. Stopping resource monitor.');
      this._resourceMonitor.stop();
    }
    
    if (this._slots.length === 0) {
      this._logger.debug('No slots to shut down.');
      return;
    }

    this._logger.info(`正在关闭所有 ${this._slots.length} 个槽位...`);
    // 复制一份ID列表，因为我们将要修改 this._slots 数组
    const slotsToClose = [...this._slots];
    this._slots.length = 0; // 立即清空，防止竞态条件

    const removalPromises = slotsToClose.map(slot => 
      this._workerWindowManager.removeTab(slot.tabId)
        .catch(e => this._logger.warn(`关闭标签页 ${slot.tabId} 失败，可能已被关闭:`, e))
    );
    
    await Promise.all(removalPromises);
    this._logger.info('所有槽位均已成功关闭。');
  }

}