"""
Redis客户端工具
"""

import redis.asyncio as redis
from typing import Optional, Any, Dict
import json
import pickle
from datetime import timedelta

from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class RedisClient:
    """Redis客户端封装"""

    def __init__(self):
        self._client: Optional[redis.Redis] = None

    async def connect(self):
        """连接Redis"""
        try:
            self._client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # 测试连接
            await self._client.ping()
            logger.info("Redis connected successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

    async def disconnect(self):
        """断开Redis连接"""
        if self._client:
            await self._client.close()
            logger.info("Redis disconnected")

    async def get(self, key: str) -> Optional[str]:
        """获取值"""
        try:
            return await self._client.get(key)
        except Exception as e:
            logger.error(f"Redis GET error for key {key}: {e}")
            return None

    async def set(
        self,
        key: str,
        value: str,
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False,
    ) -> bool:
        """设置值"""
        try:
            return await self._client.set(key, value, ex=ex, px=px, nx=nx, xx=xx)
        except Exception as e:
            logger.error(f"Redis SET error for key {key}: {e}")
            return False

    async def setex(self, key: str, time: int, value: str) -> bool:
        """设置带过期时间的值"""
        try:
            return await self._client.setex(key, time, value)
        except Exception as e:
            logger.error(f"Redis SETEX error for key {key}: {e}")
            return False

    async def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            return await self._client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis DELETE error for keys {keys}: {e}")
            return 0

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return bool(await self._client.exists(key))
        except Exception as e:
            logger.error(f"Redis EXISTS error for key {key}: {e}")
            return False

    async def expire(self, key: str, time: int) -> bool:
        """设置过期时间"""
        try:
            return await self._client.expire(key, time)
        except Exception as e:
            logger.error(f"Redis EXPIRE error for key {key}: {e}")
            return False

    async def ttl(self, key: str) -> int:
        """获取剩余生存时间"""
        try:
            return await self._client.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL error for key {key}: {e}")
            return -1

    async def incr(self, key: str, amount: int = 1) -> int:
        """递增"""
        try:
            return await self._client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis INCR error for key {key}: {e}")
            return 0

    async def decr(self, key: str, amount: int = 1) -> int:
        """递减"""
        try:
            return await self._client.decr(key, amount)
        except Exception as e:
            logger.error(f"Redis DECR error for key {key}: {e}")
            return 0

    async def hget(self, name: str, key: str) -> Optional[str]:
        """获取哈希字段值"""
        try:
            return await self._client.hget(name, key)
        except Exception as e:
            logger.error(f"Redis HGET error for {name}.{key}: {e}")
            return None

    async def hset(self, name: str, key: str, value: str) -> int:
        """设置哈希字段值"""
        try:
            return await self._client.hset(name, key, value)
        except Exception as e:
            logger.error(f"Redis HSET error for {name}.{key}: {e}")
            return 0

    async def hgetall(self, name: str) -> Dict[str, str]:
        """获取所有哈希字段"""
        try:
            return await self._client.hgetall(name)
        except Exception as e:
            logger.error(f"Redis HGETALL error for {name}: {e}")
            return {}

    async def lpush(self, name: str, *values: str) -> int:
        """左推入列表"""
        try:
            return await self._client.lpush(name, *values)
        except Exception as e:
            logger.error(f"Redis LPUSH error for {name}: {e}")
            return 0

    async def rpop(self, name: str) -> Optional[str]:
        """右弹出列表"""
        try:
            return await self._client.rpop(name)
        except Exception as e:
            logger.error(f"Redis RPOP error for {name}: {e}")
            return None

    async def llen(self, name: str) -> int:
        """获取列表长度"""
        try:
            return await self._client.llen(name)
        except Exception as e:
            logger.error(f"Redis LLEN error for {name}: {e}")
            return 0

    async def ping(self) -> bool:
        """测试连接"""
        try:
            await self._client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis PING error: {e}")
            return False

    async def set_json(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """设置JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return await self.set(key, json_str, ex=ex)
        except Exception as e:
            logger.error(f"Redis SET_JSON error for key {key}: {e}")
            return False

    async def get_json(self, key: str) -> Optional[Any]:
        """获取JSON值"""
        try:
            json_str = await self.get(key)
            if json_str:
                return json.loads(json_str)
            return None
        except Exception as e:
            logger.error(f"Redis GET_JSON error for key {key}: {e}")
            return None


# 全局Redis客户端实例
_redis_client: Optional[RedisClient] = None


async def get_redis_client() -> RedisClient:
    """获取Redis客户端"""
    global _redis_client

    if _redis_client is None:
        _redis_client = RedisClient()
        await _redis_client.connect()

    return _redis_client


async def close_redis_client():
    """关闭Redis客户端"""
    global _redis_client

    if _redis_client:
        await _redis_client.disconnect()
        _redis_client = None
