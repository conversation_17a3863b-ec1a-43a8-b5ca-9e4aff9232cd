"""
任务调度服务
继承browser-viewer的智能调度算法
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

from app.models.task import Task, TaskStatus
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ResourceRequirements:
    """资源需求"""

    cpu: float = 1.0
    memory: int = 512  # MB
    concurrent_limit: int = 1
    domain_affinity: Optional[str] = None


@dataclass
class SlotInfo:
    """槽位信息"""

    slot_id: str
    allocated_cpu: float
    allocated_memory: int
    domain: Optional[str]
    created_at: datetime


@dataclass
class ScheduleResult:
    """调度结果"""

    success: bool
    slot_id: Optional[str] = None
    estimated_start_time: Optional[datetime] = None
    queue_position: Optional[int] = None
    error_message: Optional[str] = None


class TaskSchedulerService:
    """任务调度服务 - 继承browser-viewer的智能调度算法"""

    def __init__(self, max_concurrent_tasks: int = 10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.active_slots: Dict[str, SlotInfo] = {}
        self.task_queue: List[Task] = []
        self.domain_slots: Dict[str, List[str]] = {}  # 域名亲和性槽位
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_tasks)
        self._lock = threading.Lock()
        self._running = False

    async def start_scheduler(self):
        """启动调度器"""
        if self._running:
            return

        self._running = True
        logger.info("Task scheduler started")

        # 启动调度循环
        asyncio.create_task(self._schedule_loop())

    async def stop_scheduler(self):
        """停止调度器"""
        self._running = False
        self.executor.shutdown(wait=True)
        logger.info("Task scheduler stopped")

    async def schedule_task(self, task: Task) -> ScheduleResult:
        """智能调度任务"""
        try:
            requirements = self._extract_requirements(task)

            # 检查是否有可用槽位
            slot_id = await self._find_available_slot(requirements)

            if slot_id:
                # 立即分配槽位
                await self._allocate_slot(slot_id, task, requirements)
                return ScheduleResult(
                    success=True,
                    slot_id=slot_id,
                    estimated_start_time=datetime.utcnow(),
                )
            else:
                # 加入队列等待
                with self._lock:
                    self.task_queue.append(task)
                    queue_position = len(self.task_queue)

                estimated_time = self._estimate_start_time(queue_position)

                return ScheduleResult(
                    success=True,
                    queue_position=queue_position,
                    estimated_start_time=estimated_time,
                )

        except Exception as e:
            logger.error(f"Failed to schedule task {task.id}: {e}")
            return ScheduleResult(success=False, error_message=str(e))

    async def acquire_slot(
        self, requirements: ResourceRequirements
    ) -> Optional[SlotInfo]:
        """获取槽位"""
        slot_id = await self._find_available_slot(requirements)
        if not slot_id:
            return None

        slot_info = SlotInfo(
            slot_id=slot_id,
            allocated_cpu=requirements.cpu,
            allocated_memory=requirements.memory,
            domain=requirements.domain_affinity,
            created_at=datetime.utcnow(),
        )

        with self._lock:
            self.active_slots[slot_id] = slot_info

        return slot_info

    async def release_slot(self, slot_id: str) -> None:
        """释放槽位"""
        with self._lock:
            if slot_id in self.active_slots:
                slot_info = self.active_slots[slot_id]
                del self.active_slots[slot_id]

                # 更新域名槽位映射
                if slot_info.domain:
                    if slot_info.domain in self.domain_slots:
                        if slot_id in self.domain_slots[slot_info.domain]:
                            self.domain_slots[slot_info.domain].remove(slot_id)

                logger.info(f"Released slot: {slot_id}")

        # 触发队列调度
        await self._process_queue()

    def adjust_concurrency(self, new_limit: int) -> None:
        """动态调整并发限制"""
        old_limit = self.max_concurrent_tasks
        self.max_concurrent_tasks = new_limit

        logger.info(f"Concurrency limit adjusted: {old_limit} -> {new_limit}")

        # 如果增加了限制，尝试处理队列
        if new_limit > old_limit:
            asyncio.create_task(self._process_queue())

    def apply_throttling(self, level: str) -> None:
        """应用限流策略"""
        throttle_configs = {
            "low": {"limit": self.max_concurrent_tasks},
            "medium": {"limit": max(1, self.max_concurrent_tasks // 2)},
            "high": {"limit": max(1, self.max_concurrent_tasks // 4)},
        }

        if level in throttle_configs:
            new_limit = throttle_configs[level]["limit"]
            self.adjust_concurrency(new_limit)
            logger.info(f"Applied throttling level: {level}")

    def get_scheduler_stats(self) -> Dict:
        """获取调度器统计信息"""
        with self._lock:
            return {
                "active_slots": len(self.active_slots),
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "queue_length": len(self.task_queue),
                "domain_slots": {
                    domain: len(slots) for domain, slots in self.domain_slots.items()
                },
                "utilization": (
                    len(self.active_slots) / self.max_concurrent_tasks
                    if self.max_concurrent_tasks > 0
                    else 0
                ),
            }

    async def _schedule_loop(self):
        """调度循环"""
        while self._running:
            try:
                await self._process_queue()
                await asyncio.sleep(1)  # 每秒检查一次
            except Exception as e:
                logger.error(f"Scheduler loop error: {e}")
                await asyncio.sleep(5)

    async def _process_queue(self):
        """处理任务队列"""
        if not self.task_queue:
            return

        with self._lock:
            tasks_to_process = self.task_queue.copy()

        for task in tasks_to_process:
            if len(self.active_slots) >= self.max_concurrent_tasks:
                break

            requirements = self._extract_requirements(task)
            slot_id = await self._find_available_slot(requirements)

            if slot_id:
                await self._allocate_slot(slot_id, task, requirements)

                with self._lock:
                    if task in self.task_queue:
                        self.task_queue.remove(task)

                # 启动任务执行
                asyncio.create_task(self._execute_task(task, slot_id))

    async def _find_available_slot(
        self, requirements: ResourceRequirements
    ) -> Optional[str]:
        """查找可用槽位"""
        with self._lock:
            # 检查总槽位限制
            if len(self.active_slots) >= self.max_concurrent_tasks:
                return None

            # 域名亲和性检查
            if requirements.domain_affinity:
                domain_slots = self.domain_slots.get(requirements.domain_affinity, [])

                # 如果该域名已有槽位，优先使用同域名槽位
                for slot_id in domain_slots:
                    if slot_id not in self.active_slots:
                        return slot_id

            # 生成新槽位ID
            slot_id = f"slot_{datetime.utcnow().timestamp()}_{len(self.active_slots)}"
            return slot_id

    async def _allocate_slot(
        self, slot_id: str, task: Task, requirements: ResourceRequirements
    ):
        """分配槽位"""
        slot_info = SlotInfo(
            slot_id=slot_id,
            allocated_cpu=requirements.cpu,
            allocated_memory=requirements.memory,
            domain=requirements.domain_affinity,
            created_at=datetime.utcnow(),
        )

        with self._lock:
            self.active_slots[slot_id] = slot_info

            # 更新域名槽位映射
            if requirements.domain_affinity:
                if requirements.domain_affinity not in self.domain_slots:
                    self.domain_slots[requirements.domain_affinity] = []
                self.domain_slots[requirements.domain_affinity].append(slot_id)

        logger.info(f"Allocated slot {slot_id} for task {task.id}")

    def _extract_requirements(self, task: Task) -> ResourceRequirements:
        """从任务配置中提取资源需求"""
        config = task.config or {}

        return ResourceRequirements(
            cpu=config.get("cpu", 1.0),
            memory=config.get("memory", 512),
            concurrent_limit=config.get("concurrent_limit", 1),
            domain_affinity=config.get("domain"),
        )

    def _estimate_start_time(self, queue_position: int) -> datetime:
        """估算任务开始时间"""
        # 简单估算：假设每个任务平均执行5分钟
        estimated_delay = queue_position * 5
        return datetime.utcnow() + timedelta(minutes=estimated_delay)

    async def _execute_task(self, task: Task, slot_id: str):
        """执行任务（占位实现）"""
        try:
            logger.info(f"Executing task {task.id} in slot {slot_id}")

            # 这里应该调用实际的任务执行逻辑
            # 目前只是模拟执行
            await asyncio.sleep(1)

            logger.info(f"Task {task.id} completed in slot {slot_id}")

        except Exception as e:
            logger.error(f"Task execution error: {e}")
        finally:
            # 释放槽位
            await self.release_slot(slot_id)
