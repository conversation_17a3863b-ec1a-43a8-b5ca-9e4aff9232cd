#!/usr/bin/env node

/**
 * 构建后处理脚本
 * 1. 复制HTML文件到正确位置
 * 2. 复制manifest.json
 * 3. 复制静态资源
 * 4. 生成构建报告
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const distDir = path.join(rootDir, 'dist');

async function postBuild() {
  console.log('🔧 执行构建后处理...');
  
  try {
    // 1. 复制HTML文件到根目录
    const htmlFiles = [
      { src: 'src/ui/popup/popup.html', dest: 'popup.html' },
      { src: 'src/ui/options/options.html', dest: 'options.html' },
      { src: 'src/background/offscreen.html', dest: 'offscreen.html' }
    ];
    
    for (const file of htmlFiles) {
      const srcPath = path.join(distDir, file.src);
      const destPath = path.join(distDir, file.dest);
      
      if (await fs.pathExists(srcPath)) {
        await fs.copy(srcPath, destPath);
        console.log(`✅ 复制 ${file.src} -> ${file.dest}`);
      }
    }
    
    // 2. 复制manifest.json
    const manifestSrc = path.join(rootDir, 'manifest.json');
    const manifestDest = path.join(distDir, 'manifest.json');
    
    if (await fs.pathExists(manifestSrc)) {
      await fs.copy(manifestSrc, manifestDest);
      console.log('✅ 复制 manifest.json');
    }
    
    // 3. 复制public目录内容
    const publicDir = path.join(rootDir, 'public');
    if (await fs.pathExists(publicDir)) {
      await fs.copy(publicDir, distDir, {
        filter: (src) => {
          // 跳过已经存在的文件
          const relativePath = path.relative(publicDir, src);
          const destPath = path.join(distDir, relativePath);
          return !fs.existsSync(destPath);
        }
      });
      console.log('✅ 复制 public 目录内容');
    }

    // 4. 生成构建信息
    const buildInfo = {
      buildTime: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      nodeVersion: process.version,
      platform: process.platform,
      mode: process.env.NODE_ENV || 'production'
    };
    
    const buildInfoPath = path.join(distDir, 'build-info.json');
    await fs.writeJson(buildInfoPath, buildInfo, { spaces: 2 });
    console.log('✅ 生成构建信息');
    
    // 5. 验证关键文件
    const requiredFiles = [
      'manifest.json',
      'serviceWorker.js',
      'src/content-scripts/main.js',
      'popup.html',
      'options.html',
      'offscreen.html'
    ];
    
    console.log('\n📋 验证构建结果:');
    let allFilesExist = true;
    
    for (const file of requiredFiles) {
      const filePath = path.join(distDir, file);
      const exists = await fs.pathExists(filePath);
      const status = exists ? '✅' : '❌';
      console.log(`${status} ${file}`);
      
      if (!exists) {
        allFilesExist = false;
      }
    }
    
    if (allFilesExist) {
      console.log('\n🎉 构建后处理完成！所有文件就绪。');
      
      // 显示dist目录大小
      const stats = await getDirectorySize(distDir);
      console.log(`📦 构建输出大小: ${formatBytes(stats.size)}`);
      console.log(`📁 文件数量: ${stats.files}个文件`);
      
    } else {
      console.log('\n❌ 构建后处理完成，但缺少部分文件！');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 构建后处理失败:', error);
    process.exit(1);
  }
}

async function getDirectorySize(dir) {
  let size = 0;
  let files = 0;
  
  const items = await fs.readdir(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stat = await fs.stat(itemPath);
    
    if (stat.isDirectory()) {
      const subStats = await getDirectorySize(itemPath);
      size += subStats.size;
      files += subStats.files;
    } else {
      size += stat.size;
      files++;
    }
  }
  
  return { size, files };
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

// 执行构建后处理
postBuild();