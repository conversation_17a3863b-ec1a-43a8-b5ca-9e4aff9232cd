/**
 * 一个简单的状态控制器，用于向 ResourceMonitor 发出信号，
 * 表明当前正在执行一个有意的、长时间的暂停。
 * 这可以帮助监控器避免将模拟人类行为的延迟误报为"长任务"性能问题。
 */
export class PerformanceInterruptionController {
  private static _isPaused = false;

  /**
   * 通知监控系统，一个有意图的暂停已经开始。
   */
  public static pause(): void {
    this._isPaused = true;
  }

  /**
   * 通知监控系统，有意图的暂停已经结束。
   */
  public static resume(): void {
    this._isPaused = false;
  }

  /**
   * 检查当前是否处于一个有意图的暂停状态。
   * @returns {boolean} 如果是，则返回 true。
   */
  public static isPaused(): boolean {
    return this._isPaused;
  }
} 