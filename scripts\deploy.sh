#!/bin/bash

# 数懒平台部署脚本
# 用于生产环境部署

set -e

# 配置变量
PROJECT_NAME="digital-lazy-platform"
DOCKER_REGISTRY="your-registry.com"
VERSION=${1:-"latest"}
ENVIRONMENT=${2:-"production"}

echo "🚀 开始部署数懒平台 - 版本: $VERSION, 环境: $ENVIRONMENT"

# 检查必要工具
check_requirements() {
    echo "📋 检查部署要求..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装"
        exit 1
    fi
    
    echo "✅ 部署要求检查完成"
}

# 构建镜像
build_images() {
    echo "🔨 构建Docker镜像..."
    
    # 构建后端镜像
    echo "构建后端镜像..."
    docker build -t $PROJECT_NAME-backend:$VERSION ./backend
    
    # 构建前端镜像
    echo "构建前端镜像..."
    docker build -t $PROJECT_NAME-frontend:$VERSION ./frontend
    
    echo "✅ 镜像构建完成"
}

# 推送镜像到仓库
push_images() {
    if [ "$DOCKER_REGISTRY" != "your-registry.com" ]; then
        echo "📤 推送镜像到仓库..."
        
        # 标记镜像
        docker tag $PROJECT_NAME-backend:$VERSION $DOCKER_REGISTRY/$PROJECT_NAME-backend:$VERSION
        docker tag $PROJECT_NAME-frontend:$VERSION $DOCKER_REGISTRY/$PROJECT_NAME-frontend:$VERSION
        
        # 推送镜像
        docker push $DOCKER_REGISTRY/$PROJECT_NAME-backend:$VERSION
        docker push $DOCKER_REGISTRY/$PROJECT_NAME-frontend:$VERSION
        
        echo "✅ 镜像推送完成"
    else
        echo "⚠️  跳过镜像推送（未配置仓库地址）"
    fi
}

# 部署到生产环境
deploy_production() {
    echo "🌐 部署到生产环境..."
    
    # 创建生产环境配置
    cat > docker-compose.prod.yml << EOF
version: '3.8'

services:
  # 数据库
  postgres:
    image: postgres:15
    container_name: ${PROJECT_NAME}-postgres
    environment:
      POSTGRES_DB: digital_lazy_platform
      POSTGRES_USER: \${DB_USER}
      POSTGRES_PASSWORD: \${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: ${PROJECT_NAME}-redis
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 后端API
  backend:
    image: ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${VERSION}
    container_name: ${PROJECT_NAME}-backend
    environment:
      - DATABASE_URL=postgresql://\${DB_USER}:\${DB_PASSWORD}@postgres:5432/digital_lazy_platform
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=\${SECRET_KEY}
      - DEBUG=false
    depends_on:
      - postgres
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # 前端应用
  frontend:
    image: ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${VERSION}
    container_name: ${PROJECT_NAME}-frontend
    environment:
      - VITE_API_BASE_URL=\${API_BASE_URL}
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ${PROJECT_NAME}-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
EOF

    # 创建Nginx配置
    cat > nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name \${DOMAIN_NAME};

        # API请求转发到后端
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # WebSocket支持
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # 前端应用
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF

    # 创建环境变量文件模板
    cat > .env.prod.example << EOF
# 生产环境配置
DB_USER=your_db_user
DB_PASSWORD=your_db_password
SECRET_KEY=your_secret_key_here
API_BASE_URL=https://your-domain.com/api
DOMAIN_NAME=your-domain.com
EOF

    echo "✅ 生产环境配置创建完成"
    echo "⚠️  请复制 .env.prod.example 为 .env.prod 并填写实际配置"
}

# 启动服务
start_services() {
    echo "🚀 启动服务..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ ! -f ".env.prod" ]; then
            echo "❌ 生产环境配置文件 .env.prod 不存在"
            echo "请复制 .env.prod.example 为 .env.prod 并填写配置"
            exit 1
        fi
        
        # 使用生产环境配置启动
        docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
    else
        # 使用开发环境配置启动
        docker-compose up -d
    fi
    
    echo "✅ 服务启动完成"
}

# 健康检查
health_check() {
    echo "🏥 执行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查后端API
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端API健康检查通过"
    else
        echo "❌ 后端API健康检查失败"
        return 1
    fi
    
    # 检查前端应用
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ 前端应用健康检查通过"
    else
        echo "❌ 前端应用健康检查失败"
        return 1
    fi
    
    echo "✅ 所有服务健康检查通过"
}

# 备份数据
backup_data() {
    echo "💾 备份数据..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份数据库
    docker exec ${PROJECT_NAME}-postgres pg_dump -U user digital_lazy_platform > $BACKUP_DIR/database.sql
    
    # 备份上传文件
    if [ -d "uploads" ]; then
        cp -r uploads $BACKUP_DIR/
    fi
    
    echo "✅ 数据备份完成: $BACKUP_DIR"
}

# 回滚部署
rollback() {
    echo "🔄 回滚到上一个版本..."
    
    PREVIOUS_VERSION=${3:-"previous"}
    
    # 停止当前服务
    docker-compose down
    
    # 使用上一个版本的镜像
    sed -i "s/:$VERSION/:$PREVIOUS_VERSION/g" docker-compose.prod.yml
    
    # 重新启动
    start_services
    
    echo "✅ 回滚完成"
}

# 清理旧镜像
cleanup() {
    echo "🧹 清理旧镜像..."
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除旧版本镜像（保留最近3个版本）
    docker images $PROJECT_NAME-backend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi
    docker images $PROJECT_NAME-frontend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi
    
    echo "✅ 清理完成"
}

# 显示帮助信息
show_help() {
    echo "数懒平台部署脚本使用说明："
    echo ""
    echo "用法: $0 [版本] [环境] [操作]"
    echo ""
    echo "参数："
    echo "  版本    Docker镜像版本标签 (默认: latest)"
    echo "  环境    部署环境 (development|production, 默认: production)"
    echo "  操作    执行的操作 (deploy|rollback|backup|cleanup|help)"
    echo ""
    echo "示例："
    echo "  $0 v1.0.0 production deploy    # 部署v1.0.0到生产环境"
    echo "  $0 latest development deploy   # 部署latest到开发环境"
    echo "  $0 v1.0.0 production rollback # 回滚生产环境"
    echo "  $0 backup                      # 备份数据"
    echo ""
}

# 主函数
main() {
    local action=${3:-"deploy"}
    
    case $action in
        "deploy")
            check_requirements
            build_images
            push_images
            deploy_production
            start_services
            health_check
            ;;
        "rollback")
            rollback
            ;;
        "backup")
            backup_data
            ;;
        "cleanup")
            cleanup
            ;;
        "help")
            show_help
            ;;
        *)
            echo "❌ 未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
