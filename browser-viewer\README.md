# 数懒孪生代理 - 浏览器智能代理插件

[![TypeScript](https://img.shields.io/badge/TypeScript-5.2-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-4.5-646CFF.svg)](https://vitejs.dev/)
[![Vue](https://img.shields.io/badge/Vue-3.3-4FC08D.svg)](https://vuejs.org/)

## 项目概述

数懒孪生代理是一个高性能的浏览器插件，提供智能化的用户行为模拟和任务自动化功能。基于Manifest V3标准，采用企业级架构设计。

### 核心特性

- 🤖 **智能行为模拟**: 基于贝塞尔曲线的人类行为模拟
- 🔄 **双模式渲染**: 无缝切换无头/可视化模式
- ⚡ **并发任务管理**: 资源感知的智能调度
- 🎯 **视觉处理系统**: 分块截图与AI内容识别
- 🛡️ **异常检测**: 实时监控与自动恢复
- 🔒 **隐私保护**: 数据最小化与用户控制

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- Chrome/Edge >= 88
- 推荐使用 pnpm 或 npm

### 安装依赖

```bash

# 克隆项目

git clone <your-repo-url>
cd browser-viewer

# 安装依赖

npm install
```

## 开发指南

### 🔧 开发环境

#### 启动开发模式

```bash

# 方式1: 文件监听模式 (推荐)

npm run dev

# 方式2: 开发服务器模式

npm run dev:serve
```

**开发模式特性**:

- ✅ **SourceMap**: 启用inline sourcemap，便于调试
- ✅ **热重载**: 代码变更自动重新构建
- ✅ **详细日志**: 完整的构建和运行时日志
- ✅ **快速构建**: 关闭压缩和优化，提升构建速度
- ✅ **开发工具**: 保留console和debugger语句

#### 加载到浏览器

1. 运行 `npm run dev` 启动开发构建
2. 打开 Chrome/Edge 浏览器
3. 访问 `chrome://extensions/`
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择本项目的 `dist` 文件夹

#### 实时开发

- 代码修改后会自动重新构建
- 在扩展管理页面点击刷新按钮即可加载最新版本
- 或使用快捷键 `Ctrl+R` 重新加载扩展

### 🚀 生产构建

#### 构建命令

```bash

# 生产构建 (推荐)

npm run build

# 开发版本构建

npm run build:dev

# 构建 + 分析包大小

npm run build:analyze
```

**生产模式特性**:

- ❌ **SourceMap**: 关闭sourcemap，减小文件大小
- ✅ **代码压缩**: 使用ESBuild压缩代码
- ✅ **Tree Shaking**: 移除未使用的代码
- ✅ **资源优化**: CSS/JS/HTML压缩和优化
- ❌ **开发代码**: 移除console.log和debugger语句
- ✅ **文件Hash**: 启用文件内容hash，支持缓存控制

#### 构建产物

生产构建完成后，`dist` 目录结构如下：

```tree
dist/
├── manifest.json           # 插件清单文件
├── background.js           # Service Worker (58KB)
├── content-script.js       # 内容脚本 (8KB)
├── popup.html             # 弹出页面
├── popup.js               # 弹出页面脚本 (6KB)
├── options.html           # 设置页面  
├── options.js             # 设置页面脚本 (4KB)
├── offscreen.html         # 无头文档
├── offscreen.js           # 无头脚本 (1KB)
├── chunks/                # 代码分块
├── icons/                 # 图标文件
└── build-info.json        # 构建信息
```

### 📝 代码规范

```bash

# 检查代码规范

npm run lint

# 自动修复代码规范问题

npm run lint:fix

# TypeScript类型检查

npm run type-check
```

## 项目架构

### 技术栈

- **核心框架**: TypeScript + Vite + Vue 3
- **状态管理**: RxJS
- **UI框架**: Tailwind CSS
- **测试框架**: Jest
- **代码规范**: ESLint + Prettier
- **构建工具**: Vite + ESBuild

### 目录结构

```tree
browser-viewer/
├── dist/                       # 构建输出
├── public/                     # 公共素材
├── scripts/                    # 构建脚本
├── src/
│   ├── background/             # Service Worker
│   ├── content-scripts/        # 内容脚本
│   ├── core/                   # 核心引擎
│   │   ├── task-manager/       # 任务调度中心
│   │   ├── behavior-engine/    # 行为模拟
│   │   └── capture-system/     # 视觉处理
│   ├── services/               # 微服务层
│   │   ├── vision-service/     # 视觉服务
│   │   ├── auth-service/       # 认证服务
│   │   └── exception-service/  # 异常服务
│   ├── shared/                 # 共享资源
│   │   ├── types/              # 类型定义
│   │   ├── constants/          # 常量
│   │   └── utils/              # 工具函数
│   ├── ui/                     # 用户界面
│   │   ├── popup/              # 弹出页面
│   │   └── options/            # 设置页面
│   └── workers/                # Web Workers
├── tests/                      # 测试套件
├── eslint.config.js            # 代码风格
├── manifest.json               # 插件清单
├── package.json                # 依赖版本
├── tailwind.config.ts          # 样式配置
├── tsconfig.json               # 开发配置
├── vite.config.ts              # 构建配置
└── vitest.config.ts            # 测试配置
```

### 核心模块

#### 1. 任务调度中心 (Task Manager)

- **TaskScheduler**: 任务调度器
- **ResourceMonitor**: 资源监控
- **ConcurrentScheduler**: 并发调度器

#### 2. 行为模拟引擎 (Behavior Engine)  

- **HumanBehaviorSimulator**: 人类行为模拟器
- **BehaviorEngine**: 行为引擎主控制器
- **RenderingModeManager**: 渲染模式管理器

#### 3. 视觉处理系统 (Capture System)

- **ImageProcessor**: 图像处理器
- **ContentExtractor**: 内容提取器
- **AIIntegration**: AI服务集成

## 开发环境配置对比

| 配置项 | 开发环境 (dev) | 生产环境 (build) |
|--------|---------------|------------------|
| **SourceMap** | ✅ inline | ❌ 关闭 |
| **代码压缩** | ❌ 关闭 | ✅ ESBuild |
| **Tree Shaking** | ❌ 关闭 | ✅ 启用 |
| **Console日志** | ✅ 保留 | ❌ 移除 |
| **文件Hash** | ❌ 关闭 | ✅ 启用 |
| **构建速度** | 🚀 快速 | 🐌 完整优化 |
| **文件大小** | 📦 较大 | 📦 最小化 |

## 环境变量

### 开发环境 (.env.development)

```env
NODE_ENV=development
VITE_APP_NAME=数懒孪生代理 (开发版)
VITE_LOG_LEVEL=debug
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEVTOOLS=true
```

### 生产环境 (.env.production)

```env
NODE_ENV=production  
VITE_APP_NAME=数懒孪生代理
VITE_LOG_LEVEL=warn
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false
```

### 🧪 测试系统

本项目采用现代化的测试框架，基于 **Vitest** 构建，提供全面的测试覆盖和优秀的开发体验。

#### 测试架构

```tree
tests/
├── setup/                          # 测试环境配置
│   ├── global-setup.ts             # 全局设置和模拟
│   └── chrome-api-setup.ts         # Chrome Extension API模拟
├── utils/                          # 测试工具库
│   └── test-helpers.ts             # 通用测试工具函数
├── fixtures/                       # 测试数据和常量
│   └── test-constants.ts           # 测试常量定义
├── unit/                           # 单元测试
│   ├── core/
│   │   ├── task-manager/           # 任务管理器测试
│   │   │   ├── Task.test.ts
│   │   │   └── TaskScheduler.test.ts
│   │   └── behavior-engine/        # 行为引擎测试
│   │       └── BehaviorEngine.test.ts
│   ├── services/                   # 服务层测试
│   └── shared/                     # 共享模块测试
├── integration/                    # 集成测试
│   ├── core/
│   │   └── task-system-integration.test.ts
│   └── services/
│       └── service-communication.test.ts
├── e2e/                           # 端到端测试
│   ├── extension-workflow.test.ts  # 完整插件工作流程
│   └── behavior-simulation.test.ts # 行为模拟端到端
└── run-tests.ts                   # 测试运行脚本
```

#### 测试命令

```bash
# 🔬 基础测试命令
npm test                      # 运行所有测试
npm run test:watch            # 监听模式运行测试
npm run test:coverage         # 生成覆盖率报告

# 📊 分类测试命令
npm run test:unit             # 仅运行单元测试
npm run test:integration      # 仅运行集成测试
npm run test:e2e              # 仅运行端到端测试

# 🎯 高级测试选项
npm test -- --unit            # 运行单元测试
npm test -- --coverage        # 带覆盖率运行
npm test -- --watch           # 监听模式
npm test -- --verbose         # 详细输出
npm test -- --pattern="Task"  # 运行匹配模式的测试
```

#### 测试覆盖范围

**🔥 单元测试 (Unit Tests)**:

- Task管理器核心逻辑测试
- TaskScheduler调度算法测试
- BehaviorEngine行为模拟测试
- 各服务模块独立功能测试
- 工具函数和帮助类测试

**🔗 集成测试 (Integration Tests)**:

- 任务系统组件间协作测试
- 服务间通信和数据流测试
- 资源监控和动态调整测试
- 错误传播和恢复机制测试

**🌍 端到端测试 (E2E Tests)**:

- 完整插件工作流程测试
- 用户交互和界面测试
- 无头模式和可视化模式测试
- 行为模拟真实性测试
- 异常检测和恢复测试

#### 运行示例

```bash
# 查看测试统计信息
npm test -- --help

# 运行特定测试文件
npm test -- tests/unit/core/task-manager/Task.test.ts

# 以监听模式运行单元测试
npm test -- --unit --watch

# 生成详细的覆盖率报告
npm test -- --coverage --verbose

# 运行包含"BehaviorEngine"的测试
npm test -- --pattern="BehaviorEngine"
```

#### 测试配置

测试系统通过 `vitest.config.ts` 进行配置：

```typescript
// 主要配置项
{
  test: {
    environment: 'jsdom',                    // DOM模拟环境
    setupFiles: [                           // 测试设置文件
      'tests/setup/global-setup.ts',
      'tests/setup/chrome-api-setup.ts'
    ],
    coverage: {
      thresholds: {                         // 覆盖率阈值
        branches: 80, functions: 80,
        lines: 80, statements: 80
      }
    },
    testTimeout: 10000,                     // 测试超时时间
    pool: 'threads'                         // 并行执行模式
  }
}
```

## 常见问题

### Q: 如何调试插件？为什么我看不到准确的问题堆栈(Source Map)？

**A**:

SourceMap 在开发模式下是默认开启的，如果在浏览器中未能生效，通常是由于以下两个原因：

**1. 确认开发者工具设置已开启 Source Map：**

- 在任意页面按 `F12` 打开开发者工具。
- 点击右上角的齿轮图标 (⚙️) 进入设置。
- 在 "Preferences" (偏好设置) -> "Sources" (源代码) 分区下，确保 **"Enable JavaScript source maps"** 和 **"Enable CSS source maps"** 两个选项都已勾选。

**2. 针对不同部分使用正确的调试入口：**

- **调试弹出页 (Popup):**
  - 右键点击浏览器右上角的插件图标，选择 "检查弹出内容" 或 "Inspect popup"。
- **调试内容脚本 (Content Script):**
  - 在注入了内容脚本的目标网页上，按 `F12` 打开开发者工具，在 "Sources" (源代码) -> "Content scripts" 标签页下可以找到。
- **调试服务工作线程 (Service Worker):**
  - 这是最关键的，也是最容易出错的地方。**必须使用专用的调试窗口**。
  - 在浏览器地址栏输入 `chrome://extensions/`。
  - 找到 "数懒孪生代理"，点击其下方的 **"Service Worker"** 链接（部分浏览器可能显示为 "背景页面"）。
  - 这会打开一个**独立**的开发者工具窗口，**只有在这个窗口里**，才能看到 Service Worker 的 `console` 输出、网络请求，并对 `background` 脚本进行断点调试。

### Q: 如何添加新的功能模块？

**A**:

1. 在 `src/core/` 下创建新的模块目录
2. 实现核心逻辑和类型定义
3. 在 `src/shared/types/` 中添加相关类型
4. 更新 Service Worker 中的服务注册
5. 编写对应的测试用例

### Q: 如何发布到Chrome Web Store？

**A**:

1. 运行 `npm run build` 生成生产版本
2. 将 `dist` 文件夹打包为 .zip 文件
3. 在Chrome开发者控制台上传zip文件
4. 填写应用描述和截图
5. 提交审核

## 性能优化建议

### 开发阶段

- 使用 `npm run dev` 而不是 `npm run build:dev` 以获得最快的重新构建
- 关闭不必要的Chrome扩展以减少干扰
- 使用Chrome DevTools的Performance面板分析性能瓶颈

### 生产部署

- 确保运行 `npm run build` 进行完整优化
- 监控bundle大小，避免引入过大的依赖
- 使用 `npm run build:analyze` 定期分析包大小

## 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/AmazingFeature`
3. 提交更改: `git commit -m 'Add some AmazingFeature'`
4. 推送到分支: `git push origin feature/AmazingFeature`
5. 打开 Pull Request

**技术支持**: Agentic AGI Team  
**文档版本**: v1.0.0  
**最后更新**: 2025年6月21日
