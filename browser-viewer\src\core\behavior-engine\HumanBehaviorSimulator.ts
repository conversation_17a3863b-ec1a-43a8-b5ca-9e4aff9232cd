/**
 * 人类行为模拟器 - 模拟真实的人类浏览行为
 */

import type {
  BehaviorProfile,
  CaptureOptions,
  Point,
  ScrollOptions,
  SimulatorSpeed,
} from '@/shared/types';

import {
  BEHAVIOR_PROFILES
} from '@/shared/constants';

import {
  addRandomness,
  createLogger,
  sleep
} from '@/shared/utils';
import { MaskController } from '@/ui/mask/MaskController';
import { PerformanceInterruptionController } from '../task-manager/PerformanceInterruptionController';

/**
 * 定义行为模拟的执行上下文。
 * 它可以是浏览器标签页的顶层窗口，
 * 也可以是无头模式下iframe的contentWindow。
 */
export type ExecutionContext = Window;

interface FullPageScanOptions {
  isBackground?: boolean;
}

export class HumanBehaviorSimulator {
  private readonly _logger = createLogger('HumanBehaviorSimulator');
  
  // 关联的UI蒙版控制器，用于提供视觉反馈
  private readonly _maskController?: MaskController;

  // 行为历史（用于检测模式）
  private readonly _actionHistory: Array<{
    type: string;
    timestamp: number;
    position?: Point;
    duration?: number;
  }> = [];
  
  // 当前行为配置
  private readonly _captureOptions: CaptureOptions;
  private _currentProfile: BehaviorProfile = BEHAVIOR_PROFILES.normal;
  private _isActive = false;
  
  // 用于性能节流的控制参数
  private _targetFrameDuration = 1000 / 60; // 默认60fps
  private _distractionsPaused = false;
  
  constructor(
    profile: keyof typeof BEHAVIOR_PROFILES = 'normal',
    maskController?: MaskController,
    captureOptions?: CaptureOptions,
  ) {
    this.setProfile(profile);
    this._captureOptions = captureOptions ?? { blockSize: 800, overlap: 50, quality: 90, format: 'jpeg' };
    // 如果传入了蒙版控制器，则将其与模拟器关联
    if (maskController) {
      this._maskController = maskController;
      this._logger.info('蒙版控制器已关联，用于提供视觉反馈。');
    }
    this._logger.info(`用户行为模拟器连接完成，使用行为配置：${profile}`);
  }
  
  setProfile(profileName: keyof typeof BEHAVIOR_PROFILES): void {
    if (profileName in BEHAVIOR_PROFILES) {
      this._currentProfile = BEHAVIOR_PROFILES[profileName];
      this._logger.info(`切换行为模拟配置：${profileName}`);
    } else {
      this._logger.warn(`未知行为模拟配置：${profileName}，使用默认配置`);
    }
  }
  
  /**
   * 设置行为速度，映射到预定义的配置文件。
   * @param speed 速度级别
   */
  setSpeed(speed: SimulatorSpeed): void {
    const speedMultipliers = {
      slow: 0.5,
      medium: 1.0,
      fast: 2.0
    };
    
    this._currentProfile.scrollSpeed = speedMultipliers[speed];
    this._logger.info(`Behavior simulation speed set to: ${speed} (${speedMultipliers[speed]}x)`);
  }
  
  /**
   * 设置目标滚动动画帧率。
   * @param fps 目标FPS
   */
  setTargetFPS(fps: number): void {
    this._targetFrameDuration = 1000 / fps; // 毫秒
    this._logger.info(`Target FPS set to: ${fps} (${this._targetFrameDuration}ms per frame)`);
  }
  
  /**
   * 暂停或恢复分心等非关键行为。
   * @param paused 是否暂停
   */
  pauseDistractions(paused: boolean): void {
    this._distractionsPaused = paused;
    this._logger.info(`Non-critical distractions paused: ${paused}`);
  }
  
  async simulateScroll(
    context: ExecutionContext,
    options: ScrollOptions
  ): Promise<void> {
    const { startY, endY, duration } = options;

    // 如果页面在后台或滚动时长为0，则立即完成滚动，不使用动画
    if (duration === 0 || context.document.visibilityState === 'hidden') {
      context.scrollTo(0, endY);
      this._updateMaskOnScroll(context);
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      let startTime: number | null = null;

      const animateScroll = (timestamp: number) => {
        startTime ??= timestamp;

        const elapsed = timestamp - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const newY = startY + (endY - startY) * progress;

        context.scrollTo(0, newY);
        this._updateMaskOnScroll(context);

        if (elapsed < duration) {
          context.requestAnimationFrame(animateScroll);
        } else {
          // 确保滚动到最终位置，并解决Promise
          context.scrollTo(0, endY);
          this._updateMaskOnScroll(context);
          resolve();
        }
      };

      context.requestAnimationFrame(animateScroll);
    });
  }
  
  async simulateClick(context: ExecutionContext, target: Point): Promise<void> {
    if (!this._isActive) {
      throw new Error('Behavior simulator is not active');
    }
    
    this._logger.debug(`Simulating click at`, target);
    
    const startTime = Date.now();
    
    try {
      // 由于无法在iframe上下文之外获取鼠标位置，我们假设从(0,0)开始
      // 生成鼠标路径但不使用（预留给未来功能）
      // const currentPosition: Point = { x: 0, y: 0 }; 
      // const movePath = this.generateMousePath(currentPosition, target);
      
      // 执行鼠标移动 (在iframe中为视觉效果，非功能性)
      // await this.executeMouseMovement(context, movePath);
      
      // 执行点击，并同步更新蒙版上的鼠标位置
      await this._executeClick(context, target);
      
      // 记录行为
      this._recordAction('click', {
        position: target,
        duration: Date.now() - startTime
      });
      
      // 添加点击后的停顿
      await this._addRandomPause();
      
    } catch (error) {
      this._logger.error('Click simulation failed:', error);
      throw error;
    }
  }
  
  simulateTyping(context: ExecutionContext, text: string, targetSelector: string): void {
    if (!this._isActive) {
      throw new Error('Behavior simulator is not active');
    }
    
    this._logger.debug(`Simulating typing: "${text}"`);
    
    const startTime = Date.now();
    
    try {
      const element = context.document.querySelector(targetSelector) as HTMLElement;
      if (!element) {
        throw new Error(`Element with selector "${targetSelector}" not found.`);
      }

      element.focus();
      if ('value' in element) {
        (element as HTMLInputElement).value = text;
      }
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      this._recordAction('typing', {
        duration: Date.now() - startTime
      });
      
    } catch (error) {
      this._logger.error('Typing simulation failed:', error);
      throw error;
    }
  }
  
  async simulateWait(duration: number): Promise<void> {
    this._logger.debug(`Simulating wait for ${duration}ms`);
    
    const startTime = Date.now();
    
    // 添加随机变化到等待时间
    const actualDuration = addRandomness(duration, this._currentProfile.randomness);
    
    await this._interruptibleSleep(actualDuration);
    
    // 记录行为
    this._recordAction('wait', {
      duration: Date.now() - startTime
    });
  }
  
  async simulateDistraction(context: ExecutionContext): Promise<void> {
    if (!this._isActive || this._distractionsPaused || Math.random() > this._currentProfile.distractionRate) {
      return;
    }
    
    this._logger.debug(`Simulating distraction`);
    
    const distractionTypes = ['scroll_random', 'pause', 'safe_click'];
    const distractionType = distractionTypes[Math.floor(Math.random() * distractionTypes.length)];
    
    try {
      switch (distractionType) {
        case 'scroll_random':
          await this._simulateRandomScroll(context);
          break;
        case 'safe_click':
          await this.simulateSafeClick(context);
          break;
        case 'pause':
          {
            const delay = 500 + Math.random() * 1500;
            await this._interruptibleSleep(delay);
          }
          break;
      }
      
      this._recordAction('distraction', { type: distractionType });
      
    } catch (error) {
      this._logger.warn('Distraction simulation failed:', error);
    }
  }
  
  async simulateSafeClick(context: ExecutionContext): Promise<void> {
    if (!this._isActive || this._distractionsPaused) {
      return;
    }

    this._logger.debug(`Simulating safe click`);
    try {
      const targetPoint = this._findSafeClickTarget(context);
      if (targetPoint) {
        await this.simulateClick(context, targetPoint);
      } else {
        this._logger.warn(`Could not find a safe click target. Skipping.`);
      }
    } catch (error) {
      this._logger.error('Safe click simulation failed:', error);
      throw error;
    }
  }
  
  start(): void {
    this._isActive = true;
    this._logger.info('Human behavior simulator started');
  }
  
  stop(): void {
    this._isActive = false;
    this._logger.info('Human behavior simulator stopped');
  }
  
  isRunning(): boolean {
    return this._isActive;
  }
  
  getActionHistory(): typeof this._actionHistory {
    return [...this._actionHistory];
  }
  
  getPageDimensions(context: ExecutionContext): { scrollY: number; scrollHeight: number; innerHeight: number; clientWidth: number; } {
    return {
      scrollY: context.scrollY,
      scrollHeight: context.document.body.scrollHeight,
      innerHeight: context.innerHeight,
      clientWidth: context.document.body.clientWidth,
    };
  }
  
  clearHistory(): void {
    this._actionHistory.length = 0;
    this._logger.debug('Action history cleared');
  }
  
  // 私有辅助方法
  
  // 预留方法：生成滚动路径（当前版本未使用）
  // private generateScrollPath(options: ScrollOptions): Point[] {
  //   const { startY, endY } = options;
  //   const distance = Math.abs(endY - startY);
  //   
  //   // 根据距离调整步数
  //   const steps = Math.max(30, Math.floor(distance / 15));
  //   
  //   const startPoint: Point = { x: 0, y: startY };
  //   const endPoint: Point = { x: 0, y: endY };
  //   
  //   return generateBezierPath(startPoint, endPoint, steps, this.currentProfile.randomness);
  // }
  
  // 预留方法：执行滚动动画（当前版本未使用）
  // private async executeScrollAnimation(context: ExecutionContext, path: Point[], options: ScrollOptions): Promise<void> {
  //   const { duration } = options;
  //   let lastFrameTime = 0;
  //
  //   return new Promise((resolve) => {
  //     const animateScroll = (timestamp: number) => {
  //       if (!lastFrameTime) {
  //         lastFrameTime = timestamp;
  //       }
  //
  //       const elapsedFromLastFrame = timestamp - lastFrameTime;
  //
  //       // 如果距离上一帧的时间小于目标帧间隔，则跳过此帧
  //       if (elapsedFromLastFrame < this.targetFrameDuration) {
  //         context.requestAnimationFrame(animateScroll);
  //         return;
  //       }
  //
  //       const elapsedTotal = timestamp - (performance.timeOrigin + path[0].x);
  //       const progress = Math.min(elapsedTotal / duration, 1);
  //       const targetIndex = Math.floor(progress * (path.length - 1));
  //       const currentPoint = path[targetIndex];
  //
  //       context.scrollTo(0, currentPoint.y);
  //
  //       this.maskController?.updateHighlight({
  //         x: 0,
  //         y: context.scrollY,
  //         width: context.innerWidth,
  //         height: context.innerHeight,
  //       });
  //
  //       lastFrameTime = timestamp;
  //
  //       if (progress < 1) {
  //         context.requestAnimationFrame(animateScroll);
  //       } else {
  //         resolve();
  //       }
  //     };
  //     context.requestAnimationFrame(animateScroll);
  //   });
  // }
  
  // 预留方法：生成鼠标路径（当前版本未使用）
  // private generateMousePath(start: Point, end: Point): Point[] {
  //   return generateBezierPath(start, end, this.currentProfile.randomness);
  // }
  
  // 预留方法：执行鼠标移动（当前版本未使用）
  // private async executeMouseMovement(_context: ExecutionContext, path: Point[]): Promise<void> {
  //   for (const point of path) {
  //     // 模拟鼠标移动时，实时更新蒙版上的视觉反馈点
  //     this.maskController?.updateMousePosition(point);
  //     await new Promise(resolve => setTimeout(resolve, 10)); // 模拟移动延迟
  //   }
  // }
  
  private async _executeClick(context: ExecutionContext, position: Point): Promise<void> {
    const element = context.document.elementFromPoint(position.x, position.y) as HTMLElement | null;

    if (element) {
      // --- 视觉反馈 ---
      // 在点击前，更新蒙版上的"鼠标"位置
      this._maskController?.updateMousePosition(position);
      // 添加一个短暂的延迟，让用户能感知到鼠标移动
      const delay = this._currentProfile.preClickDelay.min + Math.random() * (this._currentProfile.preClickDelay.max - this._currentProfile.preClickDelay.min);
      await this._interruptibleSleep(delay);
      
      element.click();
      this._logger.debug('Clicked on element:', element.tagName);
    } else {
      this._logger.warn(`No element found at position`, position);
    }
  }

  // 预留方法：模拟按键（当前版本未使用）
  // private async simulateKeyPress(_context: ExecutionContext, char: string): Promise<void> {
  //   this.logger.debug(`Simulating key press: ${char}`);
  //   // 由于安全限制，无法可靠地模拟按键事件。
  //   // 我们在 `simulateTyping` 中直接设置值作为替代。
  //   return Promise.resolve();
  // }

  // 预留方法：获取当前鼠标位置（当前版本未使用）
  // private getCurrentMousePosition(_context: ExecutionContext): Point {
  //   // 无法从外部获取iframe中的鼠标位置
  //   this.logger.debug('Cannot get mouse position from iframe, returning default.');
  //   return { x: 0, y: 0 };
  // }
  
  private async _simulateRandomScroll(context: ExecutionContext): Promise<void> {
    const currentPos = this._getCurrentScrollPosition(context);
    const maxScroll = this._getMaxScrollPosition(context);
    const viewport = this._getViewportSize(context);

    const randomY = Math.random() * (maxScroll - viewport.height);
    await this.simulateScroll(context, {
      startX: 0,
      endX: 0,
      startY: currentPos.y,
      endY: randomY,
      duration: addRandomness(1000, 0.5)
    });
  }
  
  // 预留方法：模拟随机鼠标移动（当前版本未使用）
  // private async simulateRandomMouseMovement(_context: ExecutionContext): Promise<void> {
  //   this.logger.debug('Random mouse movement is skipped in headless mode.');
  //   return Promise.resolve();
  // }
  
  private _getCurrentScrollPosition(context: ExecutionContext): Point {
    return { x: context.scrollX, y: context.scrollY };
  }
  
  private _getMaxScrollPosition(context: ExecutionContext): number {
    return context.document.body.scrollHeight - context.innerHeight;
  }
  
  private _getViewportSize(context: ExecutionContext): { width: number; height: number } {
    return { width: context.innerWidth, height: context.innerHeight };
  }
  
  private async _addRandomPause(): Promise<void> {
    const { min, max } = this._currentProfile.postActionDelay;
    const delay = min + Math.random() * (max - min);
    this._logger.debug(`Pausing for ${delay.toFixed(0)}ms after action.`);
    await this._interruptibleSleep(delay);
  }
  
  private _recordAction(type: string, data?: Record<string, unknown>): void {
    this._actionHistory.push({
      type,
      timestamp: Date.now(),
      ...data
    });
    
    // 保持历史记录大小
    if (this._actionHistory.length > 100) {
      this._actionHistory.shift();
    }
  }

  private _findSafeClickTarget(context: ExecutionContext): Point | null {
    const clickableTags = ['div', 'p', 'span', 'section', 'article'];
    const {
      width,
      height
    } = this._getViewportSize(context);
    
    for (let i = 0; i < 3; i++) { // Try 3 times
      const x = Math.random() * width;
      const y = Math.random() * height;
      const element = context.document.elementFromPoint(x, y);

      if (element && clickableTags.includes(element.tagName.toLowerCase())) {
        // Further check to avoid interactive elements
        const closestInteractive = element.closest('a, button, input, [role="button"], [onclick]');
        if (!closestInteractive) {
          this._logger.info(`Found safe click target at (${x}, ${y})`);
          return {
            x,
            y
          };
        }
      }
    }
    
    return null;
  }
  
  /**
   * 模拟完整的页面扫描，包括滚动、捕捉和内容稳定性检测。
   * @param context - 页面执行上下文 (Window)。
   * @param captureRegionCallback - 一个回调函数，用于捕捉指定区域并进行处理。
   */
  async simulateFullPageScan(
    context: ExecutionContext,
    captureRegionCallback: (region: { y: number; height: number; width: number }) => Promise<void>,
    options: FullPageScanOptions = {}
  ): Promise<void> {
    this._logger.info('Starting integrated full page scan (no pre-scroll)...');
    this.start();

    try {
      await this._performFullPageScan(context, captureRegionCallback, options);
    } catch (error) {
      this._logger.error('Full page scan failed during simulation.', error);
      throw error;
    } finally {
      this.stop();
      this._logger.info('Full page scan simulation finished.');
    }
  }

  private async _performFullPageScan(
    context: ExecutionContext,
    captureRegionCallback: (region: { y: number; height: number; width: number }) => Promise<void>,
    options: FullPageScanOptions
  ): Promise<void> {
    const { isBackground = false } = options;
    const { blockSize, overlap } = this._captureOptions;
    const { clientWidth } = this.getPageDimensions(context);
    
    let scrollHeight = this.getPageDimensions(context).scrollHeight;
    let lastCaptureY = await this._captureInitialRegion(context, captureRegionCallback, blockSize, overlap, clientWidth);
    
    if (scrollHeight > blockSize) {
      const scanResult = await this._performScrollAndCapture(context, captureRegionCallback, {
        isBackground,
        blockSize,
        overlap,
        clientWidth,
        initialScrollHeight: scrollHeight,
        lastCaptureY
      });
      
      scrollHeight = scanResult.finalScrollHeight;
      lastCaptureY = scanResult.lastCaptureY;
    }
    
    await this._captureFinalRegions(captureRegionCallback, lastCaptureY, scrollHeight, blockSize, overlap, clientWidth);
  }

  private async _captureInitialRegion(
    context: ExecutionContext,
    captureRegionCallback: (region: { y: number; height: number; width: number }) => Promise<void>,
    blockSize: number,
    overlap: number,
    clientWidth: number
  ): Promise<number> {
    const scrollHeight = this.getPageDimensions(context).scrollHeight;
    const initialCaptureHeight = Math.min(blockSize, scrollHeight);
    
    await this._interruptibleSleep(0);
    this._logger.debug(`Capturing initial region from y: 0, height: ${initialCaptureHeight}`);
    await captureRegionCallback({ y: 0, height: initialCaptureHeight, width: clientWidth });
    
    return initialCaptureHeight > overlap ? initialCaptureHeight - overlap : 0;
  }

  private async _performScrollAndCapture(
    context: ExecutionContext,
    captureRegionCallback: (region: { y: number; height: number; width: number }) => Promise<void>,
    params: {
      isBackground: boolean;
      blockSize: number;
      overlap: number;
      clientWidth: number;
      initialScrollHeight: number;
      lastCaptureY: number;
    }
  ): Promise<{ finalScrollHeight: number; lastCaptureY: number }> {
    const { isBackground, blockSize, overlap, clientWidth } = params;
    let { initialScrollHeight: scrollHeight, lastCaptureY } = params;
    let currentScrollY = 0;

    while (currentScrollY < scrollHeight - context.innerHeight) {
      const scrollResult = await this._performScrollStep(context, currentScrollY, scrollHeight, isBackground);
      currentScrollY = scrollResult.currentScrollY;
      scrollHeight = scrollResult.scrollHeight;
      
      if (this._shouldCaptureRegion(currentScrollY, context.innerHeight, lastCaptureY, blockSize)) {
        this._logger.debug(`Threshold met. Capturing region from y: ${lastCaptureY}, height: ${blockSize}`);
        await this._interruptibleSleep(0);
        await captureRegionCallback({ y: lastCaptureY, height: blockSize, width: clientWidth });
        lastCaptureY += (blockSize - overlap);
      }
    }

    return { finalScrollHeight: scrollHeight, lastCaptureY };
  }

  private async _performScrollStep(
    context: ExecutionContext,
    currentScrollY: number,
    scrollHeight: number,
    isBackground: boolean
  ): Promise<{ currentScrollY: number; scrollHeight: number }> {
    const scrollIncrement = context.innerHeight * 0.85;
    const nextScrollY = Math.min(currentScrollY + scrollIncrement, scrollHeight - context.innerHeight);

    await this.simulateScroll(context, {
      startX: 0, endX: 0,
      startY: currentScrollY,
      endY: nextScrollY,
      duration: isBackground ? 0 : 750
    });

    const pauseDuration = this._currentProfile.scrollPause.min + Math.random() * (this._currentProfile.scrollPause.max - this._currentProfile.scrollPause.min);
    await this._interruptibleSleep(pauseDuration);

    const newScrollY = context.scrollY;
    const newScrollHeight = this.getPageDimensions(context).scrollHeight;
    
    if (newScrollHeight > scrollHeight) {
      this._logger.info(`Scroll height updated due to lazy loading: ${scrollHeight} -> ${newScrollHeight}`);
    }
    
    return { currentScrollY: newScrollY, scrollHeight: newScrollHeight };
  }

  private _shouldCaptureRegion(
    currentScrollY: number,
    innerHeight: number,
    lastCaptureY: number,
    blockSize: number
  ): boolean {
    return currentScrollY + innerHeight >= lastCaptureY + blockSize;
  }

  private async _captureFinalRegions(
    captureRegionCallback: (region: { y: number; height: number; width: number }) => Promise<void>,
    lastCaptureY: number,
    scrollHeight: number,
    blockSize: number,
    overlap: number,
    clientWidth: number
  ): Promise<void> {
    this._logger.info(`Main scroll loop finished. Finalizing capture from y=${lastCaptureY} to scrollHeight=${scrollHeight}.`);
    
    let currentCaptureY = lastCaptureY;
    
    while (currentCaptureY < scrollHeight) {
      const remainingHeight = scrollHeight - currentCaptureY;
      
      if (remainingHeight < overlap) {
        this._logger.debug(`Remaining content height (${remainingHeight}px) is less than overlap (${overlap}px). Final capture complete.`);
        break;
      }

      const captureHeight = Math.min(blockSize, remainingHeight);
      this._logger.debug(`Capturing final page segment. y: ${currentCaptureY}, height: ${captureHeight}`);
      
        await this._interruptibleSleep(0);
        await captureRegionCallback({ y: currentCaptureY, height: captureHeight, width: clientWidth });

      if (captureHeight >= remainingHeight) {
        break;
      }
      
      currentCaptureY += (blockSize - overlap);
    }
  }

  private _updateMaskOnScroll(context: ExecutionContext): void {
    if (!this._maskController) return;
    this._maskController.updateHighlight({
      x: 0,
      y: context.scrollY,
      width: context.innerWidth,
      height: context.innerHeight,
    });
  }

  private async _interruptibleSleep(duration: number): Promise<void> {
    PerformanceInterruptionController.pause();
    try {
      await sleep(duration);
    } finally {
      PerformanceInterruptionController.resume();
    }
  }
}