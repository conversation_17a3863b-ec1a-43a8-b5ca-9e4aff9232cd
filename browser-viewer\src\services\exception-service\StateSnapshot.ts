/**
 * @file StateSnapshot.ts
 * @description 负责捕获和恢复浏览器标签页的状态，用于异常恢复。
 */

import { STORAGE_KEYS } from '@/shared/constants';
import { DataAccessCategory, PrivacySettings, TabId } from '@/shared/types';
import { createLogger, extractDomain, getStorageData, wildcardToRegex } from '@/shared/utils';
import { PrivacyService } from '../privacy-service';

/**
 * 定义了标签页状态快照的数据结构。
 */
export interface TabState {
  timestamp: number;
  url: string;
  dom: string;
  scrollPosition: { x: number; y: number };
  cookies: chrome.cookies.Cookie[];
  formData: Record<string, string>;
}

/**
 * StateSnapshot 类提供了捕获和恢复标签页状态的方法。
 */
export class StateSnapshot {
  private readonly _logger = createLogger('StateSnapshot');
  private _privacySettings: PrivacySettings | null = null;

  constructor(private readonly privacyService: PrivacyService) {
    // 延迟加载隐私设置，避免在构造函数中执行异步操作
  }

  /**
   * 初始化实例，加载隐私设置
   */
  public async initialize(): Promise<void> {
    await this._loadPrivacySettings();
  }

  /**
   * 捕获指定标签页的完整状态。
   * @param tabId - 要捕获状态的标签页ID。
   * @returns 返回一个包含标签页状态的 TabState 对象。
   */
  public async capture(tabId: TabId): Promise<TabState | null> {
    try {
      const tab = await chrome.tabs.get(tabId);
      if (!tab.url) {
        this._logger.warn(`无法为没有 URL 的标签页 ${tabId} 创建快照。`);
        return null;
      }

      const scrollPosition = await this._captureScrollPosition(tabId);
      const dom = await this._captureDOM(tabId);
      const cookies = await this._captureCookies(tab.url, tabId);
      const formData = await this._captureFormData(tabId);

      const state: TabState = {
        timestamp: Date.now(),
        url: tab.url,
        dom,
        scrollPosition,
        cookies,
        formData,
      };

      this._logger.info(`已为标签页 ${tabId} 成功捕获状态快照。`);
      return state;
    } catch (error) {
      this._logger.error(`为标签页 ${tabId} 捕获状态时出错:`, error);
      return null;
    }
  }

  /**
   * 将指定标签页恢复到提供的状态。
   * @param tabId - 要恢复状态的标签页ID。
   * @param state - 要恢复到的 TabState 对象。
   */
  public async restore(tabId: TabId, state: TabState): Promise<void> {
    try {
      await chrome.tabs.update(tabId, { url: state.url });
      
      // 等待标签页加载完成
      await new Promise<void>(resolve => {
        const listener = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo) => {
          if (updatedTabId === tabId && changeInfo.status === 'complete') {
            chrome.tabs.onUpdated.removeListener(listener);
            resolve();
          }
        };
        chrome.tabs.onUpdated.addListener(listener);
      });

      // 页面加载完成后再恢复其他状态
      await this._restoreCookies(state.url, state.cookies, tabId);
      // DOM恢复通过页面重新加载完成，不直接注入HTML，以确保页面脚本正常执行。
      await this._restoreFormData(tabId, state.formData);
      await this._restoreScrollPosition(tabId, state.scrollPosition);

      this._logger.info(`已成功将标签页 ${tabId} 恢复到时间戳为 ${state.timestamp} 的状态。`);
    } catch (error) {
      this._logger.error(`将标签页 ${tabId} 恢复到状态时出错:`, error);
    }
  }

  private async _captureScrollPosition(tabId: TabId): Promise<{ x: number; y: number }> {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => ({ x: window.scrollX, y: window.scrollY }),
    });
    return results?.[0]?.result ?? { x: 0, y: 0 };
  }

  private async _restoreScrollPosition(tabId: TabId, position: { x: number; y: number }): Promise<void> {
    await chrome.scripting.executeScript({
      target: { tabId },
      func: (x: number, y: number) => window.scrollTo(x, y),
      args: [position.x, position.y],
    });
  }

  private async _captureDOM(tabId: TabId): Promise<string> {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => document.documentElement.outerHTML,
    });
    return results?.[0]?.result ?? '';
  }

  private async _captureCookies(url: string, tabId: TabId): Promise<chrome.cookies.Cookie[]> {
    if (this._isDomainBlacklisted(url)) {
      this._logger.warn(`域名 ${extractDomain(url)} 在Cookie黑名单中，跳过Cookie捕获。`);
      return [];
    }
    this._log(tabId, 'read', DataAccessCategory.COOKIE, { url });
    return chrome.cookies.getAll({ url });
  }

  private async _restoreCookies(url: string, cookies: chrome.cookies.Cookie[], tabId: TabId): Promise<void> {
    if (this._isDomainBlacklisted(url)) {
      this._logger.warn(`域名 ${extractDomain(url)} 在Cookie黑名单中，跳过Cookie恢复。`);
      return;
    }
    this._log(tabId, 'write', DataAccessCategory.COOKIE, { url, count: cookies.length });
    
    // 恢复前先清除现有cookie
    const existingCookies = await chrome.cookies.getAll({ url });
    for (const cookie of existingCookies) {
      await chrome.cookies.remove({ url, name: cookie.name });
    }
    // 设置快照中的cookie
    for (const cookie of cookies) {
      // 移除chrome不允许设置的属性
      const newCookie: chrome.cookies.SetDetails = {
        url,
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain,
        path: cookie.path,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        expirationDate: cookie.expirationDate,
        storeId: cookie.storeId,
      };
      await chrome.cookies.set(newCookie);
    }
  }

  private async _captureFormData(tabId: TabId): Promise<Record<string, string>> {
    this._log(tabId, 'read', DataAccessCategory.USER_INPUT);
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const data: Record<string, string> = {};
        const formElements = document.querySelectorAll('input, textarea, select');
        
        formElements.forEach((el: Element) => {
          if (
            (el instanceof HTMLInputElement ||
              el instanceof HTMLTextAreaElement ||
              el instanceof HTMLSelectElement) &&
            el.name
          ) {
            data[el.name] = el.value;
          }
        });
        return data;
      },
    });
    return results?.[0]?.result ?? {};
  }

  private async _restoreFormData(tabId: TabId, formData: Record<string, string>): Promise<void> {
    if (Object.keys(formData).length === 0) {
      return; // 如果没有表单数据，则无需执行
    }
    this._log(tabId, 'write', DataAccessCategory.USER_INPUT, { keys: Object.keys(formData) });
    
    await chrome.scripting.executeScript({
      target: { tabId },
      func: (data: Record<string, string>) => {
        for (const name in data) {
          const element = document.querySelector(`[name="${name}"]`) as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
          if (element) {
            element.value = data[name];
          }
        }
      },
      args: [formData],
    });
  }

  private async _loadPrivacySettings(): Promise<void> {
    const defaultSettings: PrivacySettings = {
      cookieDeniedDomains: [],
      enableAuditLog: true,
      proxyUrlBlacklist: [],
    };
    this._privacySettings = await getStorageData(STORAGE_KEYS.PRIVACY_SETTINGS, defaultSettings);
    this._logger.info('Privacy settings loaded for StateSnapshot');
  }

  private _isDomainBlacklisted(url: string): boolean {
    if (!this._privacySettings) return false;
    const domain = extractDomain(url);
    if (!domain) return false;

    const blacklist = this._privacySettings.cookieDeniedDomains;
    return blacklist.some(pattern => {
      const regex = wildcardToRegex(pattern);
      return regex.test(domain);
    });
  }

  private _log(tabId: TabId, type: 'read' | 'write', category: DataAccessCategory, context?: Record<string, unknown>): void {
    if (this._privacySettings?.enableAuditLog) {
      void this.privacyService.logDataAccess({
        type,
        category,
        accessedBy: `StateSnapshot-tab-${tabId}`,
        userApproved: true, // 假设在任务上下文中操作即为授权
        context,
      });
    }
  }
} 