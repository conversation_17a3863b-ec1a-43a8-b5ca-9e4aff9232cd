"""
AI服务
提供视觉识别、文本处理等AI能力
"""

import base64
import io
from typing import Optional, Dict, Any, List
from PIL import Image
import openai
from sqlalchemy.orm import Session

from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AIService:
    """AI服务类"""

    def __init__(self):
        if settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY

    async def analyze_image(
        self, image_data: bytes, prompt: str = None
    ) -> Dict[str, Any]:
        """分析图像内容"""
        try:
            if not settings.OPENAI_API_KEY:
                return {"error": "OpenAI API key not configured"}

            # 将图像转换为base64
            image_base64 = base64.b64encode(image_data).decode("utf-8")

            # 默认提示词
            if not prompt:
                prompt = "请描述这张图片的内容，包括主要元素、文字、布局等信息。"

            # 调用OpenAI Vision API
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                },
                            },
                        ],
                    }
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
            )

            result = {
                "description": response.choices[0].message.content,
                "model": settings.OPENAI_MODEL,
                "tokens_used": response.usage.total_tokens,
            }

            logger.info("Image analysis completed")
            return result

        except Exception as e:
            logger.error(f"Image analysis error: {e}")
            return {"error": str(e)}

    async def extract_text_from_image(self, image_data: bytes) -> Dict[str, Any]:
        """从图像中提取文字"""
        try:
            # 使用OCR提取文字的提示词
            prompt = "请提取这张图片中的所有文字内容，保持原有的格式和结构。"

            result = await self.analyze_image(image_data, prompt)

            if "error" not in result:
                result["extracted_text"] = result.get("description", "")

            return result

        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            return {"error": str(e)}

    async def analyze_webpage_content(
        self, content: str, url: str = None
    ) -> Dict[str, Any]:
        """分析网页内容"""
        try:
            if not settings.OPENAI_API_KEY:
                return {"error": "OpenAI API key not configured"}

            prompt = f"""
            请分析以下网页内容，提取关键信息：
            
            网页URL: {url or "未知"}
            
            内容:
            {content[:4000]}  # 限制内容长度
            
            请提供：
            1. 网页主题和类型
            2. 主要内容摘要
            3. 关键信息点
            4. 数据结构分析
            """

            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=settings.OPENAI_MAX_TOKENS,
            )

            result = {
                "analysis": response.choices[0].message.content,
                "url": url,
                "model": "gpt-4",
                "tokens_used": response.usage.total_tokens,
            }

            logger.info(f"Webpage content analysis completed for: {url}")
            return result

        except Exception as e:
            logger.error(f"Webpage content analysis error: {e}")
            return {"error": str(e)}

    async def classify_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据分类"""
        try:
            if not settings.OPENAI_API_KEY:
                return {"error": "OpenAI API key not configured"}

            prompt = f"""
            请对以下数据进行分类和标记：
            
            数据内容:
            {str(data)[:2000]}
            
            请提供：
            1. 数据类型分类
            2. 内容主题标签
            3. 重要性评级（1-5）
            4. 建议的处理方式
            """

            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
            )

            result = {
                "classification": response.choices[0].message.content,
                "model": "gpt-4",
                "tokens_used": response.usage.total_tokens,
            }

            logger.info("Data classification completed")
            return result

        except Exception as e:
            logger.error(f"Data classification error: {e}")
            return {"error": str(e)}

    async def generate_summary(
        self, text: str, max_length: int = 200
    ) -> Dict[str, Any]:
        """生成文本摘要"""
        try:
            if not settings.OPENAI_API_KEY:
                return {"error": "OpenAI API key not configured"}

            prompt = f"""
            请为以下文本生成一个简洁的摘要，长度不超过{max_length}字：
            
            {text[:3000]}
            """

            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_length + 50,
            )

            result = {
                "summary": response.choices[0].message.content,
                "original_length": len(text),
                "summary_length": len(response.choices[0].message.content),
                "model": "gpt-4",
                "tokens_used": response.usage.total_tokens,
            }

            logger.info("Text summary generated")
            return result

        except Exception as e:
            logger.error(f"Text summary error: {e}")
            return {"error": str(e)}

    def resize_image(self, image_data: bytes, max_size: tuple = (1024, 1024)) -> bytes:
        """调整图像大小"""
        try:
            image = Image.open(io.BytesIO(image_data))

            # 保持宽高比调整大小
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # 转换为JPEG格式
            output = io.BytesIO()
            if image.mode in ("RGBA", "P"):
                image = image.convert("RGB")

            image.save(output, format="JPEG", quality=settings.IMAGE_PROCESSING_QUALITY)

            return output.getvalue()

        except Exception as e:
            logger.error(f"Image resize error: {e}")
            return image_data

    def validate_image(self, image_data: bytes) -> bool:
        """验证图像数据"""
        try:
            image = Image.open(io.BytesIO(image_data))
            image.verify()
            return True
        except Exception:
            return False
