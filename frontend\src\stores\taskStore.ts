/**
 * 任务状态管理
 * 基于Zustand的轻量级状态管理
 * 继承browser-viewer的任务管理设计
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { TaskStatus, TaskPriority } from '../types/task';

// 任务接口定义
export interface Task {
  id: string;
  batchId?: string;
  userId: string;
  config: TaskConfig;
  urls: string[];
  status: TaskStatus;
  priority: TaskPriority;
  totalUrls: number;
  processedUrls: number;
  succeededUrls: number;
  failedUrls: number;
  progressPercentage: number;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  executionTime?: number;
  retryCount: number;
  errorMessage?: string;
  assignedWorker?: string;
  metadataInfo?: Record<string, any>;
  tags?: string[];
}

export interface TaskConfig {
  name: string;
  description?: string;
  extractionRules: ExtractionRule[];
  behaviorSettings: BehaviorSettings;
  retrySettings: RetrySettings;
}

export interface ExtractionRule {
  type: 'text' | 'image' | 'link' | 'table';
  selector: string;
  attribute?: string;
  required: boolean;
}

export interface BehaviorSettings {
  scrollBehavior: 'auto' | 'smooth' | 'instant';
  clickDelay: number;
  scrollDelay: number;
  waitForLoad: number;
  enableDistraction: boolean;
}

export interface RetrySettings {
  maxAttempts: number;
  delaySeconds: number;
  backoffMultiplier: number;
}

// 批次任务接口
export interface BatchTask {
  batchId: string;
  name: string;
  status: TaskStatus;
  config: TaskConfig;
  progress: {
    total: number;
    processed: number;
    succeeded: number;
    failed: number;
  };
  urlStatuses: UrlStatus[];
  createdAt: string;
  completedAt?: string;
}

export interface UrlStatus {
  url: string;
  status: TaskStatus;
  taskId: string;
}

// 任务统计接口
export interface TaskStatistics {
  totalTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  successRate: number;
  averageExecutionTime: number;
  todayTasks: number;
  weeklyTasks: number;
}

// 状态接口
interface TaskState {
  // 任务数据
  tasks: Task[];
  batchTasks: BatchTask[];
  selectedTasks: string[];
  currentTask?: Task;
  
  // 统计数据
  statistics: TaskStatistics;
  
  // UI状态
  loading: boolean;
  error: string | null;
  filters: TaskFilters;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  
  // 实时状态
  isConnected: boolean;
  lastUpdate: string | null;
}

interface TaskFilters {
  status?: TaskStatus;
  priority?: TaskPriority;
  dateRange?: [string, string];
  keyword?: string;
  userId?: string;
}

// 操作接口
interface TaskActions {
  // 任务CRUD操作
  fetchTasks: (filters?: TaskFilters) => Promise<void>;
  createTask: (config: TaskConfig, urls: string[]) => Promise<Task>;
  createBatchTask: (configs: TaskConfig[], urls: string[]) => Promise<BatchTask>;
  updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (taskId: string) => Promise<void>;
  
  // 任务控制操作 - 继承browser-viewer的控制接口
  startTask: (taskId: string) => Promise<void>;
  pauseTask: (taskId: string) => Promise<void>;
  cancelTask: (taskId: string) => Promise<void>;
  retryTask: (taskId: string) => Promise<void>;
  
  // 批量操作
  startAllTasks: () => Promise<void>;
  pauseAllTasks: () => Promise<void>;
  clearAllTasks: () => Promise<void>;
  startSelectedTasks: (taskIds: string[]) => Promise<void>;
  pauseSelectedTasks: (taskIds: string[]) => Promise<void>;
  
  // 选择操作
  selectTask: (taskId: string) => void;
  selectMultipleTasks: (taskIds: string[]) => void;
  clearSelection: () => void;
  toggleTaskSelection: (taskId: string) => void;
  
  // 过滤和分页
  setFilters: (filters: Partial<TaskFilters>) => void;
  setPagination: (pagination: Partial<TaskState['pagination']>) => void;
  
  // 实时更新
  updateTaskStatus: (taskId: string, status: TaskStatus, progress?: number) => void;
  updateBatchProgress: (batchId: string, progress: BatchTask['progress']) => void;
  
  // 统计数据
  fetchStatistics: () => Promise<void>;
  
  // 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 连接状态
  setConnectionStatus: (connected: boolean) => void;
}

// 初始状态
const initialState: TaskState = {
  tasks: [],
  batchTasks: [],
  selectedTasks: [],
  currentTask: undefined,
  statistics: {
    totalTasks: 0,
    runningTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    successRate: 0,
    averageExecutionTime: 0,
    todayTasks: 0,
    weeklyTasks: 0,
  },
  loading: false,
  error: null,
  filters: {},
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  isConnected: false,
  lastUpdate: null,
};

// 创建任务状态管理器
export const useTaskStore = create<TaskState & TaskActions>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        ...initialState,
        
        // 获取任务列表
        fetchTasks: async (filters) => {
          set((state) => {
            state.loading = true;
            state.error = null;
            if (filters) {
              state.filters = { ...state.filters, ...filters };
            }
          });
          
          try {
            // TODO: 调用API获取任务列表
            const response = await fetch('/api/v1/tasks', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ filters: get().filters, pagination: get().pagination }),
            });
            
            if (!response.ok) throw new Error('获取任务列表失败');
            
            const data = await response.json();
            
            set((state) => {
              state.tasks = data.tasks;
              state.pagination.total = data.total;
              state.loading = false;
              state.lastUpdate = new Date().toISOString();
            });
          } catch (error) {
            set((state) => {
              state.loading = false;
              state.error = error instanceof Error ? error.message : '未知错误';
            });
          }
        },
        
        // 创建单个任务
        createTask: async (config, urls) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });
          
          try {
            const response = await fetch('/api/v1/tasks', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ config, urls }),
            });
            
            if (!response.ok) throw new Error('创建任务失败');
            
            const task = await response.json();
            
            set((state) => {
              state.tasks.unshift(task);
              state.loading = false;
            });
            
            return task;
          } catch (error) {
            set((state) => {
              state.loading = false;
              state.error = error instanceof Error ? error.message : '创建任务失败';
            });
            throw error;
          }
        },
        
        // 启动任务
        startTask: async (taskId) => {
          try {
            const response = await fetch(`/api/v1/tasks/${taskId}/start`, {
              method: 'POST',
            });
            
            if (!response.ok) throw new Error('启动任务失败');
            
            set((state) => {
              const task = state.tasks.find(t => t.id === taskId);
              if (task) {
                task.status = TaskStatus.RUNNING;
                task.startedAt = new Date().toISOString();
              }
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : '启动任务失败';
            });
          }
        },
        
        // 暂停任务
        pauseTask: async (taskId) => {
          try {
            const response = await fetch(`/api/v1/tasks/${taskId}/pause`, {
              method: 'POST',
            });
            
            if (!response.ok) throw new Error('暂停任务失败');
            
            set((state) => {
              const task = state.tasks.find(t => t.id === taskId);
              if (task) {
                task.status = TaskStatus.PAUSED;
              }
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : '暂停任务失败';
            });
          }
        },
        
        // 批量操作 - 继承browser-viewer的批量控制设计
        startAllTasks: async () => {
          const pendingTasks = get().tasks.filter(t => t.status === TaskStatus.PENDING);
          for (const task of pendingTasks) {
            await get().startTask(task.id);
          }
        },
        
        pauseAllTasks: async () => {
          const runningTasks = get().tasks.filter(t => t.status === TaskStatus.RUNNING);
          for (const task of runningTasks) {
            await get().pauseTask(task.id);
          }
        },
        
        // 选择操作
        selectTask: (taskId) => {
          set((state) => {
            state.selectedTasks = [taskId];
          });
        },
        
        toggleTaskSelection: (taskId) => {
          set((state) => {
            const index = state.selectedTasks.indexOf(taskId);
            if (index > -1) {
              state.selectedTasks.splice(index, 1);
            } else {
              state.selectedTasks.push(taskId);
            }
          });
        },
        
        clearSelection: () => {
          set((state) => {
            state.selectedTasks = [];
          });
        },
        
        // 实时更新任务状态
        updateTaskStatus: (taskId, status, progress) => {
          set((state) => {
            const task = state.tasks.find(t => t.id === taskId);
            if (task) {
              task.status = status;
              if (progress !== undefined) {
                task.progressPercentage = progress;
              }
              state.lastUpdate = new Date().toISOString();
            }
          });
        },
        
        // 设置过滤器
        setFilters: (filters) => {
          set((state) => {
            state.filters = { ...state.filters, ...filters };
            state.pagination.current = 1; // 重置到第一页
          });
          get().fetchTasks();
        },
        
        // 设置分页
        setPagination: (pagination) => {
          set((state) => {
            state.pagination = { ...state.pagination, ...pagination };
          });
          get().fetchTasks();
        },
        
        // 错误处理
        setError: (error) => {
          set((state) => {
            state.error = error;
          });
        },
        
        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },
        
        // 连接状态
        setConnectionStatus: (connected) => {
          set((state) => {
            state.isConnected = connected;
          });
        },
        
        // 获取统计数据
        fetchStatistics: async () => {
          try {
            const response = await fetch('/api/v1/tasks/statistics');
            if (!response.ok) throw new Error('获取统计数据失败');
            
            const statistics = await response.json();
            
            set((state) => {
              state.statistics = statistics;
            });
          } catch (error) {
            console.error('获取统计数据失败:', error);
          }
        },
        
        // 其他操作的占位符实现
        createBatchTask: async () => { throw new Error('Not implemented'); },
        updateTask: async () => { throw new Error('Not implemented'); },
        deleteTask: async () => { throw new Error('Not implemented'); },
        cancelTask: async () => { throw new Error('Not implemented'); },
        retryTask: async () => { throw new Error('Not implemented'); },
        clearAllTasks: async () => { throw new Error('Not implemented'); },
        startSelectedTasks: async () => { throw new Error('Not implemented'); },
        pauseSelectedTasks: async () => { throw new Error('Not implemented'); },
        selectMultipleTasks: () => { throw new Error('Not implemented'); },
        updateBatchProgress: () => { throw new Error('Not implemented'); },
      }))
    ),
    {
      name: 'task-store',
      partialize: (state) => ({
        filters: state.filters,
        pagination: state.pagination,
      }),
    }
  )
);
