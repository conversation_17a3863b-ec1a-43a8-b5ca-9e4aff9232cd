# 开发指南

## 开发环境设置

### 1. 依赖安装

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置以下变量：
# MISTRAL_API_KEY=your_mistral_api_key_here
# OCR_MODEL=mistral-ocr-2505
# MISTRAL_BASE_URL=https://api.mistral.ai
```

## 常用开发命令

### 爬虫操作

```bash
# 运行主爬虫程序
cd crawler/jingdong
python with_cookies.py

# 菜单选项说明：
# 1 - 设置/刷新登录状态（首次使用必须）
# 2 - 使用已保存会话爬取（自动OCR）
# 3 - 对已有截图进行OCR识别
# 4 - 退出程序
```

### OCR操作

```bash
# 测试OCR功能完整性
python test_ocr.py

# 批量处理默认目录(.crawler_results)的图片
python ocr/process_images.py

# 指定输入输出路径
python ocr/process_images.py --input-dir /path/to/images --output-file results.json

# 设置处理参数
python ocr/process_images.py --max-tokens 3000 --delay 2.0
```

### 使用示例

```bash
# 运行OCR使用示例
python example_usage.py
```

## 开发工作流

### 1. 新功能开发流程

1. 创建任务记录文件：`PENDING_TASK_{编号}_{功能简述}.md`
2. 分析现有代码架构和集成点
3. 开始开发时重命名为：`ACTIVE_TASK_{编号}_{功能简述}.md`
4. 实现功能并更新任务记录
5. 测试验证功能完整性
6. 完成后重命名为：`DONE_TASK_{编号}_{功能简述}.md`

### 2. 代码修改原则

- **优先编辑现有文件**，避免创建新文件
- 保持与现有代码风格的一致性
- 遵循异步编程模式（使用async/await）
- 添加必要的错误处理和日志记录

### 3. 测试验证

```bash
# OCR功能测试
python test_ocr.py

# 完整流程测试（需要手动操作浏览器）
cd crawler/jingdong && python with_cookies.py
```

## 关键技术要点

### 1. 异步编程

- 所有I/O操作使用async/await
- 网络请求和文件操作需要异步处理
- 注意异步上下文管理（async with）

### 2. 错误处理

- API调用需要捕获网络异常
- 文件操作需要处理路径和权限错误
- OCR调用需要处理API限制和配额问题

### 3. 配置管理

- 敏感信息通过环境变量或.env文件管理
- 支持配置的动态加载和验证
- 提供合理的默认值

### 4. 数据存储

- 截图保存：`.crawler_results/images/`
- OCR结果：`.crawler_results/ocr/`
- 会话数据：`.crawler_profile/`
- 任务记录：`.claude-memory/tasks/`

## 常见问题解决

### 1. OCR API问题

```bash
# 检查API密钥配置
python -c "from ocr.mistral import MistralOCRConfig; print(MistralOCRConfig())"

# 测试API连接
python test_ocr.py
```

### 2. 爬虫登录问题

- 确保网络连接正常
- 检查京东账号状态
- 重新执行登录设置（菜单选项1）

### 3. 依赖安装问题

```bash
# 重新安装所有依赖
pip install -r requirements.txt --force-reinstall

# 安装Playwright浏览器（如果失败）
playwright install chromium
```

## 代码结构说明

### 1. 模块导入模式

```python
# 项目根目录路径处理
root_dir = Path(__file__).resolve().parent.parent
sys.path.append(str(root_dir))

# OCR模块导入（支持优雅降级）
try:
    from ocr.mistral import MistralOCRClient, MistralOCRConfig
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
```

### 2. 配置类使用

```python
# OCR配置初始化
config = MistralOCRConfig()
client = MistralOCRClient(config)

# 确保资源清理
client.close()
```

### 3. 异步函数调用

```python
# 异步OCR处理
result = client.ocr_image(image_path)

# 批量异步处理
results = client.batch_ocr(image_paths, delay=1.0)
```

## 扩展开发指导

### 1. 新增电商平台支持

- 在`crawler/`下创建新的平台目录
- 复制`jingdong/with_cookies.py`作为模板
- 修改URL匹配和页面解析逻辑

### 2. OCR功能扩展

- 在`ocr/`目录下添加新的OCR提供商
- 实现统一的OCR接口
- 更新配置管理支持多提供商

### 3. 数据处理增强

- 添加数据清洗和格式化功能
- 实现结构化数据提取
- 支持多种输出格式

---
*此指南随开发实践持续完善*
