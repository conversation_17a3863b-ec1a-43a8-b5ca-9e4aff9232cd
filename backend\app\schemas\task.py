"""
任务相关Schema
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, validator
from datetime import datetime
from enum import Enum

from app.models.task import TaskStatus, TaskPriority


class TaskConfigSchema(BaseModel):
    """任务配置Schema"""

    urls: List[str]
    max_pages: Optional[int] = 10
    delay: Optional[float] = 1.0
    timeout: Optional[int] = 30
    user_agent: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    cookies: Optional[Dict[str, str]] = None
    proxy: Optional[str] = None
    screenshot: Optional[bool] = True
    extract_text: Optional[bool] = True
    extract_links: Optional[bool] = False
    extract_images: Optional[bool] = False
    custom_selectors: Optional[Dict[str, str]] = None


class TaskCreateRequest(BaseModel):
    """任务创建请求"""

    name: str
    description: Optional[str] = None
    config: TaskConfigSchema
    priority: Optional[TaskPriority] = TaskPriority.NORMAL

    @validator("name")
    def validate_name(cls, v):
        if len(v.strip()) < 1:
            raise ValueError("任务名称不能为空")
        return v.strip()


class TaskUpdate(BaseModel):
    """任务更新模型"""

    name: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[TaskPriority] = None
    config: Optional[TaskConfigSchema] = None


class TaskResponse(BaseModel):
    """任务响应模型"""

    id: str
    name: str
    description: Optional[str]
    user_id: str
    batch_id: Optional[str]
    status: TaskStatus
    priority: TaskPriority
    config: Dict[str, Any]
    total_urls: int
    processed_urls: int
    succeeded_urls: int
    failed_urls: int
    progress_percentage: float
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    updated_at: Optional[datetime]
    execution_time: Optional[float]
    retry_count: int
    error_message: Optional[str]
    assigned_worker: Optional[str]

    class Config:
        from_attributes = True


class BatchResponse(BaseModel):
    """批量任务响应"""

    batch_id: str
    task_count: int
    created_at: datetime = datetime.utcnow()


class TaskMetrics(BaseModel):
    """任务指标"""

    task_id: str
    status: TaskStatus
    progress: float
    duration: Optional[float]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    performance_stats: Optional[Dict[str, Any]] = None


class TaskSummary(BaseModel):
    """任务摘要"""

    id: str
    name: str
    status: TaskStatus
    progress_percentage: float
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class TaskStatistics(BaseModel):
    """任务统计"""

    total_tasks: int
    pending_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    cancelled_tasks: int
    success_rate: float
    average_execution_time: Optional[float]
