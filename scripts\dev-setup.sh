#!/bin/bash

# 数懒平台开发环境设置脚本
# 用于快速设置开发环境

set -e

echo "🚀 数懒平台开发环境设置开始..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        echo "❌ Node.js 版本过低，需要 16+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.11+"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "⚠️  Docker 未安装，将无法使用容器化开发环境"
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        echo "❌ Git 未安装，请先安装 Git"
        exit 1
    fi
    
    echo "✅ 系统要求检查完成"
}

# 设置后端环境
setup_backend() {
    echo "🐍 设置后端环境..."
    
    cd backend
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        echo "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    echo "安装Python依赖..."
    pip install -r requirements.txt
    
    # 复制环境配置文件
    if [ ! -f ".env" ]; then
        echo "创建环境配置文件..."
        cp .env.example .env
        echo "⚠️  请编辑 backend/.env 文件配置数据库和其他服务"
    fi
    
    cd ..
    echo "✅ 后端环境设置完成"
}

# 设置前端环境
setup_frontend() {
    echo "⚛️  设置前端环境..."
    
    cd frontend
    
    # 安装依赖
    echo "安装前端依赖..."
    npm install
    
    # 创建环境配置文件
    if [ ! -f ".env.local" ]; then
        echo "创建前端环境配置..."
        cat > .env.local << EOF
# 前端环境配置
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:8000
VITE_APP_NAME=数懒平台
VITE_APP_VERSION=1.0.0
EOF
    fi
    
    cd ..
    echo "✅ 前端环境设置完成"
}

# 设置数据库
setup_database() {
    echo "🗄️  设置数据库..."
    
    # 检查是否有Docker
    if command -v docker &> /dev/null; then
        echo "使用Docker启动PostgreSQL..."
        
        # 启动PostgreSQL容器
        docker run -d \
            --name digital-lazy-postgres \
            -e POSTGRES_DB=digital_lazy_platform \
            -e POSTGRES_USER=user \
            -e POSTGRES_PASSWORD=password \
            -p 5432:5432 \
            postgres:15
        
        # 启动Redis容器
        docker run -d \
            --name digital-lazy-redis \
            -p 6379:6379 \
            redis:7-alpine
        
        echo "等待数据库启动..."
        sleep 10
        
        # 运行数据库迁移
        cd backend
        source venv/bin/activate
        alembic upgrade head
        cd ..
        
        echo "✅ 数据库设置完成"
    else
        echo "⚠️  请手动安装并配置PostgreSQL和Redis"
        echo "   PostgreSQL: 数据库名 digital_lazy_platform, 用户 user, 密码 password"
        echo "   Redis: 默认配置，端口 6379"
    fi
}

# 创建开发脚本
create_dev_scripts() {
    echo "📝 创建开发脚本..."
    
    # 创建启动脚本
    cat > start-dev.sh << 'EOF'
#!/bin/bash

# 启动开发服务器

echo "🚀 启动数懒平台开发服务器..."

# 启动后端
echo "启动后端服务..."
cd backend
source venv/bin/activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 5

# 启动前端
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 开发服务器启动完成"
echo "   后端: http://localhost:8000"
echo "   前端: http://localhost:3000"
echo "   API文档: http://localhost:8000/docs"

# 等待用户中断
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

    chmod +x start-dev.sh

    # 创建停止脚本
    cat > stop-dev.sh << 'EOF'
#!/bin/bash

echo "🛑 停止开发服务器..."

# 停止所有相关进程
pkill -f "uvicorn app.main:app"
pkill -f "npm run dev"

echo "✅ 开发服务器已停止"
EOF

    chmod +x stop-dev.sh

    # 创建重置脚本
    cat > reset-dev.sh << 'EOF'
#!/bin/bash

echo "🔄 重置开发环境..."

# 停止服务
./stop-dev.sh

# 清理数据库
if command -v docker &> /dev/null; then
    docker stop digital-lazy-postgres digital-lazy-redis || true
    docker rm digital-lazy-postgres digital-lazy-redis || true
fi

# 重新设置
./scripts/dev-setup.sh

echo "✅ 开发环境重置完成"
EOF

    chmod +x reset-dev.sh

    echo "✅ 开发脚本创建完成"
}

# 主函数
main() {
    check_requirements
    setup_backend
    setup_frontend
    setup_database
    create_dev_scripts
    
    echo ""
    echo "🎉 数懒平台开发环境设置完成！"
    echo ""
    echo "📚 使用说明："
    echo "   启动开发服务器: ./start-dev.sh"
    echo "   停止开发服务器: ./stop-dev.sh"
    echo "   重置开发环境:   ./reset-dev.sh"
    echo ""
    echo "🌐 访问地址："
    echo "   前端应用: http://localhost:3000"
    echo "   后端API:  http://localhost:8000"
    echo "   API文档:  http://localhost:8000/docs"
    echo ""
    echo "⚠️  注意事项："
    echo "   1. 请确保端口 3000, 8000, 5432, 6379 未被占用"
    echo "   2. 首次运行前请检查 backend/.env 配置"
    echo "   3. 如需修改配置，请编辑相应的环境文件"
    echo ""
}

# 运行主函数
main "$@"
