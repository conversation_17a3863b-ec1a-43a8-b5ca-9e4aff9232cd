/**
 * OffscreenManager 单元测试
 * 测试离屏文档管理器的生命周期管理功能
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
  },
  offscreen: {
    createDocument: vi.fn(),
    closeDocument: vi.fn(),
    hasDocument: vi.fn(),
    Reason: {
      AUDIO_PLAYBACK: 'audio-playback',
      DOM_PARSER: 'dom-parser',
      WORKERS: 'workers',
    },
  },
};

Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true,
});

vi.mock('@/shared/utils', () => ({
  createLogger: () => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
}));

describe('OffscreenManager', () => {
  let offscreenManager: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // 重置chrome API mocks
    mockChrome.runtime.sendMessage.mockResolvedValue(undefined);
    mockChrome.offscreen.createDocument.mockResolvedValue(undefined);
    mockChrome.offscreen.closeDocument.mockResolvedValue(undefined);
    mockChrome.offscreen.hasDocument.mockResolvedValue(false);
    
    // 清除模块缓存并重新导入
    vi.resetModules();
    const module = await import('@/background/OffscreenManager');
    offscreenManager = module.offscreenManager;
    
    // 重置内部状态
    offscreenManager['_refCount'] = 0;
    offscreenManager['_creationPromise'] = null;
  });

  describe('引用计数管理', () => {
    it('应该在第一次 startAudio 时创建文档', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      
      await offscreenManager.startAudio();
      
      expect(offscreenManager['_refCount']).toBe(1);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledWith({
        url: '/offscreen.html',
        reasons: ['audio-playback', 'dom-parser', 'workers'],
        justification: 'To play silent audio for preventing throttling and to support background tasks.',
      });
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        target: 'offscreen-audio',
        type: 'PLAY_AUDIO',
      });
    });

    it('应该正确管理多个音频请求', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      
      // 第一个请求应该创建文档
      await offscreenManager.startAudio();
      expect(offscreenManager['_refCount']).toBe(1);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
      
      // 第二个请求应该增加引用计数但不创建新文档
      mockChrome.offscreen.hasDocument.mockResolvedValue(true);
      await offscreenManager.startAudio();
      expect(offscreenManager['_refCount']).toBe(2);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
    });

    it('应该在最后一个 stopAudio 时关闭文档', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(true);
      
      // 设置初始状态
      offscreenManager['_refCount'] = 2;
      
      // 第一次停止，不应该关闭文档
      await offscreenManager.stopAudio();
      expect(offscreenManager['_refCount']).toBe(1);
      expect(mockChrome.offscreen.closeDocument).not.toHaveBeenCalled();
      
      // 第二次停止，应该关闭文档
      await offscreenManager.stopAudio();
      expect(offscreenManager['_refCount']).toBe(0);
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        target: 'offscreen-audio',
        type: 'PAUSE_AUDIO',
      });
      expect(mockChrome.offscreen.closeDocument).toHaveBeenCalledTimes(1);
    });
  });

  describe('并发处理', () => {
    it('应该处理并发的文档创建请求', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      
      // 模拟慢速的文档创建
      let resolveCreate: () => void;
      const createPromise = new Promise<void>((resolve) => {
        resolveCreate = resolve;
      });
      mockChrome.offscreen.createDocument.mockReturnValue(createPromise);
      
      // 并发启动多个音频请求
      const promises = [
        offscreenManager.startAudio(),
        offscreenManager.startAudio(),
        offscreenManager.startAudio(),
      ];
      
      // 完成文档创建
      resolveCreate!();
      await Promise.all(promises);
      
      expect(offscreenManager['_refCount']).toBe(3);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
    });

    it('应该处理文档创建失败的情况', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      mockChrome.offscreen.createDocument.mockRejectedValue(new Error('Creation failed'));
      
      await expect(offscreenManager.startAudio()).rejects.toThrow('Creation failed');
      
      // 引用计数应该回滚
      expect(offscreenManager['_refCount']).toBe(0);
    });
  });

  describe('文档状态检查', () => {
    it('应该检查现有文档', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(true);
      
      await offscreenManager.startAudio();
      
      // 不应该创建新文档
      expect(mockChrome.offscreen.createDocument).not.toHaveBeenCalled();
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        target: 'offscreen-audio',
        type: 'PLAY_AUDIO',
      });
    });

    it('应该处理文档检查失败的情况', async () => {
      mockChrome.offscreen.hasDocument.mockRejectedValue(new Error('Check failed'));
      
      await offscreenManager.startAudio();
      
      // 应该尝试创建文档
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
    });
  });

  describe('音频消息处理', () => {
    it('应该处理音频播放消息失败', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      mockChrome.runtime.sendMessage.mockRejectedValue(new Error('Message failed'));
      
      await expect(offscreenManager.startAudio()).rejects.toThrow('Message failed');
      
      // 引用计数应该回滚
      expect(offscreenManager['_refCount']).toBe(0);
    });

    it('应该处理音频停止消息失败', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(true);
      mockChrome.runtime.sendMessage.mockRejectedValue(new Error('Pause failed'));
      
      offscreenManager['_refCount'] = 1;
      
      // 即使暂停失败，也应该继续关闭文档
      await offscreenManager.stopAudio();
      
      expect(mockChrome.offscreen.closeDocument).toHaveBeenCalledTimes(1);
    });
  });

  describe('错误处理', () => {
    it('应该处理重复的 stopAudio 调用', async () => {
      expect(offscreenManager['_refCount']).toBe(0);
      
      await offscreenManager.stopAudio();
      
      expect(offscreenManager['_refCount']).toBe(0);
      expect(mockChrome.offscreen.closeDocument).not.toHaveBeenCalled();
    });

    it('应该处理文档关闭失败', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(true);
      mockChrome.offscreen.closeDocument.mockRejectedValue(new Error('Close failed'));
      
      offscreenManager['_refCount'] = 1;
      
      await expect(offscreenManager.stopAudio()).rejects.toThrow('Close failed');
    });
  });

  describe('生命周期管理', () => {
    it('应该在正常使用场景下正确管理生命周期', async () => {
      mockChrome.offscreen.hasDocument
        .mockResolvedValueOnce(false) // 第一次检查，没有文档
        .mockResolvedValue(true);     // 后续检查，有文档
      
      // 启动多个音频会话
      await offscreenManager.startAudio();
      await offscreenManager.startAudio();
      await offscreenManager.startAudio();
      
      expect(offscreenManager['_refCount']).toBe(3);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
      
      // 停止部分会话
      await offscreenManager.stopAudio();
      await offscreenManager.stopAudio();
      
      expect(offscreenManager['_refCount']).toBe(1);
      expect(mockChrome.offscreen.closeDocument).not.toHaveBeenCalled();
      
      // 停止最后一个会话
      await offscreenManager.stopAudio();
      
      expect(offscreenManager['_refCount']).toBe(0);
      expect(mockChrome.offscreen.closeDocument).toHaveBeenCalledTimes(1);
    });

    it('应该处理快速的启动-停止序列', async () => {
      // 在启动时文档不存在，但在停止时需要检查文档是否存在来决定是否关闭
      mockChrome.offscreen.hasDocument
        .mockResolvedValueOnce(false)  // startAudio 时检查
        .mockResolvedValueOnce(true);  // stopAudio 时检查
      
      // 快速启动和停止
      await offscreenManager.startAudio();
      await offscreenManager.stopAudio();
      
      expect(offscreenManager['_refCount']).toBe(0);
      expect(mockChrome.offscreen.createDocument).toHaveBeenCalledTimes(1);
      expect(mockChrome.offscreen.closeDocument).toHaveBeenCalledTimes(1);
    });
  });

  describe('内存和状态管理', () => {
    it('应该在完成创建后清理创建Promise', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      
      await offscreenManager.startAudio();
      
      expect(offscreenManager['_creationPromise']).toBeNull();
    });

    it('应该在创建失败后清理创建Promise', async () => {
      mockChrome.offscreen.hasDocument.mockResolvedValue(false);
      mockChrome.offscreen.createDocument.mockRejectedValue(new Error('Failed'));
      
      await expect(offscreenManager.startAudio()).rejects.toThrow('Failed');
      
      expect(offscreenManager['_creationPromise']).toBeNull();
    });
  });
});