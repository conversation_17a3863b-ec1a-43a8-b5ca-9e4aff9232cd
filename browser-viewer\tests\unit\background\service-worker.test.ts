/**
 * Service Worker 单元测试
 * 测试后台服务工作器的核心功能
 */

import { MESSAGE_TYPES } from '@/shared/constants';
import type { ServiceMessage, TaskConfig } from '@/shared/types';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TestUtils } from '../../utils/test-helpers';

// Mock 所有依赖
vi.mock('@/core/behavior-engine/BehaviorEngine');
vi.mock('@/core/task-manager/TaskScheduler');
vi.mock('@/services/pool/SharedWorkerPool');
vi.mock('@/services/privacy-service/PrivacyService');
vi.mock('@/services/security-service/SecurityService');
vi.mock('@/background/OffscreenManager');
vi.mock('@/background/WorkerWindowManager');
vi.mock('webextension-polyfill', () => ({
  default: {
    storage: {
      local: {
        get: vi.fn().mockResolvedValue({}),
        set: vi.fn().mockResolvedValue(undefined),
        clear: vi.fn().mockResolvedValue(undefined)
      }
    },
    runtime: {
      onMessage: {
        addListener: vi.fn()
      }
    }
  }
}));
vi.mock('jszip', () => ({
  default: class MockJSZip {
    file() { return this; }
    generateAsync() { return Promise.resolve(new ArrayBuffer(0)); }
  }
}));

describe('Service Worker', () => {
  let mockChrome: any;
  let mockPort: any;

  beforeEach(() => {
    // Mock Chrome runtime APIs
    mockChrome = {
      runtime: {
        onInstalled: {
          addListener: vi.fn()
        },
        onStartup: {
          addListener: vi.fn()
        },
        onConnect: {
          addListener: vi.fn()
        },
        onMessage: {
          addListener: vi.fn()
        },
        sendMessage: vi.fn(),
        getURL: vi.fn().mockReturnValue('chrome-extension://test/')
      },
      storage: {
        local: {
          get: vi.fn().mockResolvedValue({}),
          set: vi.fn().mockResolvedValue(undefined),
          clear: vi.fn().mockResolvedValue(undefined)
        }
      },
      tabs: {
        create: vi.fn().mockResolvedValue({ id: 1 }),
        remove: vi.fn().mockResolvedValue(undefined),
        sendMessage: vi.fn().mockResolvedValue({})
      },
      windows: {
        create: vi.fn().mockResolvedValue({ id: 1 })
      },
      notifications: {
        create: vi.fn().mockResolvedValue('notification-id')
      }
    };

    // Mock Port
    mockPort = {
      name: 'popup',
      onMessage: {
        addListener: vi.fn()
      },
      onDisconnect: {
        addListener: vi.fn()
      },
      postMessage: vi.fn(),
      disconnect: vi.fn()
    };

    vi.stubGlobal('chrome', mockChrome);
    
    // 清理之前的模块缓存
    vi.resetModules();
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('Service Worker 初始化', () => {
    it('应该注册运行时事件监听器', async () => {
      // 动态导入service worker来触发构造函数
      await import('@/background/service-worker');

      expect(mockChrome.runtime.onInstalled.addListener).toHaveBeenCalled();
      expect(mockChrome.runtime.onStartup.addListener).toHaveBeenCalled();
      expect(mockChrome.runtime.onConnect.addListener).toHaveBeenCalled();
    });

    it('应该处理安装事件', async () => {
      await import('@/background/service-worker');

      // 获取onInstalled监听器并触发
      const onInstalledListener = mockChrome.runtime.onInstalled.addListener.mock.calls[0][0];
      
      // 调用监听器不应该抛出错误
      expect(() => onInstalledListener()).not.toThrow();
    });

    it('应该处理启动事件', async () => {
      await import('@/background/service-worker');

      // 获取onStartup监听器并触发
      const onStartupListener = mockChrome.runtime.onStartup.addListener.mock.calls[0][0];
      
      // 调用监听器不应该抛出错误
      expect(() => onStartupListener()).not.toThrow();
    });
  });

  describe('端口连接处理', () => {
    it('应该处理popup连接', async () => {
      await import('@/background/service-worker');

      // 获取onConnect监听器
      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      
      // 模拟popup连接
      onConnectListener(mockPort);

      expect(mockPort.onMessage.addListener).toHaveBeenCalled();
    });

    it('应该忽略非popup连接', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      
      // 模拟非popup连接
      const nonPopupPort = { 
        name: 'content-script',
        postMessage: vi.fn(),
        disconnect: vi.fn()
      };
      onConnectListener(nonPopupPort);

      // 非popup端口不应该添加消息监听器
      expect((nonPopupPort as any).onMessage?.addListener).toBeUndefined();
    });

    it('应该处理端口消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      // 获取消息监听器
      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];
      
      const testMessage: ServiceMessage<any> = {
        id: 'test-request',
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        payload: {},
        timestamp: Date.now()
      };

      // 调用消息监听器不应该抛出错误
      expect(() => messageListener(testMessage)).not.toThrow();
    });
  });

  describe('消息处理', () => {
    it('应该处理任务生命周期消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const taskMessages = [
        MESSAGE_TYPES.TASK_CREATE,
        MESSAGE_TYPES.TASK_PAUSE,
        MESSAGE_TYPES.TASK_RESUME,
        MESSAGE_TYPES.TASK_CANCEL,
        MESSAGE_TYPES.TASK_STATUS
      ];

      for (const messageType of taskMessages) {
        const message: ServiceMessage<any> = {
          id: `test-${messageType}`,
          type: messageType,
          payload: {},
          timestamp: Date.now()
        };

        expect(() => messageListener(message)).not.toThrow();
      }
    });

    it('应该处理捕获和内容消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const captureMessages = [
        MESSAGE_TYPES.CAPTURE_AND_PROCESS_VIEWPORT,
        MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED,
        MESSAGE_TYPES.CAPTURE_COMPLETED,
        MESSAGE_TYPES.CAPTURE_FAILED,
        MESSAGE_TYPES.CONTENT_SCRIPT_READY
      ];

      for (const messageType of captureMessages) {
        const message: ServiceMessage<any> = {
          id: `test-${messageType}`,
          type: messageType,
          payload: {},
          timestamp: Date.now()
        };

        expect(() => messageListener(message)).not.toThrow();
      }
    });

    it('应该处理开发工具消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const devMessages = [
        MESSAGE_TYPES.GET_TASKS_STATUS,
        MESSAGE_TYPES.CREATE_DEV_TASK,
        MESSAGE_TYPES.START_ALL_TASKS,
        MESSAGE_TYPES.PAUSE_ALL_TASKS,
        MESSAGE_TYPES.CLEAR_ALL_TASKS
      ];

      for (const messageType of devMessages) {
        const message: ServiceMessage<any> = {
          id: `test-${messageType}`,
          type: messageType,
          payload: {},
          timestamp: Date.now()
        };

        expect(() => messageListener(message)).not.toThrow();
      }
    });
  });

  describe('任务创建', () => {
    it('应该处理任务创建请求', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const taskConfig: TaskConfig = {
        urls: ['https://example.com'],
        behaviorProfile: 'normal',
        outputFormat: 'text',
        priority: 5,
        timeout: 30000,
        continueOnError: true
      };

      const createTaskMessage: ServiceMessage<TaskConfig> = {
        id: 'create-task-test',
        type: MESSAGE_TYPES.TASK_CREATE,
        payload: taskConfig,
        timestamp: Date.now()
      };

      expect(() => messageListener(createTaskMessage)).not.toThrow();
    });

    it('应该处理开发任务创建', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const devTaskMessage: ServiceMessage<{ urls: string[] }> = {
        id: 'create-dev-task-test',
        type: MESSAGE_TYPES.CREATE_DEV_TASK,
        payload: { urls: ['https://dev.example.com'] },
        timestamp: Date.now()
      };

      expect(() => messageListener(devTaskMessage)).not.toThrow();
    });
  });

  describe('任务状态管理', () => {
    it('应该处理任务状态查询', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const statusMessage: ServiceMessage<{}> = {
        id: 'status-test',
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        payload: {},
        timestamp: Date.now()
      };

      expect(() => messageListener(statusMessage)).not.toThrow();
    });

    it('应该处理批量任务操作', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const batchOperations = [
        MESSAGE_TYPES.START_ALL_TASKS,
        MESSAGE_TYPES.PAUSE_ALL_TASKS,
        MESSAGE_TYPES.CLEAR_ALL_TASKS
      ];

      for (const operation of batchOperations) {
        const message: ServiceMessage<{}> = {
          id: `batch-${operation}`,
          type: operation,
          payload: {},
          timestamp: Date.now()
        };

        expect(() => messageListener(message)).not.toThrow();
      }
    });
  });

  describe('内容脚本通信', () => {
    it('应该处理内容脚本就绪消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const readyMessage: ServiceMessage<{ tabId: number }> = {
        id: 'content-ready-test',
        type: MESSAGE_TYPES.CONTENT_SCRIPT_READY,
        payload: { tabId: 1 },
        timestamp: Date.now()
      };

      expect(() => messageListener(readyMessage)).not.toThrow();
    });

    it('应该处理捕获完成消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const captureMessage: ServiceMessage<{ segments: any[] }> = {
        id: 'capture-complete-test',
        type: MESSAGE_TYPES.CAPTURE_COMPLETED,
        payload: { segments: [] },
        timestamp: Date.now()
      };

      expect(() => messageListener(captureMessage)).not.toThrow();
    });

    it('应该处理捕获失败消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const failureMessage: ServiceMessage<{ error: string }> = {
        id: 'capture-failure-test',
        type: MESSAGE_TYPES.CAPTURE_FAILED,
        payload: { error: 'Test capture failure' },
        timestamp: Date.now()
      };

      expect(() => messageListener(failureMessage)).not.toThrow();
    });
  });

  describe('错误处理', () => {
    it('应该处理格式错误的消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const malformedMessage = {
        // 缺少必需的字段
        type: 'INVALID_TYPE'
      };

      expect(() => messageListener(malformedMessage)).not.toThrow();
    });

    it('应该处理未知消息类型', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const unknownMessage: ServiceMessage<any> = {
        id: 'unknown-test',
        type: 'UNKNOWN_MESSAGE_TYPE' as any,
        payload: {},
        timestamp: Date.now()
      };

      expect(() => messageListener(unknownMessage)).not.toThrow();
    });

    it('应该处理Chrome API错误', async () => {
      // 模拟Chrome API失败
      mockChrome.storage.local.get.mockRejectedValue(new Error('Storage error'));

      await import('@/background/service-worker');

      const onInstalledListener = mockChrome.runtime.onInstalled.addListener.mock.calls[0][0];
      
      // 即使存储失败，也不应该抛出错误
      expect(() => onInstalledListener()).not.toThrow();
    });
  });

  describe('生命周期管理', () => {
    it('应该支持重复初始化', async () => {
      await import('@/background/service-worker');

      const onInstalledListener = mockChrome.runtime.onInstalled.addListener.mock.calls[0][0];
      
      // 多次触发安装事件
      expect(() => {
        onInstalledListener();
        onInstalledListener();
        onInstalledListener();
      }).not.toThrow();
    });

    it('应该处理端口断开', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      // 模拟端口断开
      mockPort.disconnect();

      // 端口断开后发送消息不应该导致错误
      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];
      const message: ServiceMessage<any> = {
        id: 'after-disconnect',
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        payload: {},
        timestamp: Date.now()
      };

      expect(() => messageListener(message)).not.toThrow();
    });
  });

  describe('性能考虑', () => {
    it('应该快速处理消息', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const measure = new TestUtils.PerformanceMeasure();

      const message: ServiceMessage<any> = {
        id: 'performance-test',
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        payload: {},
        timestamp: Date.now()
      };

      messageListener(message);

      measure.mark('message_processed');
      expect(measure.getMeasurement('message_processed')).toBeLessThan(50);
    });

    it('应该支持并发消息处理', async () => {
      await import('@/background/service-worker');

      const onConnectListener = mockChrome.runtime.onConnect.addListener.mock.calls[0][0];
      onConnectListener(mockPort);

      const messageListener = mockPort.onMessage.addListener.mock.calls[0][0];

      const messages = Array.from({ length: 100 }, (_, i) => ({
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        data: {},
        tabId: 1,
        requestId: `concurrent-${i}`
      }));

      const measure = new TestUtils.PerformanceMeasure();

      messages.forEach(message => messageListener(message));

      measure.mark('concurrent_messages_processed');
      expect(measure.getMeasurement('concurrent_messages_processed')).toBeLessThan(500);
    });
  });
});