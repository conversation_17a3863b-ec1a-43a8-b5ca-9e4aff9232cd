/**
 * @file StateSnapshot.ts
 * @description 捕获和恢复浏览器标签页的状态。
 */
import type { TabId, TabState } from '@/shared/types';
import { createLogger } from '@/shared/utils';

/**
 * 提供捕获标签页完整快照（包括DOM、滚动位置、存储）及后续恢复的功能。
 * 这是从错误中恢复或进行模式切换的关键。
 */
export class StateSnapshot {
  private readonly _logger = createLogger('StateSnapshot');

  constructor() {
    this._logger.info('StateSnapshot service initialized');
  }

  /**
   * 捕获指定标签页的完整状态。
   * @param tabId - 要捕获的标签页ID。
   * @returns 返回一个Promise，解析为捕获到的标签页状态。
   */
  public async capture(tabId: TabId): Promise<TabState | null> {
    this._logger.info(`正在为标签页 ${tabId} 捕获状态`);

    // 实现技术文档中定义的状态捕获逻辑
    // 在目标标签页中执行脚本以获取DOM、滚动位置和存储数据
    const [injectionResult] = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return {
          url: window.location.href,
          dom: document.documentElement.outerHTML,
          scrollPosition: { x: window.scrollX, y: window.scrollY },
          localStorage: { ...localStorage },
          sessionStorage: { ...sessionStorage },
          formData: {}, // 表单数据提取的占位符
        };
      },
    });

    if (!injectionResult?.result) {
      this._logger.error(`无法从标签页 ${tabId} 注入和捕获结果。`);
      return null;
    }

    const injectedData = injectionResult.result;

    const cookies = await chrome.cookies.getAll({ url: injectedData.url });

    const state: TabState = {
      timestamp: Date.now(),
      cookies,
      ...injectedData,
    };

    this._logger.info(`已成功捕获标签页 ${tabId} 的状态`);
    return state;
  }

  /**
   * 将标签页恢复到先前捕获的状态。
   * @param tabId - 要恢复状态的标签页ID。
   * @param _state - 要恢复的状态对象。
   */
  public restore(tabId: TabId, _state: TabState): void {
    this._logger.info(`正在为标签页 ${tabId} 恢复状态`);
    // TODO：状态恢复是一个复杂且潜在的破坏性操作
    // 当前实现为安全的基础版本，仅提供日志记录
    // 完整实现需要谨慎处理DOM替换、Cookie设置和存储恢复
    this._logger.warn(`标签页 ${tabId} 的状态恢复功能尚未完全实现，仅记录日志。`);
  }
} 