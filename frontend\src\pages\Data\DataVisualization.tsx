/**
 * 数据可视化页面
 * 展示数据统计、图表、分析结果
 */

import React, { useEffect, useState } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  Space,
  Select,
  DatePicker,
  Typography,
  Tabs,
  Progress,
  Empty,
} from 'antd'
import {
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  DownloadOutlined,
  FilterOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { Line, Bar, Pie } from '@ant-design/plots'

import { DataType, DataStatus } from '../../types/task'
import { dataApi } from '../../services/api'

const { Title } = Typography
const { RangePicker } = DatePicker
const { Option } = Select
const { TabPane } = Tabs

const DataVisualization: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [dataList, setDataList] = useState<any[]>([])
  const [filters, setFilters] = useState<any>({})
  const [stats, setStats] = useState<any>({})

  useEffect(() => {
    fetchData()
  }, [filters])

  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await dataApi.getData(filters)
      setDataList(response.data || [])
      
      // 计算统计数据
      const totalData = response.data?.length || 0
      const processedData = response.data?.filter((item: any) => 
        item.status === DataStatus.PROCESSED
      ).length || 0
      const failedData = response.data?.filter((item: any) => 
        item.status === DataStatus.FAILED
      ).length || 0
      
      setStats({
        total: totalData,
        processed: processedData,
        failed: failedData,
        successRate: totalData > 0 ? Math.round((processedData / totalData) * 100) : 0,
      })
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setLoading(false)
    }
  }

  // 数据类型分布图表数据
  const dataTypeDistribution = Object.values(DataType).map(type => {
    const count = dataList.filter(item => item.data_type === type).length
    return {
      type: type,
      value: count,
    }
  }).filter(item => item.value > 0)

  // 数据状态分布图表数据
  const dataStatusDistribution = Object.values(DataStatus).map(status => {
    const count = dataList.filter(item => item.status === status).length
    return {
      status: status,
      value: count,
    }
  }).filter(item => item.value > 0)

  // 时间趋势图表数据（按天统计）
  const timeTrendData = dataList.reduce((acc: any[], item) => {
    const date = new Date(item.created_at).toISOString().split('T')[0]
    const existing = acc.find(d => d.date === date)
    if (existing) {
      existing.count += 1
    } else {
      acc.push({ date, count: 1 })
    }
    return acc
  }, []).sort((a, b) => a.date.localeCompare(b.date))

  // 数据表格列定义
  const columns = [
    {
      title: '数据名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'data_type',
      key: 'data_type',
      render: (type: DataType) => {
        const typeConfig: Record<DataType, { color: string; text: string }> = {
          [DataType.TEXT]: { color: 'blue', text: '文本' },
          [DataType.IMAGE]: { color: 'green', text: '图片' },
          [DataType.TABLE]: { color: 'orange', text: '表格' },
          [DataType.FORM]: { color: 'purple', text: '表单' },
          [DataType.LINK]: { color: 'cyan', text: '链接' },
          [DataType.DOCUMENT]: { color: 'red', text: '文档' },
          [DataType.SCREENSHOT]: { color: 'magenta', text: '截图' },
          [DataType.OTHER]: { color: 'default', text: '其他' },
        }
        const config = typeConfig[type]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: DataStatus) => {
        const statusConfig: Record<DataStatus, { color: string; text: string }> = {
          [DataStatus.RAW]: { color: 'default', text: '原始' },
          [DataStatus.PROCESSING]: { color: 'processing', text: '处理中' },
          [DataStatus.PROCESSED]: { color: 'success', text: '已处理' },
          [DataStatus.FAILED]: { color: 'error', text: '失败' },
          [DataStatus.ARCHIVED]: { color: 'warning', text: '已归档' },
        }
        const config = statusConfig[status]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => {
        if (!size) return '-'
        if (size < 1024) return `${size} B`
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
        return `${(size / (1024 * 1024)).toFixed(1)} MB`
      },
    },
    {
      title: '访问次数',
      dataIndex: 'access_count',
      key: 'access_count',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>数据可视化</Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="总数据量" value={stats.total} />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已处理"
              value={stats.processed}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="处理失败"
              value={stats.failed}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="成功率"
              value={stats.successRate}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Select
              placeholder="数据类型"
              allowClear
              style={{ width: 120 }}
              onChange={(value) => setFilters({ ...filters, data_type: value })}
            >
              {Object.values(DataType).map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="数据状态"
              allowClear
              style={{ width: 120 }}
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              {Object.values(DataStatus).map(status => (
                <Option key={status} value={status}>{status}</Option>
              ))}
            </Select>
          </Col>
          <Col>
            <RangePicker
              onChange={(dates) => {
                if (dates) {
                  setFilters({
                    ...filters,
                    date_from: dates[0]?.toISOString(),
                    date_to: dates[1]?.toISOString(),
                  })
                } else {
                  const { date_from, date_to, ...rest } = filters
                  setFilters(rest)
                }
              }}
            />
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchData}
                loading={loading}
              >
                刷新
              </Button>
              <Button icon={<DownloadOutlined />}>
                导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 图表和数据 */}
      <Tabs defaultActiveKey="charts">
        <TabPane tab="数据图表" key="charts">
          <Row gutter={[16, 16]}>
            {/* 数据类型分布 */}
            <Col xs={24} lg={8}>
              <Card title="数据类型分布" style={{ height: 400 }}>
                {dataTypeDistribution.length > 0 ? (
                  <Pie
                    data={dataTypeDistribution}
                    angleField="value"
                    colorField="type"
                    radius={0.8}
                    label={{
                      type: 'outer',
                      content: '{name} {percentage}',
                    }}
                    interactions={[{ type: 'element-active' }]}
                  />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </Card>
            </Col>

            {/* 数据状态分布 */}
            <Col xs={24} lg={8}>
              <Card title="数据状态分布" style={{ height: 400 }}>
                {dataStatusDistribution.length > 0 ? (
                  <Bar
                    data={dataStatusDistribution}
                    xField="value"
                    yField="status"
                    seriesField="status"
                    legend={false}
                  />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </Card>
            </Col>

            {/* 时间趋势 */}
            <Col xs={24} lg={8}>
              <Card title="数据创建趋势" style={{ height: 400 }}>
                {timeTrendData.length > 0 ? (
                  <Line
                    data={timeTrendData}
                    xField="date"
                    yField="count"
                    point={{
                      size: 5,
                      shape: 'diamond',
                    }}
                    label={{
                      style: {
                        fill: '#aaa',
                      },
                    }}
                  />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="数据列表" key="list">
          <Card>
            <Table
              columns={columns}
              dataSource={dataList}
              loading={loading}
              rowKey="id"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="数据分析" key="analysis">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="处理效率分析">
                <div style={{ padding: '20px 0' }}>
                  <div style={{ marginBottom: 16 }}>
                    <span>整体处理成功率</span>
                    <Progress
                      percent={stats.successRate}
                      status={stats.successRate > 80 ? 'success' : 'normal'}
                      style={{ marginLeft: 16 }}
                    />
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <span>数据完整性</span>
                    <Progress
                      percent={85}
                      strokeColor="#52c41a"
                      style={{ marginLeft: 16 }}
                    />
                  </div>
                  <div>
                    <span>存储利用率</span>
                    <Progress
                      percent={67}
                      strokeColor="#1890ff"
                      style={{ marginLeft: 16 }}
                    />
                  </div>
                </div>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="数据质量评估">
                <div style={{ padding: '20px 0' }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="平均文件大小"
                        value={1.2}
                        suffix="MB"
                        precision={1}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="平均处理时间"
                        value={2.3}
                        suffix="秒"
                        precision={1}
                      />
                    </Col>
                  </Row>
                  <Row gutter={16} style={{ marginTop: 16 }}>
                    <Col span={12}>
                      <Statistic
                        title="重复数据率"
                        value={5.2}
                        suffix="%"
                        precision={1}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="数据完整性"
                        value={94.8}
                        suffix="%"
                        precision={1}
                      />
                    </Col>
                  </Row>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default DataVisualization
