# 数懒平台后端依赖包
# 基于Python + FastAPI的微服务架构

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.1
aioredis==2.0.1

# 消息队列
celery==5.3.4
kombu==5.3.4
aio-pika==9.3.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 数据处理
pandas==2.1.4
numpy==1.25.2
pillow==10.1.0
opencv-python==********

# AI/ML
openai==1.3.8
transformers==4.36.2
torch==2.1.2
torchvision==0.16.2
scikit-learn==1.3.2

# 图像处理
pytesseract==0.3.10
pdf2image==1.16.3

# 工具库
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0
loguru==0.7.2
structlog==23.2.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 文件处理
aiofiles==23.2.1
python-magic==0.4.27

# 监控和指标
prometheus-client==0.19.0
psutil==5.9.6

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 部署
gunicorn==21.2.0
docker==6.1.3

# WebSocket
websockets==12.0
python-socketio==5.10.0

# 任务调度
apscheduler==3.10.4
croniter==2.0.1

# 配置管理
dynaconf==3.2.4
pyyaml==6.0.1

# 缓存
aiocache==0.12.2

# 限流
slowapi==0.1.9

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 类型检查
types-redis==4.6.0.11
types-requests==2.31.0.10
