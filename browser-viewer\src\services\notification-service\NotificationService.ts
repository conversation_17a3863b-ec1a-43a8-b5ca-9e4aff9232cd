/**
 * @file NotificationService.ts
 * @description 提供一个简单的服务，用于向用户显示浏览器通知。
 */
import { createLogger } from '@/shared/utils';

/**
 * 通知选项
 */
export interface NotificationOptions {
  title: string;
  message: string;
  iconUrl?: string; // 默认为插件图标
}

/**
 * NotificationService 封装了 chrome.notifications API，提供统一的通知创建方法。
 */
export class NotificationService {
  private readonly _logger = createLogger('NotificationService');
  private readonly _defaultIconUrl = chrome.runtime.getURL('assets/icons/icon128.png');
  private _counter = 0; // 添加计数器确保ID唯一性

  constructor() {
    this._logger.info('插件通知服务已连接。');
  }

  /**
   * 创建并显示一个浏览器通知。
   * @param options - 通知的配置选项。
   * @param notificationId - (可选) 通知的唯一ID，用于更新或清除。
   */
  public async show(options: NotificationOptions, notificationId?: string): Promise<void> {
    const id = notificationId ?? `agent-notification-${Date.now()}-${++this._counter}`;
    
    try {
      await chrome.notifications.create(id, {
        type: 'basic',
        iconUrl: options.iconUrl ?? this._defaultIconUrl,
        title: options.title,
        message: options.message,
      });
      this._logger.info(`已显示通知: "${options.title}"`);
    } catch (error) {
      this._logger.error('显示通知时出错:', error);
    }
  }
} 