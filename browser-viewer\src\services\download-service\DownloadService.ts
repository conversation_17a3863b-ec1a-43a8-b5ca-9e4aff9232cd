/**
 * 下载服务
 *
 * @description
 * 负责处理与文件下载相关的逻辑，特别是将任务产物（如截图）打包成ZIP并提供给用户下载。
 */
import { Task } from '@/core/task-manager/Task';
import { ImageSegment } from '@/shared/types';
import { createLogger, Logger } from '@/shared/utils';
import JSZip from 'jszip';

export class DownloadService {
  private static _instance: DownloadService;
  private readonly _logger: Logger = createLogger('DownloadService');

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  public static getInstance(): DownloadService {
    if (!DownloadService._instance) {
      DownloadService._instance = new DownloadService();
    }
    return DownloadService._instance;
  }

  /**
   * 将指定任务和URL的截图资源打包成ZIP并触发浏览器下载。
   * @param task - 目标任务实例。
   * @param url - 要下载其截图的目标URL。
   */
  public async downloadTaskAssetsAsZip(task: Task, url: string): Promise<void> {
    this._logger.info(`开始处理任务 ${task._id} 的结果下载，URL: ${url}`);

    let images: ImageSegment[];
    try {
      images = task.getCapturedImages(url);
    } catch (getImagesError: unknown) {
      const errorMessage = getImagesError instanceof Error ? getImagesError.message : String(getImagesError);
      this._logger.error(`获取任务 ${task._id} 的截图失败:`, errorMessage);
      throw new Error(`获取截图失败: ${errorMessage}`);
    }

    if (!images || images.length === 0) {
      this._logger.warn(`在任务 ${task._id} 中未找到URL ${url} 的截图。`);
      throw new Error(`未找到URL ${url} 的截图。`);
    }

    this._logger.info(`发现 ${images.length} 张图片，将为 ${url} 打包。`);

    try {
      const zip = new JSZip();
      images.forEach((image, index: number) => {
        const blob = new Blob([image.data], { type: image.mimeType });
        zip.file(`capture-${index + 1}.jpg`, blob);
      });

      const zipBase64 = await zip.generateAsync({ type: 'base64' });
      const downloadUrl = `data:application/zip;base64,${zipBase64}`;

      const urlParts = url.replace(/^(https?:\/\/)/, '').split('/');
      const domain = urlParts[0];
      const filename = `agent-captures-${domain}-${Date.now()}.zip`;

      chrome.downloads.download({
        url: downloadUrl,
        filename: filename,
        saveAs: true,
      }, (downloadId) => {
        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message ?? '未知下载错误';
          this._logger.error('下载失败:', errorMessage);
        } else if (downloadId) {
          this._logger.info(`已开始下载，ID: ${downloadId}`);
        } else {
            this._logger.warn('下载被用户取消或因未知原因失败。');
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this._logger.error('创建ZIP文件或开始下载时出错:', errorMessage);
      throw new Error(`ZIP压缩过程中出错: ${errorMessage}`);
    }
  }
} 