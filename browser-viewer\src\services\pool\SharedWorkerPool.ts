/**
 * Shared Worker Pool Service - 一个全局单例，用于管理和分发CPU密集型计算任务。
 * 
 * 设计目标：
 * 1.  **资源统一管理**：避免系统不同部分（如多个并发的CaptureSystem）各自创建工作池，导致线程数超载。
 * 2.  **避免资源争抢**：所有计算任务进入同一个队列，由有限的Worker执行，实现有序的资源共享而非争抢。
 * 3.  **提高利用率**：确保在任何时候，CPU计算核心都能被充分利用，同时为UI和其他关键任务留出余地。
 */
import type { ContentSegment, ImageSegment, ServiceMessage } from '@/shared/types';
import { createLogger, Logger } from '@/shared/utils';
import { offscreenManager } from '../../background/OffscreenManager';

// 定义Worker返回的可能结果类型
type WorkerResult = ContentSegment | { error: string; segmentId: string };

const OFFSCREEN_DOCUMENT_PATH = '/offscreen.html';

// 用于追踪发送到 Offscreen Document 并等待结果的任务
interface PendingTask {
  resolve: (result: WorkerResult) => void;
  reject: (reason?: unknown) => void;
}

export class SharedWorkerPoolService {
  private readonly _logger: Logger;
  private readonly _pendingTasks = new Map<string, PendingTask>();
  private _nextTaskId = 0;
  
  public constructor() {
    this._logger = createLogger('[SharedWorkerPoolService]');
    // 在构造函数中立即开始监听来自 Offscreen Document 的消息
    chrome.runtime.onMessage.addListener(this._handleMessage);
  }

  private readonly _handleMessage = (message: ServiceMessage, sender: chrome.runtime.MessageSender): void => {
    if (!this._isValidWorkerMessage(message, sender)) {
      return;
    }

    const payload = message.payload as { taskId: string; result?: WorkerResult; error?: string };
    this._processWorkerResult(payload);
  }

  private _isValidWorkerMessage(message: ServiceMessage, sender: chrome.runtime.MessageSender): boolean {
    return Boolean(
      sender.url?.endsWith(OFFSCREEN_DOCUMENT_PATH) &&
      message.type === 'WORKER_RESULT' &&
      message.payload &&
      typeof message.payload === 'object' &&
      'taskId' in message.payload
    );
  }

  private _processWorkerResult(payload: { taskId: string; result?: WorkerResult; error?: string }): void {
    const { taskId, result, error } = payload;
    const task = this._pendingTasks.get(taskId);

    if (!task) {
      this._logger.warn(`Received result for unknown task: ${taskId}`);
      return;
    }

    try {
      if (error && typeof error === 'string') {
        this._logger.error(`Task ${taskId} failed in worker:`, error);
        task.reject(new Error(error));
      } else if (result) {
        task.resolve(result);
      } else {
        task.reject(new Error('Invalid worker response'));
      }
    } finally {
      this._pendingTasks.delete(taskId);
    }
  }
  
  /**
   * 向共享池提交一个计算任务。
   * @param data 要处理的图像分段。
   * @param transfer 可选的、需要转移所有权的对象数组（如ImageBitmap）。
   * @returns 一个解析为处理结果的Promise。
   */
  public async run(data: ImageSegment, transfer?: Transferable[]): Promise<WorkerResult> {
    // 1. 请求Offscreen Document的使用权
    await offscreenManager.startAudio();
    
    const taskId = `task-${this._nextTaskId++}`;

    return new Promise<WorkerResult>((resolve, reject) => {
      this._pendingTasks.set(taskId, { resolve, reject });
      
      void chrome.runtime.sendMessage({
        type: 'EXECUTE_WORKER_TASK',
        target: 'offscreen-compute',
        payload: {
          taskId,
          data,
          transfer,
        }
      });
    }).finally(() => {
      // 2. 无论任务成功或失败，都释放使用权
      void offscreenManager.stopAudio();
    });
  }

  /**
   * 销毁服务，清理所有挂起的任务和监听器。
   */
  public destroy(): void {
    this._logger.info('Destroying the SharedWorkerPoolService.');
    this._pendingTasks.forEach(task => task.reject(new Error('SharedWorkerPoolService is being destroyed.')));
    this._pendingTasks.clear();
    
    chrome.runtime.onMessage.removeListener(this._handleMessage);
  }
}

// 显式初始化
// 确保 Worker 的创建时机完全由 Service Worker 控制，
// 避免在模块导入时就意外实例化而导致在错误上下文中创建 Worker 的问题。

/**
 * 全局共享的 Worker 池单例。
 * @type {SharedWorkerPoolService | null}
 * @description 该变量在初始化之前为 null。
 */
let _sharedWorkerPool: SharedWorkerPoolService | null = null;

/**
 * 初始化全局共享的 Worker 池。
 * @description 此函数必须且只能在 service-worker.ts 的初始化流程中调用一次。
 * 它负责创建 Worker 池的单例，并将其赋值给 sharedWorkerPool 变量。
 */
export function initializeSharedWorkerPool(): void {
  if (_sharedWorkerPool) {
    createLogger('[SharedWorkerPool]').debug('Shared worker pool is already initialized.');
    return;
  }
  _sharedWorkerPool = new SharedWorkerPoolService();
  createLogger('[SharedWorkerPool]').info('插件共享 Worker 池已连接。');
}

/**
 * 获取已初始化的全局共享 Worker 池实例。
 * @description 其他模块（如 ImageProcessingPipeline）通过此函数来安全地获取 Worker 池实例。
 * 如果在调用时 Worker 池尚未初始化，将抛出错误，以保证流程的正确性。
 * @returns {SharedWorkerPoolService} Worker 池的单例实例。
 */
export function getSharedWorkerPool(): SharedWorkerPoolService {
  if (!_sharedWorkerPool) {
    throw new Error('Shared worker pool has not been initialized. Please call initializeSharedWorkerPool() in the service worker first.');
  }
  return _sharedWorkerPool;
}

/**
 * 只读访问器，用于检查是否已初始化（可选）
 */
export const getSharedWorkerPoolInstance = (): SharedWorkerPoolService | null => _sharedWorkerPool; 