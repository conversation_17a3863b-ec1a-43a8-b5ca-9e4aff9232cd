/**
 * 行为分析器 - 分析和优化行为模式
 */

import type { Point } from '@/shared/types';
import { createLogger } from '@/shared/utils';

interface ActionRecord {
  type: string;
  timestamp: number;
  position?: Point;
  duration?: number;
}

export interface AnalysisResult {
  isAnomalous: boolean;
  reason?: string;
  recommendation?: 'INCREASE_RANDOMNESS' | 'ADJUST_TIMING' | 'VARY_PATHS' | 'VARY_ACTION_TYPES';
}

export class BehaviorAnalyzer {
  private readonly _logger = createLogger('BehaviorAnalyzer');
  private _isAnalyzing = false;
  
  start(): void {
    this._isAnalyzing = true;
    this._logger.info('BehaviorAnalyzer started');
  }
  
  stop(): void {
    this._isAnalyzing = false;
    this._logger.info('BehaviorAnalyzer stopped');
  }
  
  isRunning(): boolean {
    return this._isAnalyzing;
  }
  
  analyze(history: readonly ActionRecord[]): AnalysisResult {
    if (!this._isAnalyzing || history.length < 10) { // Need more data for entropy analysis
      return { isAnomalous: false };
    }
    
    this._logger.debug(`Analyzing action history of length ${history.length}`);
    
    // 检查1：操作间隔的一致性 (保留方差检查，因为它对定时检测依然有效)
    const intervals = [];
    for (let i = 1; i < history.length; i++) {
      intervals.push(history[i].timestamp - history[i - 1].timestamp);
    }
    const intervalVariance = this._calculateVariance(intervals);
    if (intervalVariance < 100) { // 方差过小，说明时间间隔太固定
      return {
        isAnomalous: true,
        reason: 'Action intervals are too consistent.',
        recommendation: 'ADJUST_TIMING'
      };
    }

    // 检查2：行为序列的复杂度 (新增)
    const actionTypes = history.map(h => h.type);
    const actionTypeEntropy = this._calculateEntropy(actionTypes);
    if (actionTypeEntropy < 0.8) { // 熵值过低，说明行为模式单一，如反复"滚动-点击"
        return {
            isAnomalous: true,
            reason: 'Action sequence is too predictable (low entropy).',
            recommendation: 'VARY_ACTION_TYPES'
        };
    }
    
    // 检查3：点击位置的分布 (用熵分析取代方差分析)
    const clickPositions = history
      .filter(h => h.type === 'click' && h.position)
      .map(h => h.position as Point);

    if (clickPositions.length > 5) {
      // 将视口划分为100x100像素的网格，计算点击落入的网格单元
      const gridCells = clickPositions.map(p => {
          const gridX = Math.floor((p?.x || 0) / 100);
          const gridY = Math.floor((p?.y || 0) / 100);
          return `${gridX}_${gridY}`;
      });
      const positionEntropy = this._calculateEntropy(gridCells);
      if (positionEntropy < 1.0) { // 位置熵过低，说明点击过于集中
        return {
          isAnomalous: true,
          reason: 'Click positions are too clustered (low spatial entropy).',
          recommendation: 'VARY_PATHS'
        };
      }
    }
    
    this._logger.debug('Behavior analysis found no anomalies.');
    return { isAnomalous: false };
  }
  
  private _calculateVariance(numbers: number[]): number {
    if (numbers.length < 2) return 0;
    
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squareDiffs = numbers.map(value => Math.pow(value - mean, 2));
    const variance = squareDiffs.reduce((a, b) => a + b, 0) / numbers.length;
    
    return variance;
  }

  private _calculateEntropy(values: (string | number)[]): number {
    if (values.length === 0) return 0;

    const counts = new Map<string | number, number>();
    for (const value of values) {
        counts.set(value, (counts.get(value) ?? 0) + 1);
    }

    let entropy = 0;
    const total = values.length;
    for (const count of counts.values()) {
        const probability = count / total;
        if (probability > 0) {
            entropy -= probability * Math.log2(probability);
        }
    }

    return entropy;
  }
}