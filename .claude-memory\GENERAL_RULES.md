# 项目通用规则

## 代码开发规则

### 1. 代码修改原则

- 优先编辑现有文件，避免创建新文件
- 遵循现有代码架构和设计模式
- 保持代码风格一致性
- 修改前先理解代码意图和上下文

### 2. 爬虫开发规范

- 使用Playwright进行浏览器自动化
- 遵循异步编程模式（async/await）
- 保持爬虫模块的独立性和可扩展性
- 实现会话管理和Cookie持久化
- 添加反反爬措施（playwright-stealth）

### 3. OCR集成规范

- 使用Mistral AI OCR API进行图像识别
- 实现OCR功能的优雅降级处理
- 配置API密钥的安全管理
- 支持批量图像处理和结果存储
- 控制API调用频率避免超限

### 4. 数据存储规范

- 截图文件存储在`.crawler_results/images/`
- OCR识别结果存储在`.crawler_results/ocr/`
- 会话数据存储在`.crawler_profile/`
- 使用JSON格式保存结构化数据

### 5. 配置和环境管理

- 使用.env文件管理敏感配置（API密钥等）
- 支持环境变量的动态加载
- 提供.env.example作为配置模板
- 实现配置验证和错误处理

## 交流和工作规则

### 1. 语言使用

- **必须使用中文与用户对话**
- 代码注释可以使用英文
- 文档和说明使用中文

### 2. 任务管理

- 每个任务都要创建对应的记录文件
- 实时更新任务进展状态
- 完成任务后及时标记完成

### 3. 任务失败处理原则

- **环境验证优先**：任何涉及外部依赖的任务，开始前必须验证环境可用性
- **设置合理超时**：为Docker、网络请求等操作预留充足时间（至少3-5分钟）
- **准备降级方案**：主要方案失败时，提供替代验证方法
- **及时标记失败**：遇到核心环境问题无法解决时，立即终止任务并记录原因
- **记录环境信息**：失败时详细记录系统环境、版本信息和错误现象

### 4. 状态记忆管理

- 使用.claude-memory文件夹管理所有状态
- 每个任务创建独立的markdown记录
- 定期更新和维护规则文件

### 5. 安全原则

- 不泄露敏感信息
- 不提交密钥和配置到代码库
- 遵循最佳安全实践

---
*此文件会根据项目进展和新的规则要求持续更新*
