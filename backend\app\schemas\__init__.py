"""
Pydantic模式定义
用于API请求和响应的数据验证
"""

from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.schemas.task import (
    TaskCreateRequest,
    TaskUpdate,
    TaskResponse,
    BatchResponse,
    TaskMetrics,
)
from app.schemas.data import DataResponse, DataQuery, DataUploadResponse
from app.schemas.common import BaseResponse, PaginatedResponse

__all__ = [
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "TaskCreateRequest",
    "TaskUpdate",
    "TaskResponse",
    "BatchResponse",
    "TaskMetrics",
    "DataResponse",
    "DataQuery",
    "DataUploadResponse",
    "BaseResponse",
    "PaginatedResponse",
]
