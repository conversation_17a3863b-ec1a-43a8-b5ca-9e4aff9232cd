/**
 * BehaviorEngine 单元测试
 * 测试行为引擎核心功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TEST_TIMEOUTS } from '../../../fixtures/test-constants';
import { TestUtils } from '../../../utils/test-helpers';

describe('BehaviorEngine', () => {
  let behaviorEngine: any;
  let humanBehaviorSimulator: any;
  let behaviorAnalyzer: any;

  beforeEach(() => {
    humanBehaviorSimulator = {
      simulateScroll: vi.fn().mockResolvedValue(undefined),
      simulateClick: vi.fn().mockResolvedValue(undefined),
      simulateSafeClick: vi.fn().mockResolvedValue(undefined),
      generateBezierPath: vi.fn().mockReturnValue([
        { x: 0, y: 0 },
        { x: 50, y: 100 },
        { x: 100, y: 200 },
      ]),
      generateRandomDelay: vi.fn().mockReturnValue(200),
    };

    behaviorAnalyzer = {
      calculateSequenceEntropy: vi.fn().mockReturnValue(0.8),
      calculateClickDistributionEntropy: vi.fn().mockReturnValue(0.7),
      analyzePattern: vi.fn().mockReturnValue({
        sequenceEntropy: 0.8,
        clickDistribution: 0.7,
        suspicious: false,
      }),
    };

    behaviorEngine = {
      humanSimulator: humanBehaviorSimulator,
      analyzer: behaviorAnalyzer,
      simulateFullPageScan: vi.fn().mockResolvedValue(undefined),
      simulateHumanInteraction: vi.fn().mockResolvedValue(undefined),
      analyzeBehaviorPattern: vi.fn().mockImplementation(() => 
        behaviorAnalyzer.analyzePattern()
      ),
      adjustBehaviorStrategy: vi.fn().mockResolvedValue(undefined),
    };
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('页面扫描模拟', () => {
    it('应该执行完整的页面扫描流程', async () => {
      const tabId = 1;
      const sessionId = 'test-session';

      await behaviorEngine.simulateFullPageScan(tabId, sessionId);
      
      expect(behaviorEngine.simulateFullPageScan).toHaveBeenCalledWith(tabId, sessionId);
    });

    it('应该生成自然的滚动路径', () => {
      const scrollOptions = {
        start: { x: 0, y: 0 },
        end: { x: 0, y: 1000 },
        controlPoints: [
          { x: 20, y: 250 },
          { x: -10, y: 750 },
        ],
      };

      const path = humanBehaviorSimulator.generateBezierPath(scrollOptions);
      
      expect(path).toHaveLength(3);
      expect(path[0]).toEqual({ x: 0, y: 0 });
      expect(path[2].y).toBeGreaterThan(path[0].y);
    });

    it('应该在滚动间隔中添加随机延迟', () => {
      const baseDelay = 400;
      const randomness = 0.2;

      const delay = humanBehaviorSimulator.generateRandomDelay(baseDelay, randomness);
      
      expect(delay).toBeGreaterThan(0);
      expect(typeof delay).toBe('number');
    });
  });

  describe('人类行为模拟', () => {
    it('应该模拟安全的点击行为', async () => {
      const tabId = 1;
      
      await humanBehaviorSimulator.simulateSafeClick(tabId);
      
      expect(humanBehaviorSimulator.simulateSafeClick).toHaveBeenCalledWith(tabId);
    });

    it('应该避免点击危险元素', async () => {
      const dangerousElements = [
        { tagName: 'button', onclick: 'submitForm()' },
        { tagName: 'a', href: 'javascript:void(0)' },
        { tagName: 'input', type: 'submit' },
      ];

      // 模拟元素过滤逻辑
      const safeElements = dangerousElements.filter(element => {
        return !['button', 'a'].includes(element.tagName) && 
               element.tagName !== 'input';
      });

      expect(safeElements).toHaveLength(0);
    });

    it('应该在不同配置下产生不同行为', async () => {
      const profiles = ['stealth', 'normal', 'aggressive'];
      
      for (const profile of profiles) {
        const delay = humanBehaviorSimulator.generateRandomDelay(1000, profile);
        expect(typeof delay).toBe('number');
      }

      expect(humanBehaviorSimulator.generateRandomDelay).toHaveBeenCalledTimes(profiles.length);
    });
  });

  describe('行为分析', () => {
    it('应该计算行为序列的熵值', () => {
      const behaviorSequence = [
        { type: 'scroll', timestamp: 1000 },
        { type: 'pause', timestamp: 1200 },
        { type: 'scroll', timestamp: 1500 },
        { type: 'click', timestamp: 1800 },
      ];

      const entropy = behaviorAnalyzer.calculateSequenceEntropy(behaviorSequence);
      
      expect(entropy).toBe(0.8);
      expect(behaviorAnalyzer.calculateSequenceEntropy).toHaveBeenCalledWith(behaviorSequence);
    });

    it('应该分析点击位置分布', () => {
      const clickEvents = [
        { x: 100, y: 200 },
        { x: 300, y: 400 },
        { x: 150, y: 250 },
        { x: 450, y: 500 },
      ];

      const entropy = behaviorAnalyzer.calculateClickDistributionEntropy(clickEvents);
      
      expect(entropy).toBe(0.7);
      expect(behaviorAnalyzer.calculateClickDistributionEntropy).toHaveBeenCalledWith(clickEvents);
    });

    it('应该检测可疑的行为模式', () => {
      const suspiciousPattern = {
        sequenceEntropy: 0.1, // 低熵值
        clickDistribution: 0.05, // 高度聚集
        timingRegularity: 0.95, // 高度规律
      };

      const analysis = behaviorAnalyzer.analyzePattern(suspiciousPattern);
      
      // 在实际实现中，低熵值应该被标记为可疑
      expect(analysis).toHaveProperty('suspicious');
    });
  });

  describe('自适应行为调整', () => {
    it('应该根据分析结果调整行为策略', async () => {
      const suspiciousAnalysis = {
        sequenceEntropy: 0.2,
        clickDistribution: 0.1,
        suspicious: true,
      };

      await behaviorEngine.adjustBehaviorStrategy(suspiciousAnalysis);
      
      expect(behaviorEngine.adjustBehaviorStrategy).toHaveBeenCalledWith(suspiciousAnalysis);
    });

    it('应该增加行为的随机性', async () => {
      // 模拟调整前后的行为
      const beforeAdjustment = humanBehaviorSimulator.generateRandomDelay(1000, 0.1);
      
      // 调整策略
      await behaviorEngine.adjustBehaviorStrategy({ suspicious: true });
      
      const afterAdjustment = humanBehaviorSimulator.generateRandomDelay(1000, 0.5);
      
      // 调整后应该有更多随机性
      expect(typeof beforeAdjustment).toBe('number');
      expect(typeof afterAdjustment).toBe('number');
    });
  });

  describe('渲染模式适配', () => {
    it('应该在无头模式下工作', async () => {
      const headlessConfig = {
        mode: 'headless',
        iframeId: 'test-iframe',
      };

      await behaviorEngine.simulateFullPageScan(headlessConfig.iframeId);
      
      expect(behaviorEngine.simulateFullPageScan).toHaveBeenCalledWith(headlessConfig.iframeId);
    });

    it('应该在可视化模式下工作', async () => {
      const visualConfig = {
        mode: 'visual',
        tabId: 1,
        showProgress: true,
      };

      await behaviorEngine.simulateFullPageScan(visualConfig.tabId);
      
      expect(behaviorEngine.simulateFullPageScan).toHaveBeenCalledWith(visualConfig.tabId);
    });
  });

  describe('性能优化', () => {
    it('应该节流高频操作', async () => {
      const measure = new TestUtils.PerformanceMeasure();
      
      // 执行多次快速操作
      const operations = Array.from({ length: 20 }, () => 
        humanBehaviorSimulator.simulateScroll(1, { throttle: true })
      );

      await Promise.all(operations);
      
      measure.mark('throttled_operations_complete');
      expect(measure.getMeasurement('throttled_operations_complete')).toBeLessThan(TEST_TIMEOUTS.MEDIUM);
    });

    it('应该批处理DOM操作', async () => {
      const batchSize = 10;
      const operations: any[] = [];

      // 收集操作到批次中
      for (let i = 0; i < batchSize; i++) {
        operations.push({
          type: 'scroll',
          target: { x: 0, y: i * 100 },
        });
      }

      // 批量执行
      await Promise.all(operations.map(op => 
        humanBehaviorSimulator.simulateScroll(1, op.target)
      ));

      expect(humanBehaviorSimulator.simulateScroll).toHaveBeenCalledTimes(batchSize);
    });
  });

  describe('错误处理', () => {
    it('应该处理滚动失败', async () => {
      const error = new Error('Scroll failed');
      humanBehaviorSimulator.simulateScroll.mockRejectedValueOnce(error);

      try {
        await humanBehaviorSimulator.simulateScroll(1, { x: 0, y: 100 });
      } catch (e) {
        expect(e).toBe(error);
      }

      // 应该能重试
      humanBehaviorSimulator.simulateScroll.mockResolvedValueOnce(undefined);
      await humanBehaviorSimulator.simulateScroll(1, { x: 0, y: 100 });
      
      expect(humanBehaviorSimulator.simulateScroll).toHaveBeenCalledTimes(2);
    });

    it('应该处理点击失败', async () => {
      const error = new Error('Click failed');
      humanBehaviorSimulator.simulateClick.mockRejectedValueOnce(error);

      try {
        await humanBehaviorSimulator.simulateClick(1, { x: 100, y: 200 });
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('应该在无法找到安全元素时优雅处理', async () => {
      // 模拟页面没有安全可点击元素
      humanBehaviorSimulator.simulateSafeClick.mockResolvedValueOnce(undefined);

      await humanBehaviorSimulator.simulateSafeClick(1);
      
      expect(humanBehaviorSimulator.simulateSafeClick).toHaveBeenCalledWith(1);
    });
  });

  describe('配置和自定义', () => {
    it('应该支持自定义行为配置', () => {
      const customConfig = {
        scrollSpeed: 'slow',
        pauseTimeRange: [500, 1500],
        randomnessLevel: 0.8,
        distractionFrequency: 0.1,
      };

      const delay = humanBehaviorSimulator.generateRandomDelay(
        customConfig.pauseTimeRange[0], 
        customConfig.randomnessLevel
      );

      expect(typeof delay).toBe('number');
      expect(delay).toBeGreaterThan(0);
    });

    it('应该验证配置参数', () => {
      const invalidConfig = {
        scrollSpeed: 'invalid',
        pauseTimeRange: [-100, 0],
        randomnessLevel: 2.0, // 超出范围
      };

      // 在实际实现中，应该验证配置
      expect(() => {
        if (typeof invalidConfig.scrollSpeed !== 'string' || 
            invalidConfig.pauseTimeRange.some(n => n < 0) ||
            invalidConfig.randomnessLevel > 1.0) {
          throw new Error('Invalid configuration');
        }
      }).toThrow('Invalid configuration');
    });
  });

  describe('行为历史记录', () => {
    it('应该记录行为历史', () => {
      const behaviorHistory: any[] = [];
      
      const recordBehavior = (type: string, data: any) => {
        behaviorHistory.push({
          type,
          data,
          timestamp: Date.now(),
        });
      };

      recordBehavior('scroll', { x: 0, y: 100 });
      recordBehavior('click', { x: 200, y: 300 });
      recordBehavior('pause', { duration: 150 });

      expect(behaviorHistory).toHaveLength(3);
      expect(behaviorHistory[0].type).toBe('scroll');
      expect(behaviorHistory[1].type).toBe('click');
      expect(behaviorHistory[2].type).toBe('pause');
    });

    it('应该限制历史记录大小', () => {
      const maxHistorySize = 100;
      const behaviorHistory: any[] = [];
      
      const addToHistory = (item: any) => {
        behaviorHistory.push(item);
        if (behaviorHistory.length > maxHistorySize) {
          behaviorHistory.shift(); // 移除最旧的记录
        }
      };

      // 添加超过限制的记录
      for (let i = 0; i < 150; i++) {
        addToHistory({ type: 'test', index: i });
      }

      expect(behaviorHistory).toHaveLength(maxHistorySize);
      expect(behaviorHistory[0].index).toBe(50); // 前50个被移除了
    });
  });
});