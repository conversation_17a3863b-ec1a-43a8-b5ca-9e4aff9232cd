/**
 * 默认的提取策略：全页滚动截图。
 * 这是处理所有未知网站的保底方案。
 */
import { HumanBehaviorSimulator } from '@/core/behavior-engine/HumanBehaviorSimulator';
import { MESSAGE_TYPES } from '@/shared/constants';
import { Logger } from '@/shared/utils';
import { ExtractionResult, ExtractionStrategy } from './ExtractionStrategy';

export class DefaultScreenshotStrategy implements ExtractionStrategy {
  private readonly simulator: HumanBehaviorSimulator;

  constructor(private readonly logger: Logger) {
    // 默认策略通常与可视化反馈有关，因此可以考虑注入MaskController
    // 但为了简化，我们暂时不注入，因为MaskController在main.ts中管理
    this.simulator = new HumanBehaviorSimulator('normal');
  }

  async execute(window: Window, taskId: string, sessionId: string): Promise<ExtractionResult> {
    this.logger.info(`Executing DefaultScreenshotStrategy for session: ${sessionId}`);

    try {
      // 定义截图回调函数
      const captureAndProcessViewport = async (): Promise<void> => {
        try {
          const { innerWidth, innerHeight, pageYOffset } = window;
          const message = {
            type: MESSAGE_TYPES.CAPTURE_AND_PROCESS_VIEWPORT,
            payload: {
              taskId,
              sessionId,
              // 注意：这里我们不直接截图，而是请求后台服务截图
              // 但为了演示，我们假设内容脚本有此能力
              // 在真实实现中，这里可能是空的，截图由后台通过其它方式触发
              // 或者我们发送一个请求，让后台截图
              y: pageYOffset,
              height: innerHeight,
              width: innerWidth,
            },
          };
          this.logger.debug(`Requesting viewport capture for session ${sessionId}`, message.payload);
          // 在真实场景中，我们可能需要等待后台响应
          await chrome.runtime.sendMessage(message);
        } catch (e) {
          this.logger.error(`Failed to capture and process viewport for session ${sessionId}:`, e);
          // 单次截图失败不应中断整个扫描过程
        }
      };
      
      // 使用模拟器执行全页扫描，并传入截图回调
      await this.simulator.simulateFullPageScan(window, captureAndProcessViewport);

      this.logger.info(`DefaultScreenshotStrategy finished successfully for session: ${sessionId}`);
      // 对于截图策略，其成功与否由后台的图片拼接和分析结果决定。
      // 这里我们只表示扫描流程已完成。
      return { success: true, data: { status: 'scan_completed' } };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error executing DefaultScreenshotStrategy for session ${sessionId}:`, errorMessage);
      return { success: false, error: errorMessage };
    }
  }
} 