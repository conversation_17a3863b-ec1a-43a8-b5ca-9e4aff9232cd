/**
 * AuthService 测试
 */
import { AuthService } from '@/services/auth-service/AuthService';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/shared/utils', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  })),
}));

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    vi.clearAllMocks();
    authService = new AuthService();
  });

  describe('初始化', () => {
    it('应该成功初始化服务', () => {
      expect(authService).toBeInstanceOf(AuthService);
    });

    it('应该在初始状态下未认证', () => {
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getToken()).toBeNull();
    });
  });

  describe('登录功能', () => {
    it('应该成功登录并设置令牌', () => {
      const credentials = { username: 'test', password: 'password' };
      
      authService.login(credentials);
      
      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getToken()).toBe('mock-auth-token');
    });

    it('应该支持不同类型的凭据', () => {
      const stringCredentials = 'token-string';
      authService.login(stringCredentials);
      expect(authService.isAuthenticated()).toBe(true);

      authService.logout();

      const objectCredentials = { 
        email: '<EMAIL>', 
        token: 'oauth-token' 
      };
      authService.login(objectCredentials);
      expect(authService.isAuthenticated()).toBe(true);
    });

    it('应该处理空凭据', () => {
      authService.login(null);
      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getToken()).toBe('mock-auth-token');
    });

    it('应该处理未定义的凭据', () => {
      authService.login(undefined);
      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getToken()).toBe('mock-auth-token');
    });
  });

  describe('登出功能', () => {
    it('应该成功登出并清除令牌', () => {
      // 先登录
      authService.login({ username: 'test' });
      expect(authService.isAuthenticated()).toBe(true);
      
      // 然后登出
      authService.logout();
      
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getToken()).toBeNull();
    });

    it('应该在未登录状态下也能安全登出', () => {
      expect(authService.isAuthenticated()).toBe(false);
      
      authService.logout();
      
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getToken()).toBeNull();
    });

    it('应该支持多次登出', () => {
      authService.login({ username: 'test' });
      
      authService.logout();
      expect(authService.isAuthenticated()).toBe(false);
      
      authService.logout();
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('认证状态管理', () => {
    it('应该正确跟踪认证状态变化', () => {
      // 初始状态
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getToken()).toBeNull();
      
      // 登录后
      authService.login({ username: 'user' });
      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getToken()).toBe('mock-auth-token');
      
      // 登出后
      authService.logout();
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getToken()).toBeNull();
    });

    it('应该在多次登录时保持一致的令牌', () => {
      authService.login({ username: 'user1' });
      const token1 = authService.getToken();
      
      authService.login({ username: 'user2' });
      const token2 = authService.getToken();
      
      expect(token1).toBe(token2);
      expect(token1).toBe('mock-auth-token');
    });
  });

  describe('令牌管理', () => {
    it('应该返回有效的令牌格式', () => {
      authService.login({ username: 'test' });
      const token = authService.getToken();
      
      expect(typeof token).toBe('string');
      expect(token).toBe('mock-auth-token');
      expect((token as string).length).toBeGreaterThan(0);
    });

    it('应该在登出后清除令牌', () => {
      authService.login({ username: 'test' });
      expect(authService.getToken()).not.toBeNull();
      
      authService.logout();
      expect(authService.getToken()).toBeNull();
    });
  });

  describe('边界情况', () => {
    it('应该处理快速的登录登出操作', () => {
      for (let i = 0; i < 10; i++) {
        authService.login({ iteration: i });
        expect(authService.isAuthenticated()).toBe(true);
        
        authService.logout();
        expect(authService.isAuthenticated()).toBe(false);
      }
    });

    it('应该处理复杂的凭据对象', () => {
      const complexCredentials = {
        username: '<EMAIL>',
        password: 'complex-password-123!@#',
        metadata: {
          device: 'browser-extension',
          version: '1.0.0',
          features: ['auth', 'capture', 'analysis']
        },
        timestamp: Date.now()
      };
      
      authService.login(complexCredentials);
      expect(authService.isAuthenticated()).toBe(true);
    });

    it('应该处理非标准类型的凭据', () => {
      // 数组类型凭据
      authService.login(['username', 'password']);
      expect(authService.isAuthenticated()).toBe(true);
      
      authService.logout();
      
      // 数字类型凭据
      authService.login(12345);
      expect(authService.isAuthenticated()).toBe(true);
      
      authService.logout();
      
      // 布尔类型凭据
      authService.login(true);
      expect(authService.isAuthenticated()).toBe(true);
    });
  });

  describe('服务生命周期', () => {
    it('应该支持服务的重新初始化', () => {
      authService.login({ username: 'test' });
      expect(authService.isAuthenticated()).toBe(true);
      
      // 创建新实例模拟重新初始化
      const newAuthService = new AuthService();
      expect(newAuthService.isAuthenticated()).toBe(false);
      expect(newAuthService.getToken()).toBeNull();
    });

    it('应该维护独立的服务实例状态', () => {
      const authService1 = new AuthService();
      const authService2 = new AuthService();
      
      authService1.login({ user: '1' });
      expect(authService1.isAuthenticated()).toBe(true);
      expect(authService2.isAuthenticated()).toBe(false);
      
      authService2.login({ user: '2' });
      expect(authService1.isAuthenticated()).toBe(true);
      expect(authService2.isAuthenticated()).toBe(true);
      
      authService1.logout();
      expect(authService1.isAuthenticated()).toBe(false);
      expect(authService2.isAuthenticated()).toBe(true);
    });
  });
});