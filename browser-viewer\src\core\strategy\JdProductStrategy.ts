import { createLogger } from '@/shared/utils';
import { HumanBehaviorSimulator } from '../../core/behavior-engine/HumanBehaviorSimulator';
import type { ExtractionResult, ExtractionStrategy } from './ExtractionStrategy';

/**
 * 针对京东（JD.com）商品详情页的精准提取策略。
 */
export class JdProductStrategy implements ExtractionStrategy {
  private readonly _logger = createLogger('JdProductStrategy');
  private readonly _simulator: HumanBehaviorSimulator;

  constructor() {
    this._simulator = new HumanBehaviorSimulator('normal');
  }

  async execute(context: Window, _taskId: string, _sessionId: string): Promise<ExtractionResult> {
    this._logger.info('Executing JD Product extraction strategy...');
    const { document } = context;

    try {
      // 1. 等待关键元素加载
      await this._waitForElement(document, '.itemInfo-wrap .price');
      this._logger.info('Key element (price) found.');

      // 2. 快速滚动到底部以触发懒加载
      await this._simulator.simulateScroll(context, {
        startY: 0,
        endY: document.body.scrollHeight,
        duration: 1500, // 较快的滚动
        startX: 0,
        endX: 0
      });
      this._logger.info('Fast scroll to bottom completed.');

      // 3. 提取结构化数据
      const title = (document.querySelector('.itemInfo-wrap .sku-name') as HTMLElement)?.innerText || '';
      const price = (document.querySelector('.itemInfo-wrap .price') as HTMLElement)?.innerText || '';
      
      // 4. 提取商品详情图片链接
      const imageUrls: string[] = [];
      const imageElements = document.querySelectorAll('#J-detail-content img');
      imageElements.forEach(img => {
        const src = (img as HTMLImageElement).src;
        if (src) {
          imageUrls.push(src);
        }
      });
      this._logger.info(`Found ${imageUrls.length} detail image URLs.`);

      const extractedData = {
        title,
        price,
        imageUrls,
        url: context.location.href,
      };
      
      // TODO: 将提取的数据通过新消息类型发送回 Service Worker
      this._logger.info('Structured data extraction successful:', extractedData);

      return { success: true, data: extractedData };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error in JdProductStrategy';
      this._logger.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  }
  
  /**
   * 等待指定的DOM元素出现。
   */
  private _waitForElement(doc: Document, selector: string, timeout = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      const interval = setInterval(() => {
        if (doc.querySelector(selector)) {
          clearInterval(interval);
          clearTimeout(timer);
          resolve();
        }
      }, 100);

      const timer = setTimeout(() => {
        clearInterval(interval);
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }
} 