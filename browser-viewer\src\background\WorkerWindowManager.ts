import { TabId } from '@/shared/types';
import { createLogger, Logger } from '@/shared/utils';

/**
 * 管理一个专用的、折叠的标签组来运行所有任务，以实现非干扰的并发处理。
 * 所有任务标签页都在此组内以后台方式创建，并通过后台音频机制保持"激活"状态。
 * 这是一个单例类。
 */
export class WorkerWindowManager {
  private static _instance: WorkerWindowManager;
  private readonly _logger: Logger = createLogger('[WorkerWindowManager]');
  private readonly _managedTabIds = new Set<TabId>();
  private _workerGroupId: number | null = null;
  private _isInitialized = false;

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): WorkerWindowManager {
    if (!WorkerWindowManager._instance) {
      WorkerWindowManager._instance = new WorkerWindowManager();
    }
    return WorkerWindowManager._instance;
  }

  public initialize(): void {
    if (this._isInitialized) {
      return;
    }
    // 监听标签页移除事件，以便在最后一个任务标签页关闭时清理标签组
    chrome.tabs.onRemoved.addListener((tabId) => {
      if (this._managedTabIds.has(tabId)) {
        this._logger.info(`受控标签页（ID: ${tabId}）已被移除。`);
        this._managedTabIds.delete(tabId);
        if (this._managedTabIds.size === 0) {
          this._logger.info('所有受控标签页均已关闭，清理标签组。');
          void this.cleanupGroup();
        }
      }
    });

    this._isInitialized = true;
    this._logger.info('标签组管理的工作窗口管理器已连接。');
  }

  private async _getWorkerGroupId(): Promise<number | null> {
    if (this._workerGroupId) {
      try {
        await chrome.tabGroups.get(this._workerGroupId);
        return this._workerGroupId;
      } catch {
        this._logger.warn(`标签组 ${this._workerGroupId} 未找到，将重置。`);
        this._workerGroupId = null;
      }
    }
    return null;
  }

  public async createTab(options: chrome.tabs.CreateProperties): Promise<chrome.tabs.Tab> {
    const tab = await chrome.tabs.create({
      ...options,
      active: false, // 确保新标签页在后台打开，不打扰用户
    });

    if (!tab.id) {
      throw new Error('Failed to create tab.');
    }

    let groupId = await this._getWorkerGroupId();

    if (groupId) {
      // 组已存在，将标签页加入其中
      await chrome.tabs.group({ tabIds: [tab.id], groupId });
    } else {
      // 组不存在，用此标签页创建一个新组
      groupId = await chrome.tabs.group({ tabIds: [tab.id] });
      await chrome.tabGroups.update(groupId, {
        title: '🦥',
        color: 'green',
        collapsed: true,
      });
      this._workerGroupId = groupId;
      this._logger.info(`已创建新的工作标签组，ID: ${this._workerGroupId}`);
    }

    // 将标签页静音，并设置为不可丢弃，以防止浏览器在后台自动休眠它
    await chrome.tabs.update(tab.id, { autoDiscardable: false, muted: true });
    this._managedTabIds.add(tab.id);
    this._logger.info(`已在标签组 ${groupId} 中创建并管理新标签页 ${tab.id}。`);

    return tab;
  }

  public async removeTab(tabId: TabId): Promise<void> {
    if (!this._managedTabIds.has(tabId)) {
      this._logger.warn(`试图移除一个非本管理器创建的标签页 ${tabId}。`);
      return;
    }

    try {
      await chrome.tabs.remove(tabId);
      // onRemoved 监听器会自动处理后续清理
    } catch (error) {
      this._logger.error(`移除标签页 ${tabId} 失败:`, error);
      // 即使移除失败，也清理状态
      this._managedTabIds.delete(tabId);
    }
  }

  public async cleanupGroup(): Promise<void> {
    if (!this._workerGroupId) return;
    try {
      const tabs = await chrome.tabs.query({ groupId: this._workerGroupId });
      const tabIds = tabs.map(t => t.id).filter((id): id is number => !!id);
      if (tabIds.length > 0) {
        await chrome.tabs.ungroup(tabIds);
      }
    } catch (error) {
      this._logger.warn(`解散标签组 ${this._workerGroupId} 失败，可能已被解散。`, error);
    } finally {
      this._workerGroupId = null;
      this._managedTabIds.clear();
    }
  }
} 