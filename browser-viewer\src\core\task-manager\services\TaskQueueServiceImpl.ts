/**
 * 任务队列服务实现
 * 
 * 职责：
 * - 管理任务队列的入队和出队操作
 * - 实现智能的任务分配算法
 * - 处理任务优先级和批处理操作
 */

import { TaskConfig } from '@/shared/types';
import { createLogger } from '@/shared/utils';
import { SlotState, TaskQueueService } from '../interfaces';

export class TaskQueueServiceImpl implements TaskQueueService {
  private readonly _logger = createLogger('TaskQueueService');
  private readonly _queue: TaskConfig[] = [];

  enqueue(configs: TaskConfig[]): void {
    if (!configs || configs.length === 0) {
      return;
    }

    let addedCount = 0;
    
    for (const config of configs) {
      // 为每个传入的 config 生成一个唯一的 batchId（如果没有的话）
      const batchId = config.batchId ?? crypto.randomUUID();

      if (config.urls && config.urls.length > 0) {
        for (const url of config.urls) {
          // 为每个URL创建一个独立的任务配置
          const singleUrlConfig: TaskConfig = {
            ...config,
            urls: [url],
            batchId: batchId,
          };
          
          this._queue.push(singleUrlConfig);
          addedCount++;
        }
      }
    }

    this._logger.info(`向队列添加了 ${addedCount} 个任务，当前队列长度: ${this._queue.length}`);
  }

  dequeue(slot: SlotState): TaskConfig | null {
    if (this._queue.length === 0) {
      return null;
    }

    // 1. 尝试同源亲和性匹配
    if (slot.lastDomain) {
      const affinityIndex = this._queue.findIndex(config => {
        try {
          return new URL(config.urls[0]).hostname === slot.lastDomain;
        } catch {
          return false; // 无效URL，不匹配
        }
      });
      
      if (affinityIndex !== -1) {
        this._logger.info(`槽位 ${slot.id} 找到同域名任务 (域名: ${slot.lastDomain})`);
        const [taskConfig] = this._queue.splice(affinityIndex, 1);
        return taskConfig;
      }
    }
    
    // 2. 如果没有亲和性匹配，寻找优先级最高的任务
    let highestPriorityIndex = 0;
    let highestPriority = this._queue[0].priority ?? 10; // 默认优先级为10
    
    for (let i = 1; i < this._queue.length; i++) {
      const currentPriority = this._queue[i].priority ?? 10;
      if (currentPriority < highestPriority) { // 数字越小优先级越高
        highestPriority = currentPriority;
        highestPriorityIndex = i;
      }
    }
    
    this._logger.info(`槽位 ${slot.id} 选择优先级最高的任务 (优先级: ${highestPriority})`);
    const [taskConfig] = this._queue.splice(highestPriorityIndex, 1);
    return taskConfig;
  }

  getQueueLength(): number {
    return this._queue.length;
  }

  clearQueue(): void {
    const clearedCount = this._queue.length;
    this._queue.length = 0;
    
    if (clearedCount > 0) {
      this._logger.info(`队列已清空，移除了 ${clearedCount} 个任务`);
    }
  }

  prioritizeBatches(batchIds: string[]): void {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('prioritizeBatches 调用时没有提供 batchIds');
      return;
    }

    const prioritizedTasks: TaskConfig[] = [];
    const otherTasks: TaskConfig[] = [];

    // 分离指定批处理的任务和其他任务
    for (const config of this._queue) {
      if (config.batchId && batchIds.includes(config.batchId)) {
        prioritizedTasks.push(config);
      } else {
        otherTasks.push(config);
      }
    }

    // 重新排列队列：优先任务在前
    this._queue.length = 0;
    this._queue.push(...prioritizedTasks, ...otherTasks);
    
    this._logger.info(`已将 ${prioritizedTasks.length} 个任务优先排队 (批处理: ${batchIds.join(', ')})`);
  }

  removeBatchTasks(batchIds: string[]): TaskConfig[] {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('removeBatchTasks 调用时没有提供 batchIds');
      return [];
    }

    const removedTasks: TaskConfig[] = [];
    const remainingTasks: TaskConfig[] = [];

    for (const config of this._queue) {
      if (config.batchId && batchIds.includes(config.batchId)) {
        removedTasks.push(config);
      } else {
        remainingTasks.push(config);
      }
    }

    // 更新队列
    this._queue.length = 0;
    this._queue.push(...remainingTasks);
    
    this._logger.info(`从队列中移除了 ${removedTasks.length} 个任务 (批处理: ${batchIds.join(', ')})`);
    
    return removedTasks;
  }

  getAllQueuedTasks(): TaskConfig[] {
    return [...this._queue]; // 返回副本以防止外部修改
  }

  /**
   * 向队列前端添加任务（用于重试等高优先级场景）
   */
  prependTasks(configs: TaskConfig[]): void {
    if (!configs || configs.length === 0) {
      return;
    }

    let addedCount = 0;
    const tasksToAdd: TaskConfig[] = [];
    
    for (const config of configs) {
      if (config.urls && config.urls.length > 0) {
        for (const url of config.urls) {
          const singleUrlConfig: TaskConfig = {
            ...config,
            urls: [url],
          };
          
          tasksToAdd.push(singleUrlConfig);
          addedCount++;
        }
      }
    }

    // 添加到队列前端
    this._queue.unshift(...tasksToAdd);
    
    this._logger.info(`向队列前端添加了 ${addedCount} 个高优先级任务`);
  }

  /**
   * 获取队列中指定批处理的任务
   */
  getTasksByBatchIds(batchIds: string[]): TaskConfig[] {
    return this._queue.filter(config => 
      config.batchId && batchIds.includes(config.batchId)
    );
  }

  /**
   * 检查队列中是否有指定批处理的任务
   */
  hasBatchTasks(batchId: string): boolean {
    return this._queue.some(config => config.batchId === batchId);
  }

  /**
   * 获取队列中所有批处理的统计信息
   */
  getBatchStatistics(): { [batchId: string]: number } {
    const stats: { [batchId: string]: number } = {};
    
    for (const config of this._queue) {
      if (config.batchId) {
        stats[config.batchId] = (stats[config.batchId] || 0) + 1;
      }
    }
    
    return stats;
  }

  /**
   * 获取队列中任务的优先级分布
   */
  getPriorityDistribution(): { [priority: number]: number } {
    const distribution: { [priority: number]: number } = {};
    
    for (const config of this._queue) {
      const priority = config.priority ?? 10;
      distribution[priority] = (distribution[priority] || 0) + 1;
    }
    
    return distribution;
  }

  /**
   * 重新排序队列中的任务
   * @param compareFn 比较函数
   */
  reorderQueue(compareFn: (a: TaskConfig, b: TaskConfig) => number): void {
    this._queue.sort(compareFn);
    this._logger.info('队列已重新排序');
  }

  /**
   * 根据条件筛选并移除任务
   */
  removeTasksWhere(predicate: (config: TaskConfig) => boolean): TaskConfig[] {
    const removedTasks: TaskConfig[] = [];
    const remainingTasks: TaskConfig[] = [];

    for (const config of this._queue) {
      if (predicate(config)) {
        removedTasks.push(config);
      } else {
        remainingTasks.push(config);
      }
    }

    this._queue.length = 0;
    this._queue.push(...remainingTasks);
    
    if (removedTasks.length > 0) {
      this._logger.info(`根据条件移除了 ${removedTasks.length} 个任务`);
    }
    
    return removedTasks;
  }
}