/**
 * NotificationService 单元测试
 * 测试通知服务的核心功能
 */

import type { NotificationOptions } from '@/services/notification-service/NotificationService';
import { NotificationService } from '@/services/notification-service/NotificationService';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TestUtils } from '../../../utils/test-helpers';

describe('NotificationService', () => {
  let notificationService: NotificationService;
  let mockChrome: any;

  beforeEach(() => {
    // Mock Chrome APIs
    mockChrome = {
      notifications: {
        create: vi.fn().mockResolvedValue('notification-id'),
        clear: vi.fn().mockResolvedValue(true),
        getAll: vi.fn().mockResolvedValue({}),
        update: vi.fn().mockResolvedValue(true),
        onClicked: {
          addListener: vi.fn(),
          removeListener: vi.fn()
        },
        onClosed: {
          addListener: vi.fn(),
          removeListener: vi.fn()
        }
      },
      runtime: {
        getURL: vi.fn().mockReturnValue('chrome-extension://test/assets/icons/icon128.png')
      }
    };

    vi.stubGlobal('chrome', mockChrome);
    
    notificationService = new NotificationService();
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('初始化', () => {
    it('应该成功初始化服务', () => {
      expect(notificationService).toBeDefined();
      expect(mockChrome.runtime.getURL).toHaveBeenCalledWith('assets/icons/icon128.png');
    });

    it('应该设置默认图标URL', () => {
      // 通过创建通知来间接测试默认图标
      const options: NotificationOptions = {
        title: '测试通知',
        message: '这是一个测试消息'
      };

      notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          iconUrl: 'chrome-extension://test/assets/icons/icon128.png'
        })
      );
    });
  });

  describe('通知显示', () => {
    it('应该成功显示基本通知', async () => {
      const options: NotificationOptions = {
        title: '测试标题',
        message: '测试消息'
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.stringMatching(/^agent-notification-\d+-\d+$/),
        {
          type: 'basic',
          iconUrl: 'chrome-extension://test/assets/icons/icon128.png',
          title: '测试标题',
          message: '测试消息'
        }
      );
    });

    it('应该支持自定义通知ID', async () => {
      const options: NotificationOptions = {
        title: '自定义ID通知',
        message: '这是带有自定义ID的通知'
      };
      const customId = 'custom-notification-id';

      await notificationService.show(options, customId);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        customId,
        expect.objectContaining({
          title: '自定义ID通知',
          message: '这是带有自定义ID的通知'
        })
      );
    });

    it('应该支持自定义图标', async () => {
      const customIcon = 'chrome-extension://test/custom-icon.png';
      const options: NotificationOptions = {
        title: '自定义图标通知',
        message: '这是带有自定义图标的通知',
        iconUrl: customIcon
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          iconUrl: customIcon
        })
      );
    });

    it('应该生成唯一的通知ID', async () => {
      const options: NotificationOptions = {
        title: '测试通知',
        message: '测试消息'
      };

      await notificationService.show(options);
      // 确保时间差异
      await new Promise(resolve => setTimeout(resolve, 1));
      await notificationService.show(options);

      const calls = mockChrome.notifications.create.mock.calls;
      expect(calls).toHaveLength(2);
      expect(calls[0][0]).not.toBe(calls[1][0]);
    });
  });

  describe('错误处理', () => {
    it('应该处理通知创建失败', async () => {
      const error = new Error('Notification creation failed');
      mockChrome.notifications.create.mockRejectedValue(error);

      // Mock logger.error to prevent stderr output
      const mockError = vi.fn();
      vi.spyOn(notificationService['_logger'], 'error').mockImplementation(mockError);

      const options: NotificationOptions = {
        title: '失败通知',
        message: '这个通知应该失败'
      };

      // 应该不抛出错误，而是在内部处理
      await expect(notificationService.show(options)).resolves.not.toThrow();
      
      // 验证错误被记录
      expect(mockError).toHaveBeenCalledWith('显示通知时出错:', error);
    });

    it('应该处理Chrome API不可用', async () => {
      vi.stubGlobal('chrome', undefined);

      const options: NotificationOptions = {
        title: 'API不可用',
        message: '这个通知在API不可用时发送'
      };

      // 创建新的服务实例来测试API不可用的情况
      expect(() => new NotificationService()).toThrow();
    });

    it('应该处理权限不足', async () => {
      const permissionError = new Error('Permission denied');
      mockChrome.notifications.create.mockRejectedValue(permissionError);

      // Mock logger.error to prevent stderr output
      const mockError = vi.fn();
      vi.spyOn(notificationService['_logger'], 'error').mockImplementation(mockError);

      const options: NotificationOptions = {
        title: '权限测试',
        message: '测试权限不足的情况'
      };

      await expect(notificationService.show(options)).resolves.not.toThrow();
      
      // 验证错误被记录
      expect(mockError).toHaveBeenCalledWith('显示通知时出错:', permissionError);
    });
  });

  describe('通知选项验证', () => {
    it('应该处理空标题', async () => {
      const options: NotificationOptions = {
        title: '',
        message: '空标题测试'
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          title: '',
          message: '空标题测试'
        })
      );
    });

    it('应该处理空消息', async () => {
      const options: NotificationOptions = {
        title: '空消息测试',
        message: ''
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          title: '空消息测试',
          message: ''
        })
      );
    });

    it('应该处理长文本', async () => {
      const longTitle = 'A'.repeat(500);
      const longMessage = 'B'.repeat(1000);
      
      const options: NotificationOptions = {
        title: longTitle,
        message: longMessage
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          title: longTitle,
          message: longMessage
        })
      );
    });

    it('应该处理特殊字符', async () => {
      const options: NotificationOptions = {
        title: '特殊字符 🚀 测试 ✨',
        message: '消息包含换行符\n和制表符\t以及其他特殊字符 @#$%^&*()'
      };

      await notificationService.show(options);

      expect(mockChrome.notifications.create).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          title: '特殊字符 🚀 测试 ✨',
          message: '消息包含换行符\n和制表符\t以及其他特殊字符 @#$%^&*()'
        })
      );
    });
  });

  describe('并发处理', () => {
    it('应该支持同时显示多个通知', async () => {
      const notifications = Array.from({ length: 5 }, (_, i) => ({
        title: `通知 ${i + 1}`,
        message: `这是第 ${i + 1} 个通知`
      }));

      const promises = notifications.map(options => 
        notificationService.show(options)
      );

      await Promise.all(promises);

      expect(mockChrome.notifications.create).toHaveBeenCalledTimes(5);
    });

    it('应该为每个并发通知生成唯一ID', async () => {
      const notifications = Array.from({ length: 10 }, (_, i) => ({
        title: `并发通知 ${i}`,
        message: `并发消息 ${i}`
      }));

      // 串行创建而不是并行，以确保时间戳不同
      for (const options of notifications) {
        await notificationService.show(options);
        await new Promise(resolve => setTimeout(resolve, 1)); // 确保时间差异
      }

      const calls = mockChrome.notifications.create.mock.calls;
      const ids = calls.map((call: any[]) => call[0]);
      const uniqueIds = new Set(ids);

      expect(uniqueIds.size).toBe(ids.length);
    });
  });

  describe('性能测试', () => {
    it('应该快速创建通知', async () => {
      const options: NotificationOptions = {
        title: '性能测试',
        message: '测试通知创建性能'
      };

      const measure = new TestUtils.PerformanceMeasure();
      
      await notificationService.show(options);
      
      measure.mark('notification_complete');
      expect(measure.getMeasurement('notification_complete')).toBeLessThan(50);
    });

    it('应该处理大量通知创建', async () => {
      const notifications = Array.from({ length: 100 }, (_, i) => ({
        title: `批量通知 ${i}`,
        message: `批量消息 ${i}`
      }));

      const measure = new TestUtils.PerformanceMeasure();
      
      await Promise.all(
        notifications.map(options => notificationService.show(options))
      );
      
      measure.mark('batch_notifications_complete');
      expect(measure.getMeasurement('batch_notifications_complete')).toBeLessThan(1000);
    });
  });

  describe('ID生成', () => {
    it('应该生成基于时间戳的ID', async () => {
      const options: NotificationOptions = {
        title: 'ID测试',
        message: 'ID生成测试'
      };

      const startTime = Date.now();
      await notificationService.show(options);
      const endTime = Date.now();

      const call = mockChrome.notifications.create.mock.calls[0];
      const id = call[0];
      
      expect(id).toMatch(/^agent-notification-\d+-\d+$/);
      
      const timestampPart = id.replace('agent-notification-', '').split('-')[0];
      const timestamp = parseInt(timestampPart);
      expect(timestamp).toBeGreaterThanOrEqual(startTime);
      expect(timestamp).toBeLessThanOrEqual(endTime);
    });

    it('应该接受自定义ID格式', async () => {
      const customIds = [
        'my-notification',
        'notification_123',
        'task-completion-alert',
        'error-notification-2023',
        '通知-中文-ID'
      ];

      for (const customId of customIds) {
        const options: NotificationOptions = {
          title: '自定义ID测试',
          message: `测试ID: ${customId}`
        };

        await notificationService.show(options, customId);
      }

      const calls = mockChrome.notifications.create.mock.calls;
      customIds.forEach((id, index) => {
        expect(calls[index][0]).toBe(id);
      });
    });
  });

  describe('资源清理', () => {
    it('应该正确处理服务生命周期', () => {
      // 创建服务实例
      const service = new NotificationService();
      
      // 验证服务已初始化
      expect(service).toBeDefined();
      
      // 注意：由于没有显式的清理方法，这里主要测试构造函数
      expect(mockChrome.runtime.getURL).toHaveBeenCalled();
    });
  });
});