# Browser-Use 使用指南

## 在本项目中集成 Browser-Use

### 1. 环境准备

```bash
# 安装 browser-use
pip install browser-use

# 安装浏览器
playwright install chromium --with-deps --no-shell

# 安装记忆功能（可选，需要 Python < 3.13）
pip install "browser-use[memory]"

# 安装 CLI 工具（可选）
pip install "browser-use[cli]"
```

### 2. 环境变量配置

在项目根目录创建 `.env` 文件：

```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key

# Google Gemini
GOOGLE_API_KEY=your_google_api_key

# DeepSeek
DEEPSEEK_API_KEY=your_deepseek_api_key

# 其他模型
AZURE_OPENAI_ENDPOINT=your_azure_endpoint
AZURE_OPENAI_KEY=your_azure_key
GROK_API_KEY=your_grok_api_key
NOVITA_API_KEY=your_novita_api_key
```

### 3. 基础使用模式

#### 模式一：简单任务执行

```python
import asyncio
from dotenv import load_dotenv
from browser_use import Agent
from langchain_openai import ChatOpenAI

load_dotenv()

async def simple_task():
    agent = Agent(
        task="访问京东官网并搜索'小米手机'，获取前5个商品的价格信息",
        llm=ChatOpenAI(model="gpt-4o"),
    )
    result = await agent.run()
    return result

# 运行任务
asyncio.run(simple_task())
```

#### 模式二：持久化会话

```python
from browser_use import Agent, BrowserSession, BrowserProfile

async def persistent_session():
    # 创建持久化浏览器会话
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            user_data_dir='./browser_profiles/jingdong',  # 保存 Cookie 等
            trace_path='./traces/',  # 保存操作轨迹
        )
    )
    
    try:
        agent = Agent(
            task="登录京东账户并查看购物车",
            llm=ChatOpenAI(model="gpt-4o"),
            browser_session=browser_session,
        )
        await agent.run()
    finally:
        await browser_session.close()

asyncio.run(persistent_session())
```

#### 模式三：结构化数据采集

```python
from pydantic import BaseModel
from browser_use import Agent, Controller

class Product(BaseModel):
    name: str
    price: str
    rating: float
    image_url: str
    product_url: str

class ProductList(BaseModel):
    products: list[Product]
    total_count: int

async def structured_scraping():
    controller = Controller(output_model=ProductList)
    
    agent = Agent(
        task="访问京东搜索'华为手机'，获取前10个商品的详细信息",
        llm=ChatOpenAI(model="gpt-4o"),
        controller=controller
    )
    
    history = await agent.run()
    result = history.final_result()
    
    if result:
        products = ProductList.model_validate_json(result)
        return products
    return None

# 使用结果
products = asyncio.run(structured_scraping())
if products:
    for product in products.products:
        print(f"商品：{product.name}")
        print(f"价格：{product.price}")
        print(f"评分：{product.rating}")
        print("---")
```

### 4. 高级配置

#### 自定义动作函数

```python
from browser_use import Agent, Controller
from browser_use.controller.registry.service import Registry

# 创建自定义控制器
controller = Controller()

# 注册自定义动作
@controller.registry.action("等待指定时间（秒）")
async def wait_seconds(seconds: int):
    await asyncio.sleep(seconds)
    return f"等待了 {seconds} 秒"

# 注册自定义信息采集动作
@controller.registry.action("提取页面所有图片URL")
async def extract_images(browser_session):
    page = await browser_session.get_current_page()
    images = await page.query_selector_all('img')
    urls = []
    for img in images:
        src = await img.get_attribute('src')
        if src:
            urls.append(src)
    return {"image_urls": urls, "count": len(urls)}

agent = Agent(
    task="访问商品页面，等待3秒加载，然后提取所有图片URL",
    llm=ChatOpenAI(model="gpt-4o"),
    controller=controller
)
```

#### 浏览器配置优化

```python
from browser_use import BrowserSession, BrowserProfile

# 配置隐身模式和代理
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        headless=False,  # 显示浏览器窗口，便于调试
        user_data_dir='./profiles/stealth',
        extra_launch_args=[
            '--no-sandbox',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    )
)
```

### 5. 信息采集最佳实践

#### 5.1 电商网站采集

```python
async def ecommerce_scraping():
    """电商网站信息采集示例"""
    
    # 配置专用浏览器配置文件
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            user_data_dir='./profiles/ecommerce',
            trace_path='./traces/ecommerce/',
            # 设置合理的延迟，避免被反爬
            extra_launch_args=['--disable-blink-features=AutomationControlled']
        )
    )
    
    agent = Agent(
        task="""
        1. 访问京东商城
        2. 搜索'笔记本电脑'
        3. 按销量排序
        4. 采集前20个商品的以下信息：
           - 商品名称
           - 价格
           - 评价数量
           - 店铺名称
           - 商品链接
        5. 保存为结构化数据
        """,
        llm=ChatOpenAI(model="gpt-4o"),
        browser_session=browser_session,
    )
    
    return await agent.run()
```

#### 5.2 新闻网站采集

```python
async def news_scraping():
    """新闻网站信息采集示例"""
    
    class NewsArticle(BaseModel):
        title: str
        summary: str
        author: str
        publish_time: str
        url: str
        category: str
    
    class NewsList(BaseModel):
        articles: list[NewsArticle]
        source: str
        crawl_time: str
    
    controller = Controller(output_model=NewsList)
    
    agent = Agent(
        task="""
        访问新浪新闻首页，采集今日头条新闻：
        1. 获取头条新闻标题和摘要
        2. 记录作者和发布时间
        3. 获取新闻链接和分类
        4. 按重要性排序
        """,
        llm=ChatOpenAI(model="gpt-4o"),
        controller=controller
    )
    
    return await agent.run()
```

### 6. 错误处理和重试

```python
import logging
from browser_use.exceptions import BrowserError

async def robust_scraping():
    """带错误处理的采集示例"""
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            browser_session = BrowserSession()
            
            agent = Agent(
                task="采集指定网站信息",
                llm=ChatOpenAI(model="gpt-4o"),
                browser_session=browser_session,
            )
            
            result = await agent.run()
            await browser_session.close()
            return result
            
        except BrowserError as e:
            retry_count += 1
            logging.warning(f"浏览器错误，第{retry_count}次重试: {e}")
            if retry_count >= max_retries:
                raise
                
        except Exception as e:
            logging.error(f"未知错误: {e}")
            raise
        
        finally:
            # 确保浏览器会话关闭
            if 'browser_session' in locals():
                await browser_session.close()
```

### 7. 性能优化建议

#### 7.1 资源管理

```python
# 使用上下文管理器确保资源释放
async def optimized_scraping():
    async with BrowserSession() as browser_session:
        agent = Agent(
            task="执行采集任务",
            llm=ChatOpenAI(model="gpt-4o"),
            browser_session=browser_session,
        )
        return await agent.run()
    # 浏览器会话自动关闭
```

#### 7.2 并发处理

```python
async def parallel_scraping():
    """并发采集多个网站"""
    
    tasks = [
        "采集网站A的信息",
        "采集网站B的信息", 
        "采集网站C的信息"
    ]
    
    async def single_task(task):
        async with BrowserSession() as browser_session:
            agent = Agent(
                task=task,
                llm=ChatOpenAI(model="gpt-4o"),
                browser_session=browser_session,
            )
            return await agent.run()
    
    # 并发执行
    results = await asyncio.gather(*[single_task(task) for task in tasks])
    return results
```

### 8. 调试和监控

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

async def debug_scraping():
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,  # 显示浏览器窗口
            trace_path='./debug_traces/',  # 保存操作轨迹
        )
    )
    
    agent = Agent(
        task="调试采集任务",
        llm=ChatOpenAI(model="gpt-4o"),
        browser_session=browser_session,
    )
    
    # 获取详细历史记录
    history = await agent.run()
    
    # 打印每一步操作
    for step in history.history:
        print(f"步骤: {step.action}")
        print(f"结果: {step.result}")
        print("---")
    
    return history
```

### 9. 与现有项目集成

在 `agent-crawl` 项目中集成 browser-use：

```python
# crawler/browser_use_crawler.py
from browser_use import Agent, BrowserSession, BrowserProfile
from langchain_openai import ChatOpenAI
import asyncio

class BrowserUseCrawler:
    def __init__(self, model_name="gpt-4o"):
        self.llm = ChatOpenAI(model=model_name)
        
    async def crawl_jingdong(self, product_name: str):
        """使用 browser-use 采集京东商品信息"""
        
        browser_session = BrowserSession(
            browser_profile=BrowserProfile(
                user_data_dir='./browser_profiles/jingdong',
            )
        )
        
        task = f"""
        访问京东官网，搜索'{product_name}'商品：
        1. 打开 jd.com
        2. 在搜索框输入'{product_name}'
        3. 点击搜索
        4. 获取前10个商品的详细信息
        5. 返回结构化数据
        """
        
        agent = Agent(
            task=task,
            llm=self.llm,
            browser_session=browser_session,
        )
        
        try:
            result = await agent.run()
            return result
        finally:
            await browser_session.close()

# 使用示例
async def main():
    crawler = BrowserUseCrawler()
    result = await crawler.crawl_jingdong("小米手机")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
```

这个使用指南提供了在项目中集成和使用 browser-use 的完整方案，可以根据具体需求进行调整和扩展。
