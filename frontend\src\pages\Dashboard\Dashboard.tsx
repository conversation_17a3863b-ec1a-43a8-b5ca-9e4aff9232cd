/**
 * 仪表板页面
 * 显示系统概览和关键指标
 */

import React, { useEffect } from 'react'
import { Row, Col, Card, Statistic, Progress, Table, Tag, Typography, Space, Button } from 'antd'
import {
  UserOutlined,
  ProjectOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import { useTaskStore, TaskStatus } from '../../stores/taskStore'
import { useAuthStore } from '../../stores/authStore'

const { Title, Text } = Typography

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const { tasks, fetchTasks, loading } = useTaskStore()

  useEffect(() => {
    fetchTasks({ page: 1, size: 10 })
  }, [fetchTasks])

  // 计算统计数据
  const stats = {
    totalTasks: tasks.length,
    runningTasks: tasks.filter(task => task.status === TaskStatus.RUNNING).length,
    completedTasks: tasks.filter(task => task.status === TaskStatus.COMPLETED).length,
    failedTasks: tasks.filter(task => task.status === TaskStatus.FAILED).length,
  }

  const successRate = stats.totalTasks > 0 
    ? Math.round((stats.completedTasks / stats.totalTasks) * 100) 
    : 0

  // 最近任务表格列定义
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/tasks/${record.id}`)}
          style={{ padding: 0 }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TaskStatus) => {
        const statusConfig = {
          [TaskStatus.PENDING]: { color: 'default', text: '等待中' },
          [TaskStatus.RUNNING]: { color: 'processing', text: '运行中' },
          [TaskStatus.COMPLETED]: { color: 'success', text: '已完成' },
          [TaskStatus.FAILED]: { color: 'error', text: '失败' },
          [TaskStatus.CANCELLED]: { color: 'default', text: '已取消' },
          [TaskStatus.PAUSED]: { color: 'warning', text: '已暂停' },
        }
        const config = statusConfig[status]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '进度',
      dataIndex: 'progressPercentage',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Text type="secondary">欢迎回来，{user?.username}！</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.totalTasks}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.runningTasks}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completedTasks}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats.failedTasks}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 成功率 */}
        <Col xs={24} lg={8}>
          <Card title="任务成功率" style={{ height: 300 }}>
            <div style={{ textAlign: 'center', paddingTop: 40 }}>
              <Progress
                type="circle"
                percent={successRate}
                format={percent => `${percent}%`}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">
                  {stats.completedTasks} / {stats.totalTasks} 任务成功
                </Text>
              </div>
            </div>
          </Card>
        </Col>

        {/* 最近任务 */}
        <Col xs={24} lg={16}>
          <Card
            title="最近任务"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/tasks')}
                >
                  新建任务
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchTasks({ page: 1, size: 10 })}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            }
            style={{ height: 300 }}
          >
            <Table
              columns={columns}
              dataSource={tasks.slice(0, 5)}
              pagination={false}
              loading={loading}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Button
              type="primary"
              size="large"
              block
              icon={<PlusOutlined />}
              onClick={() => navigate('/tasks')}
            >
              创建新任务
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              size="large"
              block
              onClick={() => navigate('/data')}
            >
              查看数据
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              size="large"
              block
              onClick={() => navigate('/settings')}
            >
              系统设置
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default Dashboard
