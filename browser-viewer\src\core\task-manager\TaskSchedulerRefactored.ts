/**
 * 任务调度器
 * 
 * - 多个独立的服务
 * - 使用依赖注入消除硬编码依赖
 * - 每个服务明确的单一职责
 */

import { ExceptionService } from '@/services/exception-service/ExceptionService';
import { StateSnapshot } from '@/services/exception-service/StateSnapshot';
import { NotificationService } from '@/services/notification-service/NotificationService';
import { PrivacyService } from '@/services/privacy-service';
import { SecurityService } from '@/services/security-service';
import {
  MESSAGE_TYPES
} from '@/shared/constants';
import {
  AgentEvent,
  TabId,
  TaskConfig,
  TaskId,
  TaskMetrics
} from '@/shared/types';
import {
  createLogger,
  generateTaskId,
  getCurrentTimestamp
} from '@/shared/utils';
import { Observable, Subject } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { EnhancedExceptionHandler } from '../exception-handling/ExceptionHandler';
import { TypedMessenger } from '../messaging/TypedMessenger';
import { AdaptiveThrottler } from './AdaptiveThrottler';
import { ResourceMonitor } from './ResourceMonitor';
import { Task } from './Task';
import {
  BatchOperationService,
  SlotManager,
  TaskControlService,
  TaskQueryService,
  TaskQueueService,
  TaskScheduler,
  TaskStateManager,
  TaskSubmissionService
} from './interfaces';
import { SERVICE_TOKENS, ServiceContainer } from './services/ServiceContainer';

/**
 * 任务控制服务实现
 */
export class TaskControlServiceImpl implements TaskControlService {
  private readonly _logger = createLogger('TaskControlService');
  private readonly _tasks = new Map<TaskId, Task>();
  private readonly _slotManager: SlotManager;

  constructor(slotManager: SlotManager) {
    this._slotManager = slotManager;
  }

  async pauseTask(taskId: TaskId): Promise<void> {
    const task = this._tasks.get(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }
    
    if (task.getStatus() === 'running') {
      await task.pause();
      this._logger.info(`任务 ${taskId} 已暂停`);
    }
  }

  async resumeTask(taskId: TaskId): Promise<void> {
    const task = this._tasks.get(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }
    
    if (task.getStatus() === 'paused') {
      await task.resume();
      this._logger.info(`任务 ${taskId} 已恢复`);
    }
  }

  cancelTask(taskId: TaskId): void {
    const task = this._tasks.get(taskId);
    if (!task) {
      this._logger.warn(`Task ${taskId} not found for cancellation`);
      return;
    }
    
    task.cancel();
    this._logger.info(`任务 ${taskId} 已取消`);
  }

  getTask(taskId: TaskId): Task | undefined {
    return this._tasks.get(taskId);
  }

  getTaskByTabId(tabId: TabId): Task | undefined {
    const slot = this._slotManager.getSlotByTabId(tabId);
    return slot?.currentTask || undefined;
  }

  async pauseAllTasks(): Promise<void> {
    this._logger.info('暂停所有活动任务...');
    const pausePromises: Promise<void>[] = [];
    
    for (const slot of this._slotManager.getAllSlots()) {
      if (slot.isBusy && slot.currentTask) {
        pausePromises.push(this.pauseTask(slot.currentTask._id));
      }
    }
    
    await Promise.all(pausePromises);
    this._logger.info('所有活动任务已暂停');
  }

  clearAllTasks(): void {
    this._logger.info('清理所有非运行状态的任务...');
    
    const tasksToPurge: Task[] = [];
    
    for (const task of this._tasks.values()) {
      if (task.getStatus() !== 'running') {
        tasksToPurge.push(task);
      }
    }
    
    if (tasksToPurge.length > 0) {
      this._logger.info(`找到 ${tasksToPurge.length} 个非运行任务需要清理`);
      for (const task of tasksToPurge) {
        task.purgeData();
        this._tasks.delete(task._id);
        this._logger.debug(`已清理任务 ${task._id}`);
      }
    }
  }

  // 内部方法，供调度器使用
  addTask(taskId: TaskId, task: Task): void {
    this._tasks.set(taskId, task);
  }

  removeTask(taskId: TaskId): void {
    const task = this._tasks.get(taskId);
    if (task) {
      task.purgeData();
      this._tasks.delete(taskId);
    }
  }

  getAllTasks(): Map<TaskId, Task> {
    return new Map(this._tasks);
  }
}

/**
 * 任务查询服务实现
 */
export class TaskQueryServiceImpl implements TaskQueryService {
  private readonly _taskStateManager: TaskStateManager;
  private readonly _slotManager: SlotManager;

  constructor(taskStateManager: TaskStateManager, slotManager: SlotManager) {
    this._taskStateManager = taskStateManager;
    this._slotManager = slotManager;
  }

  getTaskMetrics(taskId: TaskId): TaskMetrics | null {
    return this._taskStateManager.getTaskMetrics(taskId);
  }

  getAllTasks(): TaskMetrics[] {
    return this._taskStateManager.getAllTaskMetrics();
  }

  getRunningTasksCount(): number {
    return this._slotManager.getRunningTasksCount();
  }

  getTasksByBatchId(batchId: string): TaskMetrics[] {
    return this._taskStateManager.getAllTaskMetrics()
      .filter(task => task.batchId === batchId);
  }

  getTasksByStatus(status: string): TaskMetrics[] {
    return this._taskStateManager.getAllTaskMetrics()
      .filter(task => task.status === status);
  }
}

/**
 * 任务提交服务实现
 */
export class TaskSubmissionServiceImpl implements TaskSubmissionService {
  private readonly _logger = createLogger('TaskSubmissionService');
  private readonly _taskQueueService: TaskQueueService;
  private readonly _taskStateManager: TaskStateManager;

  constructor(taskQueueService: TaskQueueService, taskStateManager: TaskStateManager) {
    this._taskQueueService = taskQueueService;
    this._taskStateManager = taskStateManager;
  }

  submitBatch(configs: TaskConfig[]): void {
    this._logger.info(`接收到 ${configs.length} 个批处理任务`);
    
    // 验证和预处理配置
    const validConfigs = configs
      .filter(config => this.validateTaskConfig(config))
      .map(config => this.preprocessTaskConfig(config));
    
    if (validConfigs.length === 0) {
      this._logger.warn('没有有效的任务配置');
      return;
    }
    
    // 添加到队列
    this._taskQueueService.enqueue(validConfigs);
    
    // 创建待处理任务指标
    this._taskStateManager.createPendingTaskMetrics(validConfigs);
    
    this._logger.info(`成功提交 ${validConfigs.length} 个任务`);
  }

  validateTaskConfig(config: TaskConfig): boolean {
    if (!config) {
      this._logger.warn('任务配置为空');
      return false;
    }
    
    if (!Array.isArray(config.urls) || config.urls.length === 0) {
      this._logger.warn('任务配置缺少有效的URL列表');
      return false;
    }
    
    // 验证URL格式
    for (const url of config.urls) {
      try {
        new URL(url);
      } catch {
        this._logger.warn(`无效的URL: ${url}`);
        return false;
      }
    }
    
    return true;
  }

  preprocessTaskConfig(config: TaskConfig): TaskConfig {
    return {
      ...config,
      batchId: config.batchId ?? crypto.randomUUID(),
      priority: config.priority ?? 10,
    };
  }
}

/**
 * 批量操作服务实现
 */
export class BatchOperationServiceImpl implements BatchOperationService {
  private readonly _logger = createLogger('BatchOperationService');
  private readonly _taskQueueService: TaskQueueService;
  private readonly _taskControlService: TaskControlService;
  private readonly _taskStateManager: TaskStateManager;

  constructor(
    taskQueueService: TaskQueueService,
    taskControlService: TaskControlService,
    taskStateManager: TaskStateManager
  ) {
    this._taskQueueService = taskQueueService;
    this._taskControlService = taskControlService;
    this._taskStateManager = taskStateManager;
  }

  async startSelectedTasks(batchIds: string[]): Promise<void> {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('startSelectedTasks 调用时没有提供 batchIds');
      return;
    }
    
    this._logger.info(`启动指定批处理的任务: ${batchIds.join(', ')}`);
    
    // 处理现有任务（暂停、失败的任务）
    const tasksToRetry: TaskConfig[] = [];
    const resumePromises: Promise<void>[] = [];
    
    const allTasks = this._taskControlService.getAllTasks();
    for (const task of allTasks.values()) {
      if (task.batchId && batchIds.includes(task.batchId)) {
        const status = task.getStatus();
        if (status === 'paused') {
          this._logger.info(`恢复暂停的任务 ${task._id}`);
          resumePromises.push(task.resume());
        } else if (status === 'failed' || status === 'cancelled') {
          this._logger.info(`重试失败/取消的任务 ${task._id}`);
          tasksToRetry.push(task._config);
          this._taskControlService.removeTask(task._id);
          this._taskStateManager.removeTaskMetrics(task._id);
        }
      }
    }
    
    await Promise.all(resumePromises);
    
    // 将重试任务和选中的待处理任务优先排队
    if (tasksToRetry.length > 0) {
      this._taskQueueService.prependTasks(tasksToRetry);
    }
    this._taskQueueService.prioritizeBatches(batchIds);
    
    this._logger.info(`批处理任务启动完成，重试 ${tasksToRetry.length} 个任务`);
  }

  async pauseSelectedTasks(batchIds: string[]): Promise<void> {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('pauseSelectedTasks 调用时没有提供 batchIds');
      return;
    }
    
    this._logger.info(`暂停指定批处理的任务: ${batchIds.join(', ')}`);
    
    const pausePromises: Promise<void>[] = [];
    const allTasks = this._taskControlService.getAllTasks();
    
    for (const task of allTasks.values()) {
      if (task.batchId && batchIds.includes(task.batchId) && task.getStatus() === 'running') {
        pausePromises.push(this._taskControlService.pauseTask(task._id));
      }
    }
    
    await Promise.all(pausePromises);
    this._logger.info(`暂停了 ${pausePromises.length} 个任务`);
  }

  clearSelectedTasks(batchIds: string[]): void {
    if (!batchIds || batchIds.length === 0) {
      this._logger.warn('clearSelectedTasks 调用时没有提供 batchIds');
      return;
    }
    
    this._logger.info(`清理指定批处理的任务: ${batchIds.join(', ')}`);
    
    // 从队列中移除
    const removedFromQueue = this._taskQueueService.removeBatchTasks(batchIds);
    
    // 清理非运行状态的任务
    const allTasks = this._taskControlService.getAllTasks();
    for (const task of allTasks.values()) {
      if (task.batchId && batchIds.includes(task.batchId) && task.getStatus() !== 'running') {
        this._taskControlService.removeTask(task._id);
        this._taskStateManager.removeTaskMetrics(task._id);
      }
    }
    
    // 移除相关的任务指标
    this._taskStateManager.removeTaskMetricsByBatchIds(batchIds);
    
    this._logger.info(`清理完成，从队列移除 ${removedFromQueue.length} 个任务`);
  }
}

/**
 * 重构后的主任务调度器
 */
export class TaskSchedulerRefactored implements TaskScheduler {
  private readonly _logger = createLogger('TaskScheduler');
  private readonly _destroy$ = new Subject<void>();
  private readonly _taskEventsInternal$ = new Subject<AgentEvent>();
  
  // 依赖的服务
  private readonly _slotManager: SlotManager;
  private readonly _taskStateManager: TaskStateManager;
  private readonly _taskQueueService: TaskQueueService;
  private readonly _taskControlService: TaskControlService;
  private readonly _taskSubmissionService: TaskSubmissionService;
  private readonly _taskQueryService: TaskQueryService;
  private readonly _batchOperationService: BatchOperationService;
  private readonly _resourceMonitor: ResourceMonitor;
  private readonly _adaptiveThrottler: AdaptiveThrottler;
  private readonly _typedMessenger: TypedMessenger;
  private readonly _enhancedExceptionHandler: EnhancedExceptionHandler;
  
  // 调度参数
  private _initialMaxConcurrency: number;
  private _maxConcurrency: number;
  private _isShuttingDown = false;

  // 公开的 Observable
  public get taskMetrics$(): Observable<TaskMetrics[]> {
    return this._taskStateManager.getTaskMetrics$();
  }

  public get taskEvents$(): Observable<AgentEvent> {
    return this._taskEventsInternal$.asObservable();
  }

  constructor(container: ServiceContainer) {
    // 从容器获取所有依赖
    this._slotManager = container.get<SlotManager>(SERVICE_TOKENS.SLOT_MANAGER);
    this._taskStateManager = container.get<TaskStateManager>(SERVICE_TOKENS.TASK_STATE_MANAGER);
    this._taskQueueService = container.get<TaskQueueService>(SERVICE_TOKENS.TASK_QUEUE_SERVICE);
    this._taskControlService = container.get<TaskControlService>(SERVICE_TOKENS.TASK_CONTROL_SERVICE);
    this._taskSubmissionService = container.get<TaskSubmissionService>(SERVICE_TOKENS.TASK_SUBMISSION_SERVICE);
    this._taskQueryService = container.get<TaskQueryService>(SERVICE_TOKENS.TASK_QUERY_SERVICE);
    this._batchOperationService = container.get<BatchOperationService>(SERVICE_TOKENS.BATCH_OPERATION_SERVICE);
    this._resourceMonitor = container.get<ResourceMonitor>(SERVICE_TOKENS.RESOURCE_MONITOR);
    this._adaptiveThrottler = container.get<AdaptiveThrottler>(SERVICE_TOKENS.ADAPTIVE_THROTTLER);
    this._typedMessenger = container.get<TypedMessenger>(SERVICE_TOKENS.TYPED_MESSENGER);
    this._enhancedExceptionHandler = container.get<EnhancedExceptionHandler>(SERVICE_TOKENS.ENHANCED_EXCEPTION_HANDLER);

    this._initialMaxConcurrency = 2;
    this._maxConcurrency = 2;
    
    // 设置消息系统
    this._setupMessageHandlers();
  }

  async initializeScheduler(): Promise<void> {
    this._logger.info('插件任务调度器正在连接...');
    
    const initialConcurrency = Math.max(2, Math.floor(navigator.hardwareConcurrency / 2));
    this._initialMaxConcurrency = initialConcurrency;
    this._maxConcurrency = initialConcurrency;
    
    this._setupThrottlingActions();
    this._resourceMonitor.metrics$
      .pipe(throttleTime(1000), takeUntil(this._destroy$))
      .subscribe(metrics => this._adaptiveThrottler.adjust(metrics));
    
    await this._restoreStateFromStorage();
    this._logger.info('插件任务调度器已连接');
  }

  submitBatch(configs: TaskConfig[]): void {
    if (this._isShuttingDown) {
      this._logger.warn('调度器正在关闭，拒绝新任务');
      return;
    }
    
    this._taskSubmissionService.submitBatch(configs);
    
    // 触发调度
    void this._scheduleTasks();
    
    // 保存状态
    void this._saveStateToStorage();
  }

  async startAllPendingTasks(): Promise<void> {
    this._logger.info('手动触发启动所有未完成的任务');
    
    // 恢复所有暂停的任务
    const resumePromises: Promise<void>[] = [];
    const tasksToRetry: TaskConfig[] = [];
    
    const allTasks = this._taskControlService.getAllTasks();
    for (const task of allTasks.values()) {
      const status = task.getStatus();
      if (status === 'paused') {
        this._logger.info(`恢复暂停的任务 ${task._id}`);
        resumePromises.push(task.resume());
      } else if (status === 'failed' || status === 'cancelled') {
        this._logger.info(`重新排队失败/取消的任务 ${task._id}`);
        tasksToRetry.push(task._config);
        this._taskControlService.removeTask(task._id);
        this._taskStateManager.removeTaskMetrics(task._id);
      }
    }
    
    await Promise.all(resumePromises);
    
    // 将重试任务添加到队列前端
    if (tasksToRetry.length > 0) {
      this._taskQueueService.prependTasks(tasksToRetry);
      this._logger.info(`添加了 ${tasksToRetry.length} 个任务进行重试`);
    }
    
    // 调度所有待处理任务
    await this._scheduleTasks();
    await this._saveStateToStorage();
  }

  async shutdown(): Promise<void> {
    if (this._isShuttingDown) return;
    
    this._logger.info('关闭任务调度器...');
    this._isShuttingDown = true;
    
    // 清空待处理队列
    this._taskQueueService.clearQueue();
    
    // 取消所有正在运行的任务
    const allTasks = this._taskControlService.getAllTasks();
    await Promise.all(Array.from(allTasks.values()).map(task => task.cancel()));
    
    // 关闭所有槽位
    await this._slotManager.shutdownAllSlots();
    
    // 保存最终状态
    await this._saveStateToStorage();
    
    // 停止监控
    if (this._resourceMonitor.isMonitoring()) {
      this._resourceMonitor.stop();
    }
    
    this._destroy$.next();
    this._destroy$.complete();
    
    this._logger.info('插件任务调度器已关闭');
  }

  // 核心调度逻辑
  private async _scheduleTasks(): Promise<void> {
    if (this._isShuttingDown) return;
    
    this._logger.debug('运行调度周期...');
    
    // 按需调整槽位数量
    const queueLength = this._taskQueueService.getQueueLength();
    if (queueLength > 0) {
      await this._slotManager.adjustSlotsForConcurrency(this._maxConcurrency, queueLength);
    }
    
    const availableSlots = this._slotManager.getAvailableSlots();
    
    // 如果有任务要执行且监控器未运行，启动监控
    if (queueLength > 0 && availableSlots.length > 0 && !this._resourceMonitor.isMonitoring()) {
      this._logger.info('任务即将开始，启动资源监控');
      this._resourceMonitor.start();
    }
    
    if (availableSlots.length === 0) {
      this._logger.debug('没有可用的槽位');
      return;
    }
    
    if (queueLength === 0) {
      this._logger.debug('待处理队列为空');
      // 如果队列为空且没有正在运行的任务，关闭所有槽位
      if (this._taskQueryService.getRunningTasksCount() === 0) {
        this._logger.info('所有任务已完成且队列为空，关闭所有槽位');
        await this._slotManager.shutdownAllSlots();
      }
      return;
    }
    
    // 为每个空闲槽位分配任务
    for (const slot of availableSlots) {
      if (this._taskQueueService.getQueueLength() === 0) break;
      
      const taskConfig = this._taskQueueService.dequeue(slot);
      if (taskConfig) {
        this._assignTaskToSlot(slot, taskConfig);
      }
    }
  }

  // 将任务分配给槽位
  private _assignTaskToSlot(slot: any, taskConfig: TaskConfig): void {
    this._logger.info(`将任务 ${taskConfig.urls[0]} 分配给槽位 ${slot.id} (Tab ${slot.tabId})`);
    
    // 移除临时的待处理指标
    if (taskConfig.batchId) {
      this._taskStateManager.removePendingTaskMetrics(taskConfig.batchId, taskConfig.urls[0]);
    }
    
    // 创建新任务
    const taskId = generateTaskId();
    const task = new Task(
      taskId,
      taskConfig,
      // 这里需要从容器获取依赖，但为了简化暂时保持原有方式
      // TODO: 重构Task类也使用依赖注入
      new ExceptionService(),
      new StateSnapshot(PrivacyService.getInstance()),
      new NotificationService(),
      PrivacyService.getInstance(),
      SecurityService.getInstance()
    );
    
    // 注册任务
    this._taskControlService.addTask(taskId, task);
    this._slotManager.markSlotBusy(slot.id, task);
    
    // 监听任务事件
    this._monitorTask(task);
    
    // 启动任务
    void task.start(slot.tabId);
  }

  // 监听任务状态变化
  private _monitorTask(task: Task): void {
    task.events$.pipe(takeUntil(this._destroy$)).subscribe(event => {
      if (event.type === 'statusChange' && ['completed', 'failed', 'cancelled'].includes(event.payload.newStatus)) {
        void this._handleTaskCompletion(task);
      } else if (event.type === 'statusChange') {
        this._taskStateManager.updateTaskMetrics(task._id, { status: event.payload.newStatus });
        this._taskEventsInternal$.next({
          type: MESSAGE_TYPES.TASK_STATUS_CHANGE,
          data: { taskId: task._id, newStatus: event.payload.newStatus },
          timestamp: getCurrentTimestamp(),
          source: 'Task'
        });
      }
    });
  }

  // 处理任务完成
  private async _handleTaskCompletion(task: Task): Promise<void> {
    const taskId = task._id;
    this._logger.info(`任务 ${taskId} 已完成: ${task.getStatus()}`);
    
    // 找到对应的槽位并释放
    const slots = this._slotManager.getAllSlots();
    const slot = slots.find(s => s.currentTask?._id === taskId);
    
    if (slot) {
      try {
        const url = task._config.urls[0];
        const lastDomain = new URL(url).hostname;
        this._slotManager.markSlotFree(slot.id, lastDomain);
      } catch {
        this._slotManager.markSlotFree(slot.id);
      }
      
      // 检查是否需要平滑缩容
      await this._slotManager.performSmoothScaleDown(this._maxConcurrency, slot.id);
      
      this._logger.info(`槽位 ${slot.id} 已释放`);
      
      // 检查是否所有工作都已完成
      if (this._taskQueryService.getRunningTasksCount() === 0 && this._taskQueueService.getQueueLength() === 0) {
        this._logger.info('最后一个任务已完成且队列为空，关闭所有槽位');
        await this._slotManager.shutdownAllSlots();
      } else {
        void this._scheduleTasks();
      }
    } else {
      this._logger.warn(`无法为已完成的任务 ${taskId} 找到对应槽位`);
    }
    
    // 更新任务指标
    this._taskStateManager.updateTaskMetrics(taskId, {
      status: task.getStatus(),
      endTime: getCurrentTimestamp(),
    });
    
    // 清理已完成的任务
    this._taskStateManager.pruneBatches(25);
  }

  // 设置节流响应动作
  private _setupThrottlingActions(): void {
    this._logger.info('设置节流响应动作...');
    
    this._adaptiveThrottler.on(0, () => {
      this._logger.info('节流级别 0: 恢复正常并发');
      void this._setDynamicMaxConcurrency(this._initialMaxConcurrency);
      this._broadcastThrottlingLevel(0);
    });
    
    this._adaptiveThrottler.on(1, () => {
      this._logger.info('节流级别 1: 轻度节流');
      const newConcurrency = Math.max(1, Math.floor(this._initialMaxConcurrency * 0.75));
      void this._setDynamicMaxConcurrency(newConcurrency);
      this._broadcastThrottlingLevel(1);
    });
    
    this._adaptiveThrottler.on(2, () => {
      this._logger.warn('节流级别 2: 深度节流');
      const newConcurrency = Math.max(1, Math.floor(this._initialMaxConcurrency * 0.5));
      void this._setDynamicMaxConcurrency(newConcurrency);
      this._broadcastThrottlingLevel(2);
    });
  }

  // 动态调整最大并发数
  private async _setDynamicMaxConcurrency(newMax: number): Promise<void> {
    const oldMaxConcurrency = this._maxConcurrency;
    this._maxConcurrency = newMax;
    
    this._logger.info(`动态并发调整: ${oldMaxConcurrency} -> ${this._maxConcurrency}`);
    
    // 如果需要扩容，立即执行
    if (newMax > oldMaxConcurrency) {
      const queueLength = this._taskQueueService.getQueueLength();
      if (queueLength > 0) {
        await this._slotManager.adjustSlotsForConcurrency(newMax, queueLength);
        void this._scheduleTasks();
      }
    }
    // 缩容在任务完成时自然进行
  }

  // 向所有活动任务广播节流级别
  private _broadcastThrottlingLevel(level: number): void {
    this._logger.info(`向所有活动任务广播节流级别: ${level}`);
    const allTasks = this._taskControlService.getAllTasks();
    for (const task of allTasks.values()) {
      if (task.getStatus() === 'running') {
        task.applyThrottling(level);
      }
    }
  }

  // 状态持久化
  private async _restoreStateFromStorage(): Promise<void> {
    try {
      const restoredConfigs = await this._taskStateManager.restoreState();
      if (restoredConfigs.length > 0) {
        this._taskQueueService.enqueue(restoredConfigs);
        this._logger.info(`成功恢复 ${restoredConfigs.length} 个任务到队列`);
      }
    } catch (error) {
      this._logger.error('恢复任务状态失败:', error);
    }
  }

  private async _saveStateToStorage(): Promise<void> {
    try {
      const allTasks = this._taskStateManager.getAllTaskMetrics();
      await this._taskStateManager.saveState(allTasks);
    } catch (error) {
      this._logger.error('保存任务状态失败:', error);
    }
  }

  // 代理方法，保持向后兼容
  async pauseTask(taskId: TaskId): Promise<void> {
    return this._taskControlService.pauseTask(taskId);
  }

  async resumeTask(taskId: TaskId): Promise<void> {
    return this._taskControlService.resumeTask(taskId);
  }

  cancelTask(taskId: TaskId): void {
    this._taskControlService.cancelTask(taskId);
  }

  getTaskMetrics(taskId: TaskId): TaskMetrics | null {
    return this._taskQueryService.getTaskMetrics(taskId);
  }

  getTask(taskId: TaskId): Task | undefined {
    return this._taskControlService.getTask(taskId);
  }

  getAllTasks(): TaskMetrics[] {
    return this._taskQueryService.getAllTasks();
  }

  getRunningTasksCount(): number {
    return this._taskQueryService.getRunningTasksCount();
  }

  getTaskByTabId(tabId: number): Task | undefined {
    return this._taskControlService.getTaskByTabId(tabId);
  }

  async pauseAllTasks(): Promise<void> {
    return this._taskControlService.pauseAllTasks();
  }

  async clearAllTasks(): Promise<void> {
    this._taskControlService.clearAllTasks();
    this._taskQueueService.clearQueue();
    await this._saveStateToStorage();
  }

  async startSelectedTasks(batchIds: string[]): Promise<void> {
    await this._batchOperationService.startSelectedTasks(batchIds);
    await this._scheduleTasks();
    await this._saveStateToStorage();
  }

  async pauseSelectedTasks(batchIds: string[]): Promise<void> {
    return this._batchOperationService.pauseSelectedTasks(batchIds);
  }

  async clearSelectedTasks(batchIds: string[]): Promise<void> {
    this._batchOperationService.clearSelectedTasks(batchIds);
    await this._saveStateToStorage();
  }

  // 新增的消息处理器设置
  private _setupMessageHandlers(): void {
    // 监听用户操作消息
    this._typedMessenger.listen('USER_ACTION', async (payload) => {
      try {
        switch (payload.action) {
          case 'start':
            if (payload.target === 'all') {
              await this.startAllPendingTasks();
            } else if (payload.batchIds) {
              await this.startSelectedTasks(payload.batchIds);
            }
            break;
          case 'pause':
            if (payload.target === 'all') {
              await this.pauseAllTasks();
            } else if (payload.batchIds) {
              await this.pauseSelectedTasks(payload.batchIds);
            }
            break;
          case 'stop':
            if (payload.target === 'all') {
              await this.clearAllTasks();
            } else if (payload.batchIds) {
              await this.clearSelectedTasks(payload.batchIds);
            }
            break;
        }
      } catch (error) {
        this._enhancedExceptionHandler.handleException(
          error instanceof Error ? error : new Error(String(error)),
          {
            operation: 'user_action_handling',
            timestamp: Date.now(),
            metadata: { action: payload.action, target: payload.target }
          }
        );
      }
    });

    // 监听系统异常
    this._enhancedExceptionHandler.registerListener({
      onExceptionOccurred: (exception) => {
        void this._typedMessenger.send('EXCEPTION_OCCURRED', {
          type: exception.type as any,
          message: exception.message,
          context: exception.context,
          recoverable: exception.recoverable
        });
      },
      onRecoveryAttempted: (exception, result) => {
        void this._typedMessenger.send('RECOVERY_ACTION_TAKEN', {
          exceptionId: exception.id,
          action: result.action as any,
          success: result.success
        });
      },
      onRecoveryCompleted: (exception, result) => {
        this._logger.info(`异常恢复完成: ${exception.id}`, { action: result.action, success: result.success });
      }
    });
  }

  // 异常处理
  private _handleTaskError(task: Task, error: Error): void {
    try {
      const result = this._enhancedExceptionHandler.handleException(error, {
        taskId: task._id,
        operation: 'task_execution',
        url: task._config.urls[0],
        timestamp: Date.now()
      });

      switch (result.action) {
        case 'retry':
          if (result.retryAfter) {
            setTimeout(() => {
              void task.start(task._config.urls[0] as any); // 简化示例
            }, result.retryAfter);
          }
          break;
        case 'skip':
          this._logger.info(`跳过任务 ${task._id}: ${result.message}`);
          break;
        case 'abort':
          this._logger.error(`终止任务 ${task._id}: ${result.message}`);
          task.cancel();
          break;
      }
    } catch (handlingError) {
      this._logger.error('处理任务错误时发生异常:', handlingError);
    }
  }

  // 发送状态更新消息
  private _broadcastStatusUpdate(): void {
    const runningTaskCount = this._taskQueryService.getRunningTasksCount();
    const queueLength = this._taskQueueService?.getQueueLength() || 0;
    
    void this._typedMessenger.send('SYSTEM_STATUS_UPDATE', {
      cpuUsage: 0, // 需要从资源监控器获取
      memoryUsage: 0,
      networkUsage: 0,
      activeTaskCount: runningTaskCount,
      queuedTaskCount: queueLength
    });
  }
}