/**
 * 统一状态管理接口
 * 
 * - 提供事务性状态管理
 * - 状态订阅机制
 * - 冲突解决策略
 */

import { Observable } from 'rxjs';

// 状态键类型
export type StateKey = string; // NOSONAR

// 订阅函数类型
export type StateSubscription = {
  unsubscribe(): void;
};

// 事务上下文接口
export interface TransactionContext {
  /**
   * 在事务中保存数据
   */
  save<T>(key: StateKey, data: T): void;
  
  /**
   * 在事务中读取数据
   */
  load<T>(key: StateKey): T | null;
  
  /**
   * 在事务中删除数据
   */
  delete(key: StateKey): void;
  
  /**
   * 提交事务
   */
  commit(): Promise<void>;
  
  /**
   * 回滚事务
   */
  rollback(): void;
  
  /**
   * 检查事务是否已完成
   */
  isCompleted(): boolean;
}

// 冲突解决策略
export enum ConflictResolution {
  OVERWRITE = 'overwrite',          // 覆盖现有数据
  MERGE = 'merge',                  // 合并数据
  KEEP_EXISTING = 'keep_existing',  // 保持现有数据
  FAIL = 'fail'                     // 抛出异常
}

// 状态存储配置
export interface StateStoreConfig {
  /**
   * 默认冲突解决策略
   */
  defaultConflictResolution: ConflictResolution;
  
  /**
   * 是否启用数据压缩
   */
  enableCompression: boolean;
  
  /**
   * 是否启用数据加密
   */
  enableEncryption: boolean;
  
  /**
   * 状态持久化间隔（毫秒）
   */
  persistenceInterval: number;
  
  /**
   * 最大缓存大小
   */
  maxCacheSize: number;
  
  /**
   * 事务超时时间（毫秒）
   */
  transactionTimeout: number;
}

// 状态变更事件
export interface StateChangeEvent<T = any> {
  key: StateKey;
  oldValue: T | null;
  newValue: T | null;
  timestamp: number;
  source: string;
}

// 主状态存储接口
export interface StateStore {
  /**
   * 保存数据
   */
  save<T>(key: StateKey, data: T, conflictResolution?: ConflictResolution): Promise<void>;
  
  /**
   * 加载数据
   */
  load<T>(key: StateKey): Promise<T | null>;
  
  /**
   * 删除数据
   */
  delete(key: StateKey): Promise<void>;
  
  /**
   * 检查键是否存在
   */
  exists(key: StateKey): Promise<boolean>;
  
  /**
   * 获取所有键
   */
  keys(): Promise<StateKey[]>;
  
  /**
   * 清空所有数据
   */
  clear(): Promise<void>;
  
  /**
   * 订阅状态变更
   */
  subscribe<T>(key: StateKey, callback: (event: StateChangeEvent<T>) => void): StateSubscription;
  
  /**
   * 订阅所有状态变更
   */
  subscribeAll(callback: (event: StateChangeEvent) => void): StateSubscription;
  
  /**
   * 获取状态变更的Observable流
   */
  getChangeStream<T>(key: StateKey): Observable<StateChangeEvent<T>>;
  
  /**
   * 执行事务
   */
  withTransaction<T>(operation: (ctx: TransactionContext) => Promise<T>): Promise<T>;
  
  /**
   * 批量操作
   */
  batch(operations: Array<{ type: 'save' | 'delete', key: StateKey, data?: any }>): Promise<void>;
  
  /**
   * 获取存储统计信息
   */
  getStats(): Promise<StateStoreStats>;
}

// 存储统计信息
export interface StateStoreStats {
  totalKeys: number;
  totalSize: number;
  cacheHitRate: number;
  lastPersistTime: number;
  transactionCount: number;
  failedTransactionCount: number;
}

// 分层状态存储接口
export interface LayeredStateStore extends StateStore {
  /**
   * 创建子层
   */
  createLayer(name: string): LayeredStateStore;
  
  /**
   * 获取子层
   */
  getLayer(name: string): LayeredStateStore | null;
  
  /**
   * 删除子层
   */
  deleteLayer(name: string): Promise<void>;
  
  /**
   * 合并子层到父层
   */
  mergeLayer(name: string): Promise<void>;
  
  /**
   * 获取当前层的名称
   */
  getLayerName(): string;
}