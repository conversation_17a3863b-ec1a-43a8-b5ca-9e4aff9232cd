/**
 * 测试运行脚本
 * 提供不同类型的测试运行选项
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

interface TestOptions {
  type: 'unit' | 'integration' | 'e2e' | 'all';
  watch: boolean;
  coverage: boolean;
  verbose: boolean;
  pattern?: string;
}

class TestRunner {
  private readonly projectRoot: string;

  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
  }

  /**
   * 运行指定类型的测试
   */
  runTests(options: TestOptions): void {
    console.log(`🧪 Running ${options.type} tests...`);
    
    const command = this.buildCommand(options);
    
    try {
      console.log(`📝 Command: ${command}`);
      execSync(command, { 
        cwd: this.projectRoot, 
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: 'test' }
      });
      
      console.log(`✅ ${options.type} tests completed successfully!`);
    } catch (error: unknown) {
      console.error(`❌ ${options.type} tests failed!`, error);
      process.exit(1);
    }
  }

  /**
   * 构建vitest命令
   */
  private buildCommand(options: TestOptions): string {
    const parts = ['npx vitest'];
    
    // 测试类型和路径
    switch (options.type) {
      case 'unit':
        parts.push('tests/unit');
        break;
      case 'integration':
        parts.push('tests/integration');
        break;
      case 'e2e':
        parts.push('tests/e2e');
        break;
      case 'all':
        parts.push('tests');
        break;
    }
    
    // 监听模式
    if (options.watch) {
      // vitest 默认是监听模式，添加 run 参数来禁用监听
    } else {
      parts.push('run');
    }
    
    // 覆盖率报告
    if (options.coverage) {
      parts.push('--coverage');
    }
    
    // 详细输出
    if (options.verbose) {
      parts.push('--reporter=verbose');
    }
    
    // 文件模式匹配
    if (options.pattern) {
      parts.push(`--testNamePattern="${options.pattern}"`);
    }
    
    return parts.join(' ');
  }

  /**
   * 检查测试环境
   */
  checkEnvironment(): boolean {
    const requiredFiles = [
      'vitest.config.ts',
      'tests/setup/global-setup.ts',
      'tests/setup/chrome-api-setup.ts',
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (!existsSync(filePath)) {
        console.error(`❌ Required file not found: ${file}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 显示测试统计
   */
  showTestStats(): void {
    console.log('\n📊 Test Statistics:');
    
    try {
      // 获取测试文件统计
      const unitTests = this.countTestFiles('tests/unit');
      const integrationTests = this.countTestFiles('tests/integration');
      const e2eTests = this.countTestFiles('tests/e2e');
      
      console.log(`   Unit Tests: ${unitTests} files`);
      console.log(`   Integration Tests: ${integrationTests} files`);
      console.log(`   E2E Tests: ${e2eTests} files`);
      console.log(`   Total: ${unitTests + integrationTests + e2eTests} test files`);
    } catch (error: unknown) {
      console.warn('⚠️  Could not calculate test statistics', error);
    }
  }

  /**
   * 计算测试文件数量
   */
  private countTestFiles(directory: string): number {
    try {
      const fullPath = path.join(this.projectRoot, directory);
      if (!existsSync(fullPath)) return 0;
      
      const result = execSync(`find "${fullPath}" -name "*.test.ts" -o -name "*.spec.ts" | wc -l`, {
        encoding: 'utf8',
        cwd: this.projectRoot
      });
      
      return parseInt(result.trim()) || 0;
    } catch (error: unknown) {
      console.debug('Error counting test files:', error);
      return 0;
    }
  }

  /**
   * 运行代码覆盖率检查
   */
  runCoverageCheck(): void {
    console.log('\n🔍 Running coverage analysis...');
    
    try {
      execSync('npx vitest run --coverage', {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });
      
      console.log('✅ Coverage analysis completed!');
      console.log('📄 Coverage report available in tests/coverage/');
    } catch (error: unknown) {
      console.error('❌ Coverage analysis failed!', error);
      throw error;
    }
  }

  /**
   * 清理测试缓存
   */
  cleanCache(): void {
    console.log('🧹 Cleaning test cache...');
    
    try {
      // 清理 vitest 缓存
      execSync('npx vitest --clearCache', {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });
      
      console.log('✅ Test cache cleaned!');
    } catch (error: unknown) {
      console.warn('⚠️  Could not clean test cache', error);
    }
  }
}

// CLI 接口
function parseArgs(): TestOptions {
  const args = process.argv.slice(2);
  
  const options: TestOptions = {
    type: 'all',
    watch: false,
    coverage: false,
    verbose: false,
  };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--unit':
        options.type = 'unit';
        break;
      case '--integration':
        options.type = 'integration';
        break;
      case '--e2e':
        options.type = 'e2e';
        break;
      case '--watch':
        options.watch = true;
        break;
      case '--coverage':
        options.coverage = true;
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--pattern':
        if (i + 1 < args.length) {
          i++; // 递增索引
          options.pattern = args[i]; // 获取下一个参数
        }
        break;
      case '--help':
        showHelp();
        process.exit(0);
    }
  }
  
  return options;
}

function showHelp(): void {
  console.log(`
🧪 Browser Viewer Test Runner

Usage: npm run test [options]

Options:
  --unit          Run only unit tests
  --integration   Run only integration tests  
  --e2e           Run only e2e tests
  --watch         Run tests in watch mode
  --coverage      Generate coverage report
  --verbose       Show detailed output
  --pattern <pattern>  Run tests matching pattern
  --help          Show this help

Examples:
  npm run test                    # Run all tests
  npm run test -- --unit         # Run unit tests only
  npm run test -- --coverage     # Run all tests with coverage
  npm run test -- --watch        # Run tests in watch mode
  npm run test -- --pattern="Task"  # Run tests matching "Task"

Environment Commands:
  npm run test:unit               # Run unit tests
  npm run test:integration        # Run integration tests
  npm run test:e2e               # Run e2e tests
  npm run test:coverage          # Run with coverage
  npm run test:watch             # Run in watch mode
`);
}

// 主函数
function main(): void {
  const runner = new TestRunner();
  
  // 检查环境
  if (!runner.checkEnvironment()) {
    console.error('❌ Test environment check failed!');
    process.exit(1);
  }
  
  const options = parseArgs();
  
  try {
    // 显示测试统计
    runner.showTestStats();
    
    // 运行测试
    runner.runTests(options);
    
    // 如果请求覆盖率报告，生成报告
    if (options.coverage) {
      console.log('\n📊 Coverage report generated in tests/coverage/');
    }
    
  } catch (error: unknown) {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  }
}

// 仅在直接运行时执行
if (require.main === module) {
  try {
    main();
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('💥 Unexpected error:', errorMessage);
    process.exit(1);
  }
}

export { TestRunner };
export type { TestOptions };

