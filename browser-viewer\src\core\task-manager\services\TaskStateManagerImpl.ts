/**
 * 任务状态管理器实现
 * 
 * 职责：
 * - 任务状态的持久化和恢复
 * - 任务指标的内存管理
 * - 提供任务状态的Observable流
 */

import { SecurityService } from '@/services/security-service';
import { STORAGE_KEYS } from '@/shared/constants';
import { TaskConfig, TaskId, TaskMetrics } from '@/shared/types';
import { createLogger, getStorageData, setStorageData } from '@/shared/utils';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { TaskStateManager } from '../interfaces';

export class TaskStateManagerImpl implements TaskStateManager {
  private readonly _logger = createLogger('TaskStateManager');
  private readonly _taskMetrics$ = new BehaviorSubject<Map<TaskId, TaskMetrics>>(new Map());
  private readonly _securityService: SecurityService;

  constructor(securityService: SecurityService) {
    this._securityService = securityService;
  }

  getTaskMetrics$(): Observable<TaskMetrics[]> {
    return this._taskMetrics$.asObservable().pipe(
      map(metricsMap => Array.from(metricsMap.values()))
    );
  }

  updateTaskMetrics(taskId: TaskId, metrics: Partial<TaskMetrics>): void {
    const currentMap = this._taskMetrics$.value;
    const existingMetrics = currentMap.get(taskId);
    
    if (existingMetrics) {
      const updatedMetrics: TaskMetrics = {
        ...existingMetrics,
        ...metrics,
      };
      
      const newMap = new Map(currentMap);
      newMap.set(taskId, updatedMetrics);
      this._taskMetrics$.next(newMap);
      
      this._logger.debug(`任务 ${taskId} 的指标已更新`);
    } else {
      this._logger.warn(`尝试更新不存在任务的指标: ${taskId}`);
    }
  }

  addTaskMetrics(taskId: TaskId, metrics: TaskMetrics): void {
    const currentMap = this._taskMetrics$.value;
    
    if (currentMap.has(taskId)) {
      this._logger.warn(`任务 ${taskId} 的指标已存在，将被覆盖`);
    }
    
    const newMap = new Map(currentMap);
    newMap.set(taskId, metrics);
    this._taskMetrics$.next(newMap);
    
    this._logger.debug(`任务 ${taskId} 的指标已添加`);
  }

  removeTaskMetrics(taskId: TaskId): void {
    const currentMap = this._taskMetrics$.value;
    
    if (currentMap.has(taskId)) {
      const newMap = new Map(currentMap);
      newMap.delete(taskId);
      this._taskMetrics$.next(newMap);
      
      this._logger.debug(`任务 ${taskId} 的指标已移除`);
    } else {
      this._logger.warn(`尝试移除不存在的任务指标: ${taskId}`);
    }
  }

  getTaskMetrics(taskId: TaskId): TaskMetrics | null {
    return this._taskMetrics$.value.get(taskId) || null;
  }

  getAllTaskMetrics(): TaskMetrics[] {
    return Array.from(this._taskMetrics$.value.values());
  }

  async saveState(tasks: TaskMetrics[]): Promise<void> {
    try {
      // 只保存待处理的任务配置，不保存所有指标
      const pendingConfigs = tasks
        .filter(task => task.status === 'pending' && task.config)
        .map(task => task.config)
        .filter((config): config is TaskConfig => config !== undefined);

      if (pendingConfigs.length === 0) {
        await setStorageData(STORAGE_KEYS.TASKS, null);
        this._logger.info('待处理队列为空，存储已清空');
        return;
      }

      const jsonToEncrypt = JSON.stringify(pendingConfigs);
      const encryptedData = await this._securityService.encryptData(jsonToEncrypt);
      await setStorageData(STORAGE_KEYS.TASKS, encryptedData);
      
      this._logger.info(`已保存 ${pendingConfigs.length} 个待处理任务`);
    } catch (error) {
      this._logger.error('保存任务状态失败:', error);
    }
  }

  async restoreState(): Promise<TaskConfig[]> {
    try {
      const encryptedData = await getStorageData<string | null>(STORAGE_KEYS.TASKS, null);
      
      if (!encryptedData) {
        this._logger.info('未找到先前的任务状态');
        return [];
      }
      
      const decryptedJson = await this._securityService.decryptData(encryptedData);
      
      if (!decryptedJson) {
        this._logger.warn('解密任务队列失败');
        return [];
      }

      const restoredConfigs = JSON.parse(decryptedJson) as TaskConfig[];
      
      // 验证恢复的配置
      const validatedConfigs = restoredConfigs.filter(config => {
        if (!config || !Array.isArray(config.urls) || config.urls.length === 0) {
          this._logger.warn('发现无效的任务配置，已丢弃:', config);
          return false;
        }
        return true;
      });

      this._logger.info(`成功恢复 ${validatedConfigs.length} 个任务配置`);
      return validatedConfigs;
    } catch (error) {
      this._logger.error('恢复任务状态失败:', error);
      return [];
    }
  }

  async clearState(): Promise<void> {
    try {
      await setStorageData(STORAGE_KEYS.TASKS, null);
      this._taskMetrics$.next(new Map());
      this._logger.info('任务状态已清空');
    } catch (error) {
      this._logger.error('清空任务状态失败:', error);
    }
  }

  /**
   * 创建临时的待处理任务指标
   * 用于在任务实际开始之前显示在UI中
   */
  createPendingTaskMetrics(configs: TaskConfig[]): void {
    const currentMap = this._taskMetrics$.value;
    const newMap = new Map(currentMap);
    
    for (const config of configs) {
      if (config.batchId && config.urls && config.urls.length > 0) {
        for (const url of config.urls) {
          // 检查是否已存在相同的待处理任务
          const existingMetric = Array.from(newMap.values()).find(m =>
            m.status === 'pending' &&
            m.config &&
            m.config.batchId === config.batchId &&
            m.config.urls[0] === url
          );

          if (!existingMetric) {
            const tempId = `pending-${config.batchId}-${url}-${Math.random()}`;
            const singleUrlConfig: TaskConfig = {
              ...config,
              urls: [url],
            };

            const pendingMetrics: TaskMetrics = {
              id: tempId,
              batchId: config.batchId,
              status: 'pending',
              startTime: Date.now(),
              totalPages: 1,
              processedPages: 0,
              config: singleUrlConfig,
              endTime: undefined,
              cpuUsage: 0,
              memoryUsage: 0,
              networkUsage: 0,
              errors: []
            };

            newMap.set(tempId, pendingMetrics);
            this._logger.debug(`创建待处理任务指标: ${url}`);
          }
        }
      }
    }

    if (newMap.size > currentMap.size) {
      this._taskMetrics$.next(newMap);
      this._logger.info(`创建了 ${newMap.size - currentMap.size} 个待处理任务指标`);
    }
  }

  /**
   * 移除临时的待处理任务指标
   * 当任务实际开始时调用
   */
  removePendingTaskMetrics(batchId: string, url: string): void {
    const currentMap = this._taskMetrics$.value;
    const entryToRemove = Array.from(currentMap.entries()).find(
      ([, metric]) =>
        metric.status === 'pending' &&
        metric.config &&
        metric.config.batchId === batchId &&
        metric.config.urls[0] === url
    );

    if (entryToRemove) {
      const newMap = new Map(currentMap);
      newMap.delete(entryToRemove[0]);
      this._taskMetrics$.next(newMap);
      
      this._logger.debug(`移除待处理任务指标: ${url}`);
    }
  }

  /**
   * 清理已完成的批处理任务
   * 防止内存无限增长
   */
  pruneBatches(maxCompletedBatches: number): void {
    const currentMap = this._taskMetrics$.value;
    if (currentMap.size === 0) return;

    // 1. 按 batchId 对所有任务进行分组
    const batches = new Map<string, TaskMetrics[]>();
    for (const metric of currentMap.values()) {
      if (!batches.has(metric.batchId)) {
        batches.set(metric.batchId, []);
      }
      const batch = batches.get(metric.batchId);
      if (batch) {
        batch.push(metric);
      }
    }

    // 2. 识别完全完成的批处理任务
    const completedBatches: { batchId: string, completedAt: number }[] = [];
    for (const [batchId, tasks] of batches.entries()) {
      const isBatchComplete = tasks.every(t => ['completed', 'failed', 'cancelled'].includes(t.status));
      if (isBatchComplete) {
        const lastCompletedTime = Math.max(...tasks.map(t => t.endTime ?? 0));
        completedBatches.push({ batchId, completedAt: lastCompletedTime });
      }
    }

    // 3. 如果完成的批处理数超过限制，进行清理
    if (completedBatches.length > maxCompletedBatches) {
      this._logger.info(`已完成批处理数 (${completedBatches.length}) 超过限制 (${maxCompletedBatches})，开始清理...`);
      
      // 按完成时间升序排序
      completedBatches.sort((a, b) => a.completedAt - b.completedAt);
      
      const batchesToRemoveCount = completedBatches.length - maxCompletedBatches;
      const batchesToRemove = completedBatches.slice(0, batchesToRemoveCount);
      
      const newMap = new Map(currentMap);
      let tasksRemovedCount = 0;
      
      for (const batchToRemove of batchesToRemove) {
        const tasksInBatch = batches.get(batchToRemove.batchId) || [];
        for (const taskMetric of tasksInBatch) {
          newMap.delete(taskMetric.id);
          tasksRemovedCount++;
        }
        this._logger.debug(`已清理旧批处理任务: ${batchToRemove.batchId}`);
      }
      
      this._taskMetrics$.next(newMap);
      this._logger.info(`清理完成，移除了 ${batchesToRemove.length} 个旧批处理 (共 ${tasksRemovedCount} 个任务)`);
    }
  }

  /**
   * 根据批处理ID移除任务指标
   */
  removeTaskMetricsByBatchIds(batchIds: string[]): void {
    const currentMap = this._taskMetrics$.value;
    const newMap = new Map(currentMap);
    let removedCount = 0;

    for (const [taskId, metric] of currentMap.entries()) {
      if (batchIds.includes(metric.batchId)) {
        newMap.delete(taskId);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this._taskMetrics$.next(newMap);
      this._logger.info(`移除了 ${removedCount} 个任务指标 (批处理: ${batchIds.join(', ')})`);
    }
  }

  /**
   * 获取任务指标的内部Subject，用于其他服务直接操作
   * 注意：这是一个临时方法，理想情况下应该避免暴露内部状态
   */
  getInternalMetricsSubject(): BehaviorSubject<Map<TaskId, TaskMetrics>> {
    return this._taskMetrics$;
  }
}