# Browser-Use 架构文档

## 项目概述

Browser-Use 是一个基于 Python 的 AI 浏览器自动化库，版本 0.2.6，使用 MIT 开源协议。它允许 AI 智能体控制浏览器，实现网页自动化操作和信息采集。

## 核心组件架构

### 1. Agent (智能体) - `/browser_use/agent/`

**核心类：`Agent`**

- 位置：`browser_use/agent/service.py`
- 职责：智能体的核心逻辑，负责任务规划和执行
- 支持多种 LLM（OpenAI GPT、Claude、Gemini、DeepSeek 等）
- 具备记忆功能（需要 Python < 3.13）

**相关模块：**

- `prompts.py`：系统提示词和消息提示词管理
- `memory/`：智能体记忆服务
- `message_manager/`：消息管理和处理
- `views.py`：数据模型定义（ActionModel、ActionResult、AgentHistory 等）

### 2. Browser (浏览器会话) - `/browser_use/browser/`

**核心类：`BrowserSession`**

- 位置：`browser_use/browser/session.py`
- 职责：管理浏览器会话、标签页、上下文
- 支持 Playwright 和 Patchright 两种浏览器驱动
- 提供浏览器配置文件管理

**相关模块：**

- `browser.py`：浏览器实例抽象（别名映射）
- `profile.py`：浏览器配置文件管理
- `context.py`：浏览器上下文管理
- `views.py`：浏览器相关数据模型

### 3. Controller (控制器) - `/browser_use/controller/`

**核心类：`Controller`**

- 位置：`browser_use/controller/service.py`
- 职责：管理和执行所有浏览器动作
- 支持自定义输出模型
- 动作注册和调度机制

**Registry (注册器)：**

- 位置：`browser_use/controller/registry/service.py`
- 职责：动作注册、参数验证、函数签名标准化

**内置动作类型：**

- `SearchGoogleAction`：Google 搜索
- `GoToUrlAction`：导航到 URL
- `ClickElementAction`：点击元素
- `InputTextAction`：输入文本
- `ScrollAction`：滚动页面
- `DragDropAction`：拖拽操作
- `OpenTabAction`：打开新标签页
- `CloseTabAction`：关闭标签页
- `SwitchTabAction`：切换标签页
- `SendKeysAction`：发送键盘按键
- `DoneAction`：完成任务

### 4. DOM Service (DOM 处理) - `/browser_use/dom/`

**核心类：`DomService`**

- 位置：`browser_use/dom/service.py`
- 职责：DOM 解析、元素识别、可点击元素检测
- 使用 JavaScript 注入技术提取 DOM 信息

**相关模块：**

- `buildDomTree.js`：浏览器端 JavaScript 代码，用于构建 DOM 树
- `clickable_element_processor/`：可点击元素处理器
- `history_tree_processor/`：DOM 历史树处理器
- `views.py`：DOM 相关数据模型

### 5. 工具模块

**Telemetry (遥测)：**`/browser_use/telemetry/`

- 产品使用情况追踪
- 性能监控和分析

**异常处理：**`/browser_use/exceptions.py`

- 自定义异常类型
- LLM 相关异常处理

**CLI 工具：**`/browser_use/cli.py`

- 命令行交互界面
- 支持 rich、click、textual 库

## 基本使用方法

### 1. 简单使用示例

```python
import asyncio
from dotenv import load_dotenv
from browser_use import Agent
from langchain_openai import ChatOpenAI

load_dotenv()

async def main():
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=ChatOpenAI(model="gpt-4o"),
    )
    await agent.run()

asyncio.run(main())
```

### 2. 高级配置使用

```python
from browser_use import Agent, BrowserSession, BrowserProfile

# 配置浏览器会话
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        trace_path='./tmp/traces/',
        user_data_dir='~/.config/browseruse/profiles/default',
    )
)

# 使用自定义浏览器会话
agent = Agent(
    task="复杂的浏览器任务",
    llm=ChatOpenAI(model="gpt-4o"),
    browser_session=browser_session,
)
```

### 3. 自定义输出格式

```python
from pydantic import BaseModel
from browser_use import Agent, Controller

class Post(BaseModel):
    post_title: str
    post_url: str
    num_comments: int

class Posts(BaseModel):
    posts: list[Post]

controller = Controller(output_model=Posts)
agent = Agent(
    task="获取 Hacker News 前 5 篇文章",
    llm=ChatOpenAI(model="gpt-4o"),
    controller=controller
)
```

## 依赖关系

### 核心依赖

- `playwright>=1.52.0`：浏览器自动化
- `patchright>=1.52.5`：增强的 Playwright 版本
- `langchain>=0.3.25`：LLM 集成框架
- `pydantic>=2.11.5`：数据验证和设置管理
- `httpx>=0.28.1`：HTTP 客户端
- `python-dotenv>=1.0.1`：环境变量管理

### LLM 支持

- `langchain-openai`：OpenAI GPT 模型
- `langchain-anthropic`：Anthropic Claude 模型
- `langchain-google-genai`：Google Gemini 模型
- `langchain-deepseek`：DeepSeek 模型
- `langchain-ollama`：本地 Ollama 模型
- `langchain-aws`：AWS Bedrock 模型

### 可选依赖

- `sentence-transformers>=4.0.2`：记忆功能（需要 Python < 3.13）
- `rich>=14.0.0`：CLI 界面增强
- `textual>=3.2.0`：终端 UI 组件

## 高级特性

### 1. 多智能体协作

- 支持多个智能体共享同一浏览器会话
- 并行任务执行能力

### 2. 自定义动作

- 支持注册自定义动作函数
- 动作参数验证和类型检查

### 3. 会话管理

- 浏览器配置文件持久化
- Cookie 和会话状态保存
- 多标签页管理

### 4. 安全特性

- URL 域名限制功能
- 敏感数据处理
- 跨域 iframe 检测

### 5. 调试和追踪

- 操作历史记录
- 浏览器追踪文件
- GIF 录制功能
- 详细日志记录

## 项目结构说明

```
browser-use/
├── browser_use/           # 核心代码包
│   ├── agent/            # 智能体相关
│   ├── browser/          # 浏览器会话管理
│   ├── controller/       # 动作控制器
│   ├── dom/              # DOM 处理
│   ├── telemetry/        # 遥测数据
│   └── utils.py          # 工具函数
├── examples/             # 使用示例
│   ├── simple.py         # 基础示例
│   ├── use-cases/        # 实际应用案例
│   ├── features/         # 功能演示
│   └── models/           # 不同 LLM 模型示例
├── tests/                # 测试代码
└── docs/                 # 文档
```

## 性能特点

- **异步架构**：完全异步 Python 实现，支持高并发
- **智能 DOM 处理**：只提取可交互元素，减少 token 消耗
- **缓存机制**：XPath 缓存和元素状态缓存
- **自动重试**：连接断开自动恢复机制
- **资源优化**：自动垃圾回收和内存管理

这个架构文档为在项目中集成和使用 browser-use 提供了完整的技术参考。
