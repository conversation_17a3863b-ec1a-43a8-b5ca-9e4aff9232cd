/**
 * Service Worker - 后台服务入口
 */

import { BehaviorEngine, BehaviorEngineConfig } from '@/core/behavior-engine/BehaviorEngine';
import { TaskScheduler } from '@/core/task-manager/interfaces';
import { createTaskScheduler } from '@/core/task-manager/TaskSchedulerFactory';
import { initializeSharedWorkerPool } from '@/services/pool/SharedWorkerPool';
import { PrivacyService } from '@/services/privacy-service/PrivacyService';
import { SecurityService } from '@/services/security-service/SecurityService';
import { MESSAGE_TYPES, STORAGE_KEYS } from '@/shared/constants';
import { AgentConfig, BatchTask, ImageSegment, ServiceMessage, ServiceResponse, TaskConfig, TaskId, TaskMetrics, TaskStatus } from '@/shared/types';
import { compressImage, createLogger, getStorageData, Logger } from '@/shared/utils';
import { dataUrlToBuffer } from '@/shared/utils/pure-utils';
import 'webextension-polyfill';
import { DownloadService } from '../services/download-service/DownloadService';
import { offscreenManager } from './OffscreenManager';
import { WorkerWindowManager } from './WorkerWindowManager';

// 全局常量，用于模式切换。在vite.config.ts中可以被替换。
declare const __DEV_MODE__: boolean;
const DEV_MODE = typeof __DEV_MODE__ !== 'undefined' ? __DEV_MODE__ : true;

class ViewerServiceWorker {
  private readonly _logger: Logger = createLogger('ServiceWorker');

  // 核心服务
  private _taskScheduler!: TaskScheduler;
  private _behaviorEngine!: BehaviorEngine;
  private _workerWindowManager!: WorkerWindowManager;
  
  // 隐私与安全
  private _securityService!: SecurityService;
  private _privacyService!: PrivacyService;
  private _downloadService!: DownloadService;
  
  // 服务状态
  private _isInitialized = false;
  private _isInitializing = false; // 初始化锁
  private _isShuttingDown = false;
  private _popupPort: chrome.runtime.Port | null = null;
  private _activeTaskCount = 0;
  
  // 事件类型分类
  private static readonly _FIRE_AND_FORGET_MESSAGES = [
    MESSAGE_TYPES.CONTENT_SCRIPT_READY,
    MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED,
    MESSAGE_TYPES.STRUCTURED_DATA_CAPTURED,
  ];

  private static readonly _TASK_LIFECYCYCLE_MESSAGE_TYPES = [
    MESSAGE_TYPES.TASK_CREATE,
    MESSAGE_TYPES.TASK_PAUSE,
    MESSAGE_TYPES.TASK_RESUME,
    MESSAGE_TYPES.TASK_CANCEL,
    MESSAGE_TYPES.TASK_STATUS,
  ] as const;
  private static readonly _CAPTURE_AND_CONTENT_MESSAGE_TYPES = [
    MESSAGE_TYPES.CAPTURE_AND_PROCESS_VIEWPORT,
    MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED,
    MESSAGE_TYPES.CAPTURE_COMPLETED,
    MESSAGE_TYPES.CAPTURE_FAILED,
    MESSAGE_TYPES.CONTENT_SCRIPT_READY,
    MESSAGE_TYPES.STRUCTURED_DATA_CAPTURED,
  ] as const;
  private static readonly _DEVTOOLS_MESSAGE_TYPES = [
    MESSAGE_TYPES.GET_TASKS_STATUS,
    MESSAGE_TYPES.CREATE_DEV_TASK,
    MESSAGE_TYPES.START_ALL_TASKS,
    MESSAGE_TYPES.PAUSE_ALL_TASKS,
    MESSAGE_TYPES.CLEAR_ALL_TASKS,
    MESSAGE_TYPES.START_SELECTED_TASKS,
    MESSAGE_TYPES.PAUSE_SELECTED_TASKS,
    MESSAGE_TYPES.CLEAR_SELECTED_TASKS,
    MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS,
    MESSAGE_TYPES.GET_TASK_METRICS,
  ] as const;
  
  constructor() {
    // 确保在合适的生命周期事件中初始化
    chrome.runtime.onInstalled.addListener(() => { 
      this._logger.debug('onInstalled triggered');
      void this._safeInitialize(); 
    });
    chrome.runtime.onStartup.addListener(() => { 
      this._logger.debug('onStartup triggered');
      void this._safeInitialize(); 
    });
    
    // 监听来自弹出窗口的长连接
    chrome.runtime.onConnect.addListener(port => {
      if (port.name === 'popup') {
        this._popupPort = port;
        this._logger.info('Popup connected.');

        // 处理来自弹出窗口的消息
        port.onMessage.addListener((message: ServiceMessage<unknown>) => {
          // 封装 sendResponse 以通过端口发送回执
          const sendResponse = (response: ServiceResponse) => {
            if (this._popupPort) {
              this._popupPort.postMessage(response);
            }
          };
          void this._handleMessage(message, undefined, sendResponse);
        });

        // 连接建立后，立即发送一次当前状态
        if (this._isInitialized && this._taskScheduler) {
          this._broadcastTasksUpdate();
          this._broadcastSystemStatus();
        }

        // 处理断开连接
        port.onDisconnect.addListener(() => {
          this._popupPort = null;
          this._logger.info('Popup disconnected.');
        });
      }
    });

    // 为其他一次性消息保留onMessage监听器（例如来自内容脚本）
    chrome.runtime.onMessage.addListener((message: ServiceMessage<unknown>, sender, sendResponse) => {
      // 如果消息来自一个长连接端口，则忽略，因为它已由 onConnect 处理
      if (sender && 'port' in sender && sender.port) {
        return;
      }

      // 对于"即发即忘"的消息，我们不返回true，因为我们不会调用sendResponse
      if (ViewerServiceWorker._FIRE_AND_FORGET_MESSAGES.includes(message.type as (typeof ViewerServiceWorker._FIRE_AND_FORGET_MESSAGES)[number])) {
        void this._handleMessage(message, sender, sendResponse);
        return; // 返回 undefined/false
      }
      
      // 对于所有其他消息，我们都承诺会异步响应
      void this._handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道用于异步响应
    });

    // 将关闭逻辑也封装在构造函数中
    self.addEventListener('beforeunload', () => {
      void this.shutdown();
    });
  }
  
  /**
   * 安全的初始化方法，防止竞态条件
   */
  private async _safeInitialize(): Promise<void> {
    if (this._isInitialized || this._isInitializing) {
      return;
    }
    
    this._isInitializing = true;
    try {
      await this.initialize();
    } catch (error) {
      this._logger.error('Failed to initialize service worker:', error);
    } finally {
      this._isInitializing = false;
    }
  }
  
  async initialize(): Promise<void> {
    this._logger.info('用户浏览代理服务工作进程正在连接...');
    
    try {
      const config = await this._loadConfig();
      
      initializeSharedWorkerPool();

      this._securityService = SecurityService.getInstance();
      await this._securityService.initialize();

      this._privacyService = PrivacyService.getInstance()
      await this._privacyService.getPrivacySettings(); // 缓存隐私设置

      this._downloadService = DownloadService.getInstance();

      this._workerWindowManager = WorkerWindowManager.getInstance();
      this._workerWindowManager.initialize();

      this._taskScheduler = createTaskScheduler(this._workerWindowManager);
      await this._taskScheduler.initializeScheduler();

      this._behaviorEngine = new BehaviorEngine(config.behaviorProfiles.normal as Partial<BehaviorEngineConfig>);
      this._behaviorEngine.start();
      
      // 订阅任务数量变化来管理音频播放
      setInterval(() => {
        this._updateAudioBasedOnTaskState();
      }, 2000); // Poll every 2 seconds
      
      // 只在开发模式下，定期轮询任务状态并广播给UI
      if (DEV_MODE) {
        setInterval(() => {
          this._broadcastTasksUpdate();
        }, 1500); // Poll every 1.5 seconds
      }
      
      this._isInitialized = true;
      this._logger.info('Agent Service Worker initialized successfully');
      
    } catch (error) {
      this._logger.error('Failed to initialize Service Worker:', error);
      this._isInitialized = false;
      throw error; // 重新抛出错误，确保调用者知道初始化失败
    }
  }

  private async _loadConfig(): Promise<AgentConfig> {
    const defaultConfig: AgentConfig = {
      maxConcurrentTasks: 3,
      resourceThresholds: { CPU: 0.7, MEMORY: 1024, NETWORK: 5, FPS: 30 },
      behaviorProfiles: {
        stealth: { scrollSpeed: 0.5, preClickDelay: { min: 1500, max: 2500 }, postActionDelay: { min: 1500, max: 2500 }, scrollPause: {min: 800, max: 1500}, randomness: 0.8, distractionRate: 0.3 },
        normal: { scrollSpeed: 1.0, preClickDelay: { min: 800, max: 1200 }, postActionDelay: { min: 800, max: 1200 }, scrollPause: {min: 400, max: 800}, randomness: 0.5, distractionRate: 0.2 },
        aggressive: { scrollSpeed: 2.0, preClickDelay: { min: 300, max: 700 }, postActionDelay: { min: 300, max: 700 }, scrollPause: {min: 100, max: 300}, randomness: 0.2, distractionRate: 0.1 }
      },
      captureOptions: { quality: 80, blockSize: 1024, overlap: 50, format: 'jpeg' },
      permissions: { domains: [], cookies: true, localStorage: true, screenshots: true, automation: true },
      ai: { provider: 'default', model: 'default' }
    };
    return getStorageData(STORAGE_KEYS.CONFIG, defaultConfig);
  }

  private _updateAudioBasedOnTaskState(): void {
    const runningTasks = this._taskScheduler.getRunningTasksCount();
    
    this._logger.debug(`Updating audio state. Previously active: ${this._activeTaskCount}, now active: ${runningTasks}`);

    if (this._activeTaskCount === 0 && runningTasks > 0) {
        this._logger.info('First task started, starting audio playback to prevent throttling.');
        void offscreenManager.startAudio();
    } else if (this._activeTaskCount > 0 && runningTasks === 0) {
        this._logger.info('Last task finished, stopping audio playback.');
        void offscreenManager.stopAudio();
    }

    this._activeTaskCount = runningTasks;
  }

  private async _handleMessage(
    message: ServiceMessage<unknown>,
    sender: chrome.runtime.MessageSender | undefined,
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    try {
      if (!this._isInitialized) {
        await this._safeInitialize();
      }

      this._logger.debug('Handling service worker message:', message.type, 'from sender:', sender);
      const type = message.type;

      if (this._isTaskLifecycleMessage(type)) {
        await this._handleTaskLifecycleMessage(message, sendResponse);
      } else if (this._isCaptureAndContentMessage(type)) {
        await this._handleCaptureAndContentMessage(message, sender, sendResponse);
      } else if (this._isDevtoolsMessage(type)) {
        await this._handleDevtoolsMessage(message, sendResponse);
      } else if (type === MESSAGE_TYPES.PING) {
        this._handlePing(message, sendResponse);
      } else if (type === MESSAGE_TYPES.FETCH_RESOURCE_AS_DATA_URL) {
        await this._handleFetchResourceAsDataUrl(message as ServiceMessage<{ url: string, referer?: string }>, sendResponse);
      } else {
        this._logger.warn(`Unhandled message type in service worker: ${type}`);
        // 对未处理的消息发送一个标准的错误响应
        sendResponse(this._createServiceResponse(false, null, `Unhandled message type: ${type}`));
      }
    } catch (error) {
      this._logger.error(`Critical error handling message ${message.type}:`, error);
      sendResponse(this._createServiceResponse(false, null, error instanceof Error ? error.message : 'A critical unhandled error occurred.'));
    }
  }
  
  private _isTaskLifecycleMessage(type: string): type is typeof ViewerServiceWorker._TASK_LIFECYCYCLE_MESSAGE_TYPES[number] {
    return (ViewerServiceWorker._TASK_LIFECYCYCLE_MESSAGE_TYPES as readonly string[]).includes(type);
  }

  private _isCaptureAndContentMessage(type: string): type is typeof ViewerServiceWorker._CAPTURE_AND_CONTENT_MESSAGE_TYPES[number] {
    return (ViewerServiceWorker._CAPTURE_AND_CONTENT_MESSAGE_TYPES as readonly string[]).includes(type);
  }

  private _isDevtoolsMessage(type: string): type is typeof ViewerServiceWorker._DEVTOOLS_MESSAGE_TYPES[number] {
    return (ViewerServiceWorker._DEVTOOLS_MESSAGE_TYPES as readonly string[]).includes(type);
  }


  private async _handleTaskLifecycleMessage(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    const { type, payload } = message;
    switch (type) {
      case MESSAGE_TYPES.TASK_CREATE:
        this._handleCreateTask(payload as { config: TaskConfig }, sendResponse);
        break;
      case MESSAGE_TYPES.TASK_PAUSE:
        await this._handlePauseTask(payload as { taskId: TaskId }, sendResponse);
        break;
      case MESSAGE_TYPES.TASK_RESUME:
        await this._handleResumeTask(payload as { taskId: TaskId }, sendResponse);
        break;
      case MESSAGE_TYPES.TASK_CANCEL:
        this._handleCancelTask(payload as { taskId: TaskId }, sendResponse);
        break;
      case MESSAGE_TYPES.TASK_STATUS:
        this._handleTaskStatus(message, sendResponse);
        break;
    }
  }

  private async _handleCaptureAndContentMessage(
    message: ServiceMessage<unknown>,
    sender: chrome.runtime.MessageSender | undefined,
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    const { type } = message;
    switch (type) {
      case MESSAGE_TYPES.CAPTURE_AND_PROCESS_VIEWPORT:
        await this._handleCaptureAndProcessViewport(message, sendResponse);
        break;
      case MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED:
        this._handleFullPageScanCompleted(message);
        break;
      case MESSAGE_TYPES.STRUCTURED_DATA_CAPTURED:
        this._handleStructuredDataCaptured(message);
        break;
      case MESSAGE_TYPES.CAPTURE_COMPLETED:
        await this._handleCaptureCompleted(message, sendResponse);
        break;
      case MESSAGE_TYPES.CAPTURE_FAILED:
        this._handleCaptureFailed(message, sendResponse);
        break;
      case MESSAGE_TYPES.CONTENT_SCRIPT_READY:
        if (sender?.tab?.id) {
          const task = this._taskScheduler.getTaskByTabId(sender.tab.id);
          if (task) {
            task.handleContentScriptReady();
          } else {
            this._logger.debug(`Received CONTENT_SCRIPT_READY from a tab (${sender.tab.id}) not associated with any active task.`);
          }
        }
        break;
    }
  }

  private async _handleDevtoolsMessage(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    if (!DEV_MODE) {
      this._logger.warn('A devtools message was received in production mode and was ignored.');
      return;
    }

    const { type, payload } = message;
    switch (type) {
      case MESSAGE_TYPES.GET_TASKS_STATUS:
        this._broadcastTasksUpdate();
        break;
      case MESSAGE_TYPES.CREATE_DEV_TASK: {
        this._taskScheduler.submitBatch([payload as TaskConfig]);
        this._broadcastTasksUpdate();
        sendResponse({
          id: message.id,
          success: true,
          data: { message: "Task batch submitted." },
          timestamp: Date.now(),
        });
        break;
      }
      case MESSAGE_TYPES.START_ALL_TASKS:
        await this._handleAsyncResponse(this._taskScheduler.startAllPendingTasks(), message, sendResponse, 'A scheduling cycle has been manually triggered for all pending tasks.');
        break;
      case MESSAGE_TYPES.PAUSE_ALL_TASKS:
        await this._handleAsyncResponse(this._taskScheduler.pauseAllTasks(), message, sendResponse, 'Paused all tasks');
        break;
      case MESSAGE_TYPES.CLEAR_ALL_TASKS:
        await this._handleAsyncResponse(this._taskScheduler.clearAllTasks(), message, sendResponse, 'Cleared all non-running tasks');
        this._broadcastTasksUpdate();
        break;
      case MESSAGE_TYPES.START_SELECTED_TASKS: {
        const { batchIds } = payload as { batchIds: string[] };
        if (batchIds && batchIds.length > 0) {
          await this._handleAsyncResponse(
            this._taskScheduler.startSelectedTasks(batchIds),
            message,
            sendResponse,
            `Triggered start for ${batchIds.length} selected batches.`
          );
        } else {
          sendResponse(this._createServiceResponse(false, null, 'No batchIds provided for START_SELECTED_TASKS.'));
        }
        break;
      }
      case MESSAGE_TYPES.PAUSE_SELECTED_TASKS: {
        const { batchIds } = payload as { batchIds: string[] };
        await this._handleAsyncResponse(this._taskScheduler.pauseSelectedTasks(batchIds), message, sendResponse, `Paused tasks in selected batches.`);
        break;
      }
      case MESSAGE_TYPES.CLEAR_SELECTED_TASKS: {
        const { batchIds } = payload as { batchIds: string[] };
        await this._handleAsyncResponse(this._taskScheduler.clearSelectedTasks(batchIds), message, sendResponse, `Cleared selected non-running tasks`);
        this._broadcastTasksUpdate();
        break;
      }
      case MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS: {
        const { taskId, url } = payload as { taskId: TaskId; url: string };
        await this._handleAsyncResponse(this._handleDownloadTaskAssets({ taskId, url }), message, sendResponse, `Asset download initiated for task ${taskId}`);
        break;
      }
      case MESSAGE_TYPES.GET_TASK_METRICS: {
        const { taskId } = payload as { taskId: TaskId };
        const metrics = this._taskScheduler.getTaskMetrics(taskId);
        if (metrics) {
          sendResponse({ id: message.id, success: true, data: metrics, timestamp: Date.now() });
        } else {
          sendResponse({
            id: message.id,
            success: false,
            error: `Task with ID ${taskId} not found.`,
            timestamp: Date.now(),
          });
        }
        break;
      }
    }
  }

  private async _handleAsyncResponse(
    promise: Promise<unknown>,
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void,
    successMsg?: string
  ): Promise<void> {
    try {
      const result = await promise;
      this._logger.info(successMsg ?? `Action for ${message.type} completed successfully.`);
      sendResponse({ id: message.id, success: true, data: result, timestamp: Date.now() });
    } catch (error) {
      this._logger.error(`Error processing message ${message.type}:`, error);
      sendResponse({
        id: message.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
      });
    }
  }

  private _handlePing(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): void {
    sendResponse({ id: message.id, success: true, timestamp: Date.now() });
  }

  private _handleFullPageScanCompleted(message: ServiceMessage<unknown>): void {
    const { taskId, sessionId, result } = message.payload as { 
      taskId: TaskId; 
      sessionId: string; 
      result: { success: boolean; data?: string; error?: string }
    };

    const task = this._taskScheduler.getTask(taskId);
    if (task) {
      this._logger.info(`Forwarding FULL_PAGE_SCAN_COMPLETED for session ${sessionId} to task ${taskId}`);
      task.handleFullPageScanCompleted(sessionId, result);
    } else {
      this._logger.warn(`Task ${taskId} not found for FULL_PAGE_SCAN_COMPLETED.`);
    }
  }

  private _handleCaptureFailed(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): void {
    const { taskId, sessionId, error } = message.payload as { taskId: TaskId; sessionId: string; error: string };
    this._logger.error(`Capture failed in content script for task ${taskId}, session ${sessionId}:`, error);
    const task = this._taskScheduler.getTask(taskId);
    if (task && sessionId) {
      task.handleCaptureFailed(sessionId, error);
    }
    sendResponse({ id: message.id, success: true, timestamp: Date.now() });
  }
  
  private _broadcastTasksUpdate(): void {
    if (!this._popupPort) {
      this._logger.debug('No popup port available to broadcast task updates.');
      return;
    }

    const allMetrics = this._taskScheduler.getAllTasks();
    const batchTasks = this._aggregateMetricsToBatches(allMetrics);
    
    this._popupPort.postMessage({
      type: MESSAGE_TYPES.TASKS_UPDATED,
      payload: batchTasks
    });
  }

  private _broadcastSystemStatus(): void {
    if (!this._popupPort) return;
    this._popupPort.postMessage({
      type: MESSAGE_TYPES.SYSTEM_STATUS,
      payload: {
        connected: true, // 如果能发送消息，说明是连接的
        isInitialized: this._isInitialized,
      }
    });
  }

  private _aggregateMetricsToBatches(metrics: TaskMetrics[]): BatchTask[] {
    if (!metrics || metrics.length === 0) return [];

    // 预过滤，确保所有处理的指标都有 batchId 和 config
    const validMetrics = metrics.filter((m): m is TaskMetrics & { config: TaskConfig } => !!(m.batchId && m.config));

    const batches = new Map<string, (TaskMetrics & { config: TaskConfig })[]>();
    for (const metric of validMetrics) {
      const batchId = metric.batchId;
      let arr = batches.get(batchId);
      if (!arr) {
        arr = [];
        batches.set(batchId, arr);
      }
      arr.push(metric);
    }

    const result: BatchTask[] = [];
    const mapTaskStatusToUrlListStatus = (status: TaskStatus): 'succeeded' | 'failed' | 'pending' | 'running' => {
      switch (status) {
        case 'completed':
          return 'succeeded';
        case 'running':
          return 'running';
        case 'pending':
        case 'paused':
          return 'pending';
        case 'failed':
        case 'cancelled':
          return 'failed';
        default:
          // 处理未知状态
          if (this._logger && typeof this._logger.warn === 'function') {
            this._logger.warn(`Unhandled task status: ${String(status)}, defaulting to 'failed'.`);
          }
          return 'failed';
      }
    };

    for (const tasks of batches.values()) {
      const representativeTask = tasks[0];
      const config = representativeTask.config;
      if (!config) continue;

      const succeeded = tasks.filter(t => t.status === 'completed').length;
      const failed = tasks.filter(t => t.status === 'failed' || t.status === 'cancelled').length;

      let batchStatus: TaskStatus = 'pending';
      if (tasks.some(t => t.status === 'running')) {
        batchStatus = 'running';
      } else if (tasks.some(t => t.status === 'paused')) {
        batchStatus = 'paused';
      } else if (succeeded + failed === tasks.length) {
        batchStatus = failed > 0 ? 'failed' : 'completed';
      }

      const allUrlsInBatch = Array.from(new Set(tasks.map(t => t.config.urls[0])));

      result.push({
        batchId: representativeTask.batchId,
        status: batchStatus,
        config: { ...config, urls: allUrlsInBatch },
        progress: {
          total: tasks.length,
          processed: succeeded + failed,
          succeeded: succeeded,
          failed: failed,
        },
        urlStatuses: tasks.map(t => ({
          url: t.config.urls[0],
          status: mapTaskStatusToUrlListStatus(t.status),
          taskId: t.id,
        })),
        createdAt: Math.min(...tasks.map(t => t.startTime)),
        completedAt: (succeeded + failed === tasks.length) ? Math.max(...tasks.map(t => t.endTime ?? 0)) : undefined
      });
    }

    result.sort((a, b) => b.createdAt - a.createdAt);
    return result;
  }

  private _handleCreateTask(data: { config: TaskConfig }, sendResponse: (response: ServiceResponse) => void): void {
    this._taskScheduler.submitBatch([data.config]);
    sendResponse({ id: 'N/A', success: true, data: { message: "Task submitted to batch." }, timestamp: Date.now() });
  }

  private async _handlePauseTask(payload: { taskId: TaskId }, sendResponse: (response: ServiceResponse) => void): Promise<void> {
    await this._taskScheduler.pauseTask(payload.taskId);
    sendResponse({ id: 'N/A', success: true, timestamp: Date.now() });
  }
  
  private async _handleResumeTask(payload: { taskId: TaskId }, sendResponse: (response: ServiceResponse) => void): Promise<void> {
      await this._taskScheduler.resumeTask(payload.taskId);
      sendResponse({id: 'TASK_RESUME_RESPONSE', success: true, data: { status: 'resumed' }, timestamp: Date.now() });
  }

  private _handleCancelTask(payload: { taskId: TaskId }, sendResponse: (response: ServiceResponse) => void): void {
      this._taskScheduler.cancelTask(payload.taskId);
      sendResponse({id: 'TASK_CANCEL_RESPONSE', success: true, data: { status: 'cancelled' }, timestamp: Date.now() });
  }
  
  private _handleTaskStatus(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): void {
    const statusPayload = message.payload as { taskId: TaskId };
    const status = this._taskScheduler.getTaskMetrics(statusPayload.taskId);
    sendResponse({ id: message.id, success: true, data: status, timestamp: Date.now() });
  }

  private async _handleDownloadTaskAssets(payload: { taskId: TaskId; url: string }): Promise<void> {
    this._logger.info(`Forwarding asset download request for task ${payload.taskId}`);
    const task = this._taskScheduler.getTask(payload.taskId);
    if (!task) {
      this._logger.error(`[ServiceWorker] Task ${payload.taskId} not found for download.`);
      throw new Error(`Task ${payload.taskId} not found.`);
    }
    await this._downloadService.downloadTaskAssetsAsZip(task, payload.url);
  }
  
  public isRunning(): boolean {
    return this._taskScheduler.getRunningTasksCount() > 0;
  }
  
  async shutdown(): Promise<void> {
    if (this._isShuttingDown) {
      return;
    }
    this._logger.info('Shutting down Agent Service Worker...');
    this._isShuttingDown = true;
    
    try {
      if (this._taskScheduler) {
        await this._taskScheduler.shutdown();
      }
      
      this._behaviorEngine.stop();
      await offscreenManager.stopAudio();
      
      await this._workerWindowManager.cleanupGroup();
      
    } catch(error) {
        this._logger.error('Error during service worker shutdown:', error);
    } finally {
      this._isInitialized = false;
      this._logger.info('Service Worker shut down successfully.');
    }
  }

  private async _handleCaptureAndProcessViewport(
    message: ServiceMessage<unknown>,
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    // payload判空和类型校验
    type CaptureViewportPayload = {
      taskId: string;
      sessionId: string;
      imageDataUrl: string;
      y: number;
      height: number;
      width: number;
    };
    const payload = message.payload as CaptureViewportPayload;
    if (
      !payload ||
      typeof payload.taskId !== 'string' ||
      typeof payload.sessionId !== 'string' ||
      typeof payload.imageDataUrl !== 'string' ||
      typeof payload.y !== 'number' ||
      typeof payload.height !== 'number' ||
      typeof payload.width !== 'number'
    ) {
      this._logger.error(
        '[ServiceWorker] _handleCaptureAndProcessViewport received invalid payload:',
        message.payload
      );
      sendResponse(this._createServiceResponse(false, null, 'Invalid payload for CAPTURE_AND_PROCESS_VIEWPORT'));
      return;
    }

    const { taskId, sessionId, imageDataUrl, y, height, width } = payload;

    const task = this._taskScheduler.getTask(taskId);
    if (!task) {
      this._logger.warn(`Task ${taskId} not found for CAPTURE_AND_PROCESS_VIEWPORT.`);
      sendResponse({ id: message.id, success: false, error: 'Task not found', timestamp: Date.now() });
      return;
    }

    // 确保imageDataUrl存在
    if (typeof imageDataUrl !== 'string' || imageDataUrl.length === 0) {
      this._logger.error(`Invalid or missing imageDataUrl for CAPTURE_AND_PROCESS_VIEWPORT, task ${taskId}, session ${sessionId}`);
      sendResponse({ id: message.id, success: false, error: 'Invalid or missing imageDataUrl', timestamp: Date.now() });
      return;
    }

    try {
      this._logger.debug(`Received viewport capture for task ${taskId}, session ${sessionId}. DataURL length: ${imageDataUrl.length}. Compressing...`);

      // 在 Service Worker 中执行图片压缩
      const compressedImage = await compressImage(imageDataUrl, {
        scale: 0.8,
        quality: 0.85,
        format: 'image/jpeg',
      });
      this._logger.debug(`Image compressed for task ${taskId}. New dataUrl length: ${compressedImage.dataUrl.length}`);

      // 从压缩后的 dataUrl 字符串重建 ArrayBuffer
      const imageData = dataUrlToBuffer(compressedImage.dataUrl);

      // 验证sessionId不为空
      if (!sessionId) {
        this._logger.error(`Empty sessionId for task ${taskId}`);
        sendResponse({ id: message.id, success: false, error: 'Empty sessionId', timestamp: Date.now() });
        return;
      }

      // 验证imageData
      if (!imageData || imageData.byteLength === 0) {
        this._logger.error(`Invalid imageData for task ${taskId}, session ${sessionId}`);
        sendResponse({ id: message.id, success: false, error: 'Invalid imageData', timestamp: Date.now() });
        return;
      }

      this._logger.debug(`Processed image data for task ${taskId}. Byte length: ${imageData.byteLength}`);

      const imageSegment: ImageSegment = {
        id: `seg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        data: imageData,
        position: { x: 0, y: y, width: width, height: height },
        timestamp: Date.now(),
        mimeType: compressedImage.mimeType as "image/jpeg" | "image/png" | "image/webp",
      };

      await task.handleViewportCapture(sessionId, imageSegment);
      sendResponse({ id: message.id, success: true, timestamp: Date.now() });
    } catch (error) {
      this._logger.error(`Error handling viewport capture for task ${taskId}:`, error);
      sendResponse({
        id: message.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      });
    }
  }

  private async _handleCaptureCompleted(
    message: ServiceMessage<unknown>, 
    sendResponse: (response: ServiceResponse) => void
  ): Promise<void> {
    const { taskId, sessionId, imageData, y, height } = message.payload as { 
      taskId: TaskId; 
      sessionId: string; 
      imageData: ArrayBuffer; 
      y: number; 
      height: number;
    };
    
    const task = this._taskScheduler.getTask(taskId);
    if (!task) {
      this._logger.warn(`Task ${taskId} not found for CAPTURE_COMPLETED.`);
      sendResponse({ id: message.id, success: false, error: 'Task not found', timestamp: Date.now() });
      return;
    }

    try {
      this._logger.debug(`Received capture completed for task ${taskId}, session ${sessionId}. Image byte length: ${imageData?.byteLength ?? 0}, y: ${y}`);
      // 验证sessionId和imageData
      if (!sessionId) {
        this._logger.error(`Empty sessionId for CAPTURE_COMPLETED task ${taskId}`);
        sendResponse({ id: message.id, success: false, error: 'Empty sessionId', timestamp: Date.now() });
        return;
      }
      
      if (!imageData || imageData.byteLength === 0) {
        this._logger.error(`Invalid imageData for CAPTURE_COMPLETED task ${taskId}, session ${sessionId}`);
        sendResponse({ id: message.id, success: false, error: 'Invalid imageData', timestamp: Date.now() });
        return;
      }
      
      // 构造 ImageSegment 对象
      const imageSegment: ImageSegment = {
        id: `seg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        data: imageData,
        position: { x: 0, y: y, width: -1, height: height }, // width will be set later
        timestamp: Date.now(),
        mimeType: 'image/jpeg'
      };
      
      await task.handleCaptureCompleted(sessionId, imageSegment);
      sendResponse({ id: message.id, success: true, timestamp: Date.now() });
    } catch (error) {
      this._logger.error(`Error handling capture completed for task ${taskId}:`, error);
      sendResponse({ 
        id: message.id, 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now() 
      });
    }
  }

  /**
   * 创建一个标准的服务响应对象。
   * @param success 操作是否成功
   * @param data 成功时返回的数据
   * @param error 失败时返回的错误信息
   */
  private _createServiceResponse<T>(success: boolean, data?: T, error?: string): ServiceResponse<T> {
    return {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      success,
      ...(success ? { data } : { error: error ?? 'An unknown error occurred' }),
    };
  }
  
  private async _handleFetchResourceAsDataUrl(
    message: ServiceMessage<{ url: string, referer?: string }>,
    sendResponse: (response: ServiceResponse<string>) => void
  ): Promise<void> {
    const { url, referer } = message.payload ?? {};
    if (!url) {
      sendResponse(this._createServiceResponse<string>(false, undefined, 'URL is missing in the payload.'));
      return;
    }

    // 在 fetch 前验证 URL。以'/'结尾的URL可能是目录而非有效图片资源。
    // 这类URL可能源于目标网站自身的错误<img> src，跳过它们是正确的健壮性策略。
    if (url.endsWith('/')) {
      this._logger.debug(`Skipping fetch for likely invalid image URL (directory): ${url}`);
      sendResponse(this._createServiceResponse<string>(false, undefined, `Skipped invalid URL: ${url}`));
      return;
    }

    this._logger.debug(`Fetching resource for data URL conversion: ${url}`);
    
    try {
      const requestHeaders = new Headers();
      if (referer) {
        requestHeaders.append('Referer', referer);
      }
      // 添加一个更像浏览器的Accept头
      requestHeaders.append('Accept', 'image/avif,image/webp,image/apng,image/*,*/*;q=0.8');

      // 隐私检查：决定是否应携带Cookie
      const resourceUrl = new URL(url);
      const isCookieDenied = await this._privacyService.isDomainCookieDenied(resourceUrl.hostname);
      
      this._logger.debug(`Cookie privacy check for ${resourceUrl.hostname}: denied=${isCookieDenied}`);

      const response = await fetch(url, {
        credentials: isCookieDenied ? 'omit' : 'include', // 根据隐私设置动态决定
        headers: requestHeaders,
      });

      if (!response.ok) {
        throw new Error(`Fetch failed with status: ${response.status} ${response.statusText}`);
      }
      const blob = await response.blob();
      
      const reader = new FileReader();
      reader.onload = () => {
        sendResponse(this._createServiceResponse(true, reader.result as string));
      };
      reader.onerror = () => {
        sendResponse(this._createServiceResponse<string>(false, undefined, `FileReader error: ${reader.error?.message}`));
      };
      reader.readAsDataURL(blob);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this._logger.warn(`Failed to fetch resource ${url}:`, errorMessage);
      sendResponse(this._createServiceResponse<string>(false, undefined, errorMessage));
    }
  }

  private _handleStructuredDataCaptured(message: ServiceMessage<unknown>): void {
    const { taskId, sessionId, result } = message.payload as {
      taskId: TaskId;
      sessionId: string;
      result: { success: boolean; data?: unknown; error?: string };
    };

    const task = this._taskScheduler.getTask(taskId);
    if (task) {
      this._logger.info(`Forwarding STRUCTURED_DATA_CAPTURED for session ${sessionId} to task ${taskId}`);

      // The data from structured capture can be an object, but the existing handler expects a string.
      // We'll stringify it if it's not already a string.
      const resultForTask = {
        ...result,
        data: typeof result.data === 'string' ? result.data : JSON.stringify(result.data),
      };

      // 复用现有的处理逻辑，因为它处理的是扫描/提取任务的完成信号
      task.handleFullPageScanCompleted(sessionId, resultForTask);
    } else {
      this._logger.warn(`Task ${taskId} not found for STRUCTURED_DATA_CAPTURED.`);
    }
  }
}

// Service Worker 启动和生命周期管理
// 只需实例化一次。构造函数中的监听器会自动处理安装、启动和关闭事件。
new ViewerServiceWorker();