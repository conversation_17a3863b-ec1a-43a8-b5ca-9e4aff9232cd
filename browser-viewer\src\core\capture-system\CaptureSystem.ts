/**
 * 视觉处理系统 - 负责页面的视觉捕捉和分析
 */

import { PrivacyService } from '@/services/privacy-service';
import {
  DataAccessCategory,
  type ContentSegment,
  type ImageSegment,
  type TabId,
  type TaskId,
} from '@/shared/types';
import { createLogger, generateId, Logger } from '@/shared/utils';
import { ImageProcessingPipeline } from './ImageProcessingPipeline';

interface SessionCompletion {
  promise: Promise<string>;
  resolve: (value: string | PromiseLike<string>) => void;
  reject: (reason?: unknown) => void;
}

interface ExtendedCaptureSession {
  sessionId: string;
  taskId: TaskId;
  tabId: TabId;
  status: 'running' | 'completed' | 'failed' | 'processing';
  imageSlices: ImageSegment[];
  contentSegments: ContentSegment[];
  scanCompleted: boolean;
  processingPromises: Promise<void>[];
  startTime: number;
  endTime?: number;
}

export class CaptureSystem {
  private readonly _logger: Logger = createLogger('CaptureSystem');
  private readonly _imagePipeline: ImageProcessingPipeline;
  private readonly _sessions = new Map<string, ExtendedCaptureSession>();
  private readonly _sessionCompletions = new Map<string, SessionCompletion>();
  private readonly _privacyService: PrivacyService;
  private _throttledOptions: Record<string, unknown> | null = null;

  constructor(privacyService: PrivacyService) {
    this._imagePipeline = new ImageProcessingPipeline();
    this._privacyService = privacyService;
    this._logger.info('CaptureSystem initialized');
  }

  startSession(taskId: TaskId, tabId: TabId): string {
    const sessionId = `${taskId}-${tabId}-${Date.now()}`;
    this._logger.info(`开始新的视觉捕获会话: ${sessionId}`);
    const newSession: ExtendedCaptureSession = {
      sessionId,
      taskId,
      tabId,
      status: 'running',
      imageSlices: [],
      contentSegments: [],
      scanCompleted: false,
      processingPromises: [],
      startTime: Date.now(),
    };
    this._sessions.set(sessionId, newSession);

    let resolvePromise!: (value: string | PromiseLike<string>) => void;
    let rejectPromise!: (reason?: unknown) => void;
    const promise = new Promise<string>((resolve, reject) => {
      resolvePromise = resolve;
      rejectPromise = reject;
    });

    this._sessionCompletions.set(sessionId, {
      promise,
      resolve: resolvePromise,
      reject: rejectPromise,
    });

    this._logger.info(`Capture session ${sessionId} started for task ${taskId} on tab ${tabId}`);
    void this._checkSessionCompletion(sessionId);
    return sessionId;
  }
  
  waitForSessionCompletion(sessionId: string): Promise<string> {
    const completion = this._sessionCompletions.get(sessionId);
    if (!completion) {
      this._logger.error(`No completion promise found for session ${sessionId}. Available sessions: ${Array.from(this._sessions.keys()).join(', ')}`);
      return Promise.reject(new Error(`No completion promise found for session ${sessionId}`));
    }
    return completion.promise;
  }

  async addSlice(sessionId: string, slice: ImageSegment): Promise<void> {
    const session = this._sessions.get(sessionId);
    if (!session) {
      this._logger.warn(`Cannot add slice to session ${sessionId}: session not found.`);
      return;
    }
    
    if (session.status !== 'running') {
      this._logger.warn(`Cannot add slice to session ${sessionId}: session status is ${session.status}.`);
      return;
    }
    
    slice.id = `segment-${generateId()}`;
    session.imageSlices.push(slice);
    
    const processingPromise = this._imagePipeline.processSegments([slice])
      .then(contentSegments => {
        if (contentSegments.length > 0) {
          session.contentSegments.push(...contentSegments);
        }
      })
      .catch(error => {
        this._logger.error(`Error processing slice ${slice.id} for session ${sessionId}:`, error);
        // Decide if one slice failure fails the whole session
      });
      
    session.processingPromises.push(processingPromise);
    
    void this._privacyService.logDataAccess({
      type: 'read',
      category: DataAccessCategory.SCREEN_CAPTURE,
      accessedBy: `CaptureSystem-session-${sessionId}`,
      userApproved: true,
      context: { tabId: session.tabId, taskId: session.taskId }
    });

    await this._checkSessionCompletion(sessionId);
  }

  async signalScanCompleted(sessionId: string): Promise<void> {
    const session = this._sessions.get(sessionId);
    if (session) {
      session.scanCompleted = true;
      this._logger.info(`Scan completed signal received for session ${sessionId}.`);
      await this._checkSessionCompletion(sessionId);
    }
  }
  
  cancelSession(sessionId: string, error: string): void {
    const session = this._sessions.get(sessionId);
    const completion = this._sessionCompletions.get(sessionId);
    if (session) {
      if (session.status === 'failed' || session.status === 'completed') {
        this._logger.warn(`Session ${sessionId} already terminated with status ${session.status}. Ignoring cancellation request.`);
        return;
      }
      session.status = 'failed';
      this._logger.error(`Session ${sessionId} cancelled due to error: ${error}`);
      if (completion) {
        this._sessionCompletions.delete(sessionId); // 立即清理completion
      }
      this.cleanupSession(sessionId, 3000); // 延迟3秒清理
    }
  }

  /**
   * 获取指定session的图像数据
   * @param sessionId session ID
   * @returns 图像数据数组
   */
  getSessionImages(sessionId: string): ImageSegment[] {
    const session = this._sessions.get(sessionId);
    if (!session) {
      this._logger.warn(`Session ${sessionId} not found.`);
      return [];
    }
    return [...session.imageSlices]; // 返回副本
  }

  private async _checkSessionCompletion(sessionId: string): Promise<void> {
    const session = this._sessions.get(sessionId);
    if (!session || !session.scanCompleted || session.status !== 'running') {
      return;
    }

    try {
      // Wait for all slice processing to finish
      await Promise.all(session.processingPromises);
      session.status = 'processing';
      
      this._logger.info(`All slices for session ${sessionId} have been processed. Finalizing...`);
      
      const finalContent = this.endSession(sessionId);
      const completion = this._sessionCompletions.get(sessionId);
      if (completion) {
        completion.resolve(finalContent);
        this._sessionCompletions.delete(sessionId); // 清理completion
      }
    } catch (error) {
      this._logger.error(`Error during session completion for ${sessionId}:`, error);
      const completion = this._sessionCompletions.get(sessionId);
      if (completion) {
        completion.reject(error);
        this._sessionCompletions.delete(sessionId);
      }
      session.status = 'failed';
    }
  }

  public endSession(sessionId: string): string {
    const session = this._sessions.get(sessionId);
    if (!session) {
      this._logger.warn(`Attempted to end non-existent session: ${sessionId}`);
      return '';
    }
    
    session.status = 'completed';
    session.endTime = Date.now();
    this._logger.info(`Session ${sessionId} ended.`);
    return this._stitchContent(session.contentSegments);
  }

  public cleanupSession(sessionId: string, delay = 3 * 60 * 1000): void {
    setTimeout(() => {
      if (this._sessions.has(sessionId)) {
        this._sessions.delete(sessionId);
        this._logger.info(`Session ${sessionId} data cleaned up after delay.`);
      }
    }, delay);
  }

  public setThrottledCaptureOptions(options: Record<string, unknown> | null): void {
    this._logger.info('更新节流截图选项:', options);
    this._throttledOptions = options;
  }

  public getCaptureOptions(baseOptions: Record<string, unknown>): Record<string, unknown> {
    if (this._throttledOptions) {
      this._logger.info('正在应用节流选项...', this._throttledOptions);
      return { ...baseOptions, ...this._throttledOptions };
    }
    return baseOptions;
  }

  /**
   * 立即清除与特定任务ID相关的所有会话数据。
   * @param taskId 要清除其会话的任务ID。
   */
  public purgeAllSessionsForTask(taskId: TaskId): void {
    this._logger.info(`Purging all sessions for task ${taskId}...`);
    let purgedCount = 0;
    const sessionsToPurge: string[] = [];

    // 找到所有与该任务相关的会话
    for (const [sessionId, session] of this._sessions.entries()) {
      if (session.taskId === taskId) {
        sessionsToPurge.push(sessionId);
      }
    }

    // 执行清除
    for (const sessionId of sessionsToPurge) {
      this._sessions.delete(sessionId);
      this._sessionCompletions.delete(sessionId);
      purgedCount++;
    }

    if (purgedCount > 0) {
      this._logger.info(`Purged ${purgedCount} sessions for task ${taskId}.`);
    } else {
      this._logger.info(`No active sessions found for task ${taskId} to purge.`);
    }
  }

  private _stitchContent(segments: ContentSegment[]): string {
    if (segments.length === 0) return '';
    
    // Simple stitching for now, can be improved with overlap detection
    return segments.map(s => s.content).join('\n\n');
  }
} 
