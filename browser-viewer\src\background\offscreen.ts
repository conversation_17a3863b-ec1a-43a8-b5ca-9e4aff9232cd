/**
 * Offscreen Document - 后台计算统一处理器
 *
 * 该脚本作为任务执行的宿主环境运行在 Offscreen Document 中。其职责是：
 * 1. 管理一个Web Worker池，用于CPU密集型任务（如图像处理）。
 * 2. 接收任务并将其分发给工作池中的可用Worker。
 * 3. 播放静音音频以保持扩展激活，对抗后台节流。
 */
import { MESSAGE_TYPES } from '@/shared/constants';
import type { AIConfig, ContentSegment, ImageSegment, ServiceMessage } from '@/shared/types';
import { createLogger, type Logger } from '@/shared/utils';
import 'webextension-polyfill';
import ImageProcessorWorker from '../workers/image-processor.worker.ts?worker&inline';

// 全局常量，用于模式切换。在vite.config.ts中可以被替换。
declare const __DEV_MODE__: boolean;

const logger: Logger = createLogger('[OffscreenManager]');

// --- AI Config Handler ---
// 在Offscreen文档的顶层作用域安全地读取一次环境变量
const aiConfig: AIConfig = {
  apiKey: (import.meta.env.VITE_OPENAI_API_KEY as string) ?? 'YOUR_API_KEY_HERE',
  apiEndpoint: (import.meta.env.VITE_AI_API_ENDPOINT as string) ?? 'https://api.openai.com/v1/chat/completions',
  model: (import.meta.env.VITE_AI_MODEL as string) ?? 'gpt-4.1-nano',
  prompt: '从此图像中提取所有文本内容。仅返回文本，不含任何额外注释或格式。',
};

// --- Audio Handler ---
const audio = document.querySelector<HTMLAudioElement>('#silent-audio');
const SILENT_WAV_DATA_URI = 'data:audio/wav;base64,UklGRjIAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=';

function playAudio() {
  if (!audio) {
    logger.error('Audio element not found in offscreen document.');
    return;
  }
  
  // 确保音频循环播放，这是对抗后台节流的关键
  audio.loop = true;
  
  // 如果音源未设置，则进行设置。这确保了首次播放和暂停后再播放的可靠性。
  if (!audio.src) {
    audio.src = SILENT_WAV_DATA_URI;
  }
  
  // 仅在音频当前未播放时调用 play()，避免不必要的操作和潜在错误。
  if (audio.paused) {
    audio.play().catch(error => {
      // 在Manifest V3的Offscreen文档中，即使用户没有交互，
      // play()的调用本身就足以让Service Worker保持活动状态，
      // 我们可以安全地忽略NotAllowedError和NotSupportedError（如果源已损坏）。
      if ((error as Error).name !== 'NotAllowedError' && (error as Error).name !== 'NotSupportedError') {
        logger.warn(`Silent audio playback failed unexpectedly: Name: ${(error as Error).name}, Message: ${(error as Error).message}`, error);
      }
    });
  }
}

function pauseAudio() {
  if (!audio) {
    logger.error('Audio element not found in offscreen document.');
    return;
  }
  if (!audio.paused) {
    audio.pause();
  }
  // 在暂停时清除src可以释放内存资源。
  audio.src = ''; 
  logger.info('Paused silent audio and released resources.');
}

function handleAudioMessage(message: ServiceMessage): void {
  // 在Offscreen Document的上下文中，直接使用字符串字面量比依赖导入的常量更健壮，
  // 可以避免因打包或模块解析问题导致的常量值不匹配。
  switch (message.type) {
    case 'PLAY_AUDIO': {
      logger.info('Received PLAY_AUDIO command.');
      playAudio();
      break;
    }
    case 'PAUSE_AUDIO': {
      logger.info('Received PAUSE_AUDIO command.');
      pauseAudio();
      break;
    }
    default:
      logger.warn(`Received unknown audio message type: ${String(message.type)}`);
  }
}

// --- Worker Pool Implementation ---
const workerPool: Worker[] = [];
const taskQueue: { segment: ImageSegment, resolve: (result: ContentSegment) => void, reject: (reason?: Error) => void }[] = [];
// This map tracks the specific task each worker is currently processing.
const workerTasks = new Map<Worker, { resolve: (result: ContentSegment) => void, reject: (reason?: Error) => void }>();
const maxWorkers = navigator.hardwareConcurrency || 2;

function createWorker(): Worker {
  logger.info('Creating a new image processing worker.');
  const worker = new ImageProcessorWorker();

  worker.onmessage = (event: MessageEvent<ContentSegment>) => {
    const task = workerTasks.get(worker);
    if (!task) {
      logger.warn('Worker sent a message but was not associated with a task. Ignoring.');
      return;
    }
    workerTasks.delete(worker); // Worker is now free.

    // Worker始终返回ContentSegment。
    // Worker中的'catch'块会处理API错误并返回一个低置信度的分段。
    // 因此，我们可以在这里始终resolve。
    const result = event.data;
    task.resolve(result);

    // 任务完成后，尝试处理队列中的下一个任务
    processNextTask(worker);
  };
  
  worker.onerror = (event: ErrorEvent) => {
    const message = event.message ?? 'A generic error occurred in the worker';
    const filename = event.filename ?? 'Unknown file';
    const lineno = event.lineno ?? 0;
    const isInitializationError = !workerTasks.has(worker);

    // 构造更详细的错误信息
    const errorMessage = `An error occurred in a worker:
      Message: ${message}
      File: ${filename}
      Line: ${lineno}
      ${isInitializationError ? 'This appears to be an initialization error. The worker may be broken.' : ''}`;
    
    // 优先记录 event.error 因为它包含原始错误对象和堆栈
    logger.error(errorMessage, event.error instanceof Error ? event.error : event);

    // 如果是初始化错误，我们不能继续使用这个worker。
    // 这里我们只是简单返回，但更复杂的实现可以考虑从池中移除并替换它。
    // 由于没有与任务关联，我们直接返回，避免进一步操作。
    if (isInitializationError) {
      return;
    }
    
    // 对于运行时错误，我们需要拒绝关联的 promise
    const task = workerTasks.get(worker);
    if (task) {
      workerTasks.delete(worker); // Worker is now free.
      
      const rejectionError = event.error instanceof Error ? event.error : new Error(message);
      task.reject(rejectionError);
    } else {
      logger.warn('Worker error occurred but no associated task found');
    }

    // 出现错误后也尝试处理下一个任务
    processNextTask(worker);
  };
  
  return worker;
}

function processNextTask(worker: Worker): void {
    // 待机状态的worker将尝试获取队列中的下一个任务
    if (workerTasks.has(worker) || taskQueue.length === 0) {
        return;
    }

    const task = taskQueue.shift(); 
    if (task) {
        workerTasks.set(worker, { resolve: task.resolve, reject: task.reject });
        const messagePayload = {
          segment: task.segment,
          isDev: __DEV_MODE__,
          aiConfig: aiConfig, // 附加AI配置
        };
        // 将 ImageBitmap 或 ArrayBuffer 的所有权转移给 worker，实现零拷贝
        const transferables: Transferable[] = [];
        if (task.segment.bitmap) {
            transferables.push(task.segment.bitmap);
        } else if (task.segment.data) {
            transferables.push(task.segment.data);
        }
        worker.postMessage(messagePayload, transferables);
    }
}

function initializeWorkerPool(): void {
  for (let i = 0; i < maxWorkers; i++) {
    workerPool.push(createWorker());
  }
  logger.info(`Worker pool initialized with ${maxWorkers} workers.`);
}

async function handleProcessImageSegments(segments: ImageSegment[]): Promise<ContentSegment[]> {
  logger.info(`Queueing ${segments.length} segments to the worker pool.`);

  // 在通过 chrome.runtime.sendMessage 传递后，ArrayBuffer 会被序列化为普通对象。
  // 在将它们传递给 worker 之前，我们需要将其重建为真正的 ArrayBuffer。
  const fixedSegments = segments.map(segment => {
    // 检查 data 是否存在且不是我们期望的 ArrayBuffer 类型
    if (segment.data && !(segment.data instanceof ArrayBuffer) && !ArrayBuffer.isView(segment.data)) {
      const plainObject = segment.data as unknown as { [key: number]: number };
      // 使用 Object.keys 和 map 来处理不可枚举属性的对象
      const byteValues = Object.keys(plainObject).map(key => plainObject[Number(key)]);
      segment.data = new Uint8Array(byteValues).buffer;
    }
    return segment;
  });
  
  const processingPromises = fixedSegments.map(segment => 
    new Promise<ContentSegment>((resolve, reject) => {
      taskQueue.push({ segment, resolve, reject });
    })
  );

  // 队列每增加一个新任务都通知所有worker检查是否需要获取，忙碌的会忽略
  workerPool.forEach(processNextTask);

  return Promise.all(processingPromises);
}

// --- Main Message Listener ---
function initialize(): void {
  logger.info('Offscreen document initialized.');
  
  // 初始化时立即尝试播放音频，以激活并保持文档活跃。
  playAudio();
  
  initializeWorkerPool();

  chrome.runtime.onMessage.addListener((message: ServiceMessage, _sender, sendResponse) => {
    // 根据消息类型分发任务
    if (message.type === MESSAGE_TYPES.PROCESS_IMAGE_SEGMENTS) {
      const payload = message.payload as { segments: ImageSegment[] };
      handleProcessImageSegments(payload.segments)
        .then(results => {
          // 直接返回结果数组，这是调用方 (ImageProcessingPipeline) 所期望的格式。
          sendResponse(results);
        })
        .catch(error => {
          logger.error('Error processing image segments batch:', error);
          // 在出现错误时，返回一个空数组作为安全的回退值，
          // 以防止上游的Promise链中断。
          sendResponse([]);
        });
      return true; // 表示我们将异步响应
    } else if (message.target === 'offscreen-audio' || message.type === 'PLAY_AUDIO' || message.type === 'PAUSE_AUDIO') {
      handleAudioMessage(message);
      // 音频消息不需要响应，因此不返回true
    }
  });
}

initialize();
