/**
 * SharedWorkerPool 单元测试
 * 测试共享Worker池服务的任务管理和消息处理功能
 */

import type { ImageSegment, ServiceMessage } from '@/shared/types';
import { beforeEach, describe, expect, it, vi, type MockedFunction } from 'vitest';

// 从 chrome-api-setup 导入 mock 对象
declare const chrome: {
  runtime: {
    onMessage: {
      addListener: MockedFunction<any>;
      removeListener: MockedFunction<any>;
    };
    sendMessage: MockedFunction<any>;
  };
};

// Mock OffscreenManager
const mockOffscreenManager = {
  startAudio: vi.fn().mockResolvedValue(undefined),
  stopAudio: vi.fn().mockResolvedValue(undefined),
};

vi.mock('@/background/OffscreenManager', () => ({
  offscreenManager: mockOffscreenManager,
}));

// 注意：不需要重复定义Chrome API，chrome-api-setup.ts已经设置了

vi.mock('@/shared/utils', () => ({
  createLogger: () => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
}));

describe('SharedWorkerPool', () => {
  let SharedWorkerPoolService: any;
  let initializeSharedWorkerPool: any;
  let getSharedWorkerPool: any;
  let getSharedWorkerPoolInstance: any;
  let workerPool: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.resetModules();

    // 重置 chrome API mocks
    chrome.runtime.sendMessage.mockResolvedValue(undefined);
    mockOffscreenManager.startAudio.mockResolvedValue(undefined);
    mockOffscreenManager.stopAudio.mockResolvedValue(undefined);

    // 重新导入模块
    const module = await import('@/services/pool/SharedWorkerPool');
    SharedWorkerPoolService = module.SharedWorkerPoolService;
    initializeSharedWorkerPool = module.initializeSharedWorkerPool;
    getSharedWorkerPool = module.getSharedWorkerPool;
    getSharedWorkerPoolInstance = module.getSharedWorkerPoolInstance;
  });

  describe('SharedWorkerPoolService 类', () => {
    beforeEach(() => {
      vi.clearAllMocks();
      workerPool = new SharedWorkerPoolService();
    });

    afterEach(() => {
      workerPool?.destroy();
    });

    describe('初始化', () => {
      it('应该成功初始化并注册消息监听器', () => {
        expect(chrome.runtime.onMessage.addListener).toHaveBeenCalledWith(
          expect.any(Function)
        );
      });
    });

    describe('任务执行', () => {
      it('应该成功提交并处理计算任务', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        const expectedResult = {
          id: 'segment-1',
          text: 'Processed text',
          timestamp: Date.now(),
        };

        // 模拟任务执行
        const runPromise = workerPool.run(mockImageSegment);

        // 等待下一个事件循环，确保消息处理器已注册
        await new Promise(resolve => setTimeout(resolve, 0));

        // 获取注册的消息处理器
        const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];

        // 模拟来自 offscreen document 的响应
        const responseMessage: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: {
            taskId: 'task-0',
            result: expectedResult,
          },
          timestamp: Date.now(),
        };

        const mockSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟响应以模拟异步行为
        setTimeout(() => {
          messageHandler(responseMessage, mockSender);
        }, 10);

        const result = await runPromise;

        expect(mockOffscreenManager.startAudio).toHaveBeenCalledTimes(1);
        expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
          type: 'EXECUTE_WORKER_TASK',
          target: 'offscreen-compute',
          payload: {
            taskId: 'task-0',
            data: mockImageSegment,
            transfer: undefined,
          },
        });
        expect(result).toEqual(expectedResult);
        expect(mockOffscreenManager.stopAudio).toHaveBeenCalledTimes(1);
      }, 15000);

      it('应该处理任务执行失败', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        const runPromise = workerPool.run(mockImageSegment);

        // 等待下一个事件循环
        await new Promise(resolve => setTimeout(resolve, 0));

        const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];

        // 模拟错误响应
        const errorMessage: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: {
            taskId: 'task-0',
            error: 'Processing failed',
          },
          timestamp: Date.now(),
        };

        const mockSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟响应
        setTimeout(() => {
          messageHandler(errorMessage, mockSender);
        }, 10);

        await expect(runPromise).rejects.toThrow('Processing failed');
        expect(mockOffscreenManager.stopAudio).toHaveBeenCalledTimes(1);
      }, 15000);

      it('应该处理带有转移对象的任务', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        const transferObjects = [new ArrayBuffer(512)];

        const runPromise = workerPool.run(mockImageSegment, transferObjects);

        // 等待消息处理器注册
        await new Promise(resolve => setTimeout(resolve, 0));

        const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];

        const responseMessage: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: {
            taskId: 'task-0',
            result: { id: 'segment-1', text: 'Processed', timestamp: Date.now() },
          },
          timestamp: Date.now(),
        };

        const mockSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟响应
        setTimeout(() => {
          messageHandler(responseMessage, mockSender);
        }, 10);

        await runPromise;

        expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
          type: 'EXECUTE_WORKER_TASK',
          target: 'offscreen-compute',
          payload: {
            taskId: 'task-0',
            data: mockImageSegment,
            transfer: transferObjects,
          },
        });
      }, 15000);

      it('应该为每个任务生成唯一的任务ID', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        // 启动多个并发任务
        const task1Promise = workerPool.run(mockImageSegment);
        const task2Promise = workerPool.run(mockImageSegment);

        // 等待消息处理器注册
        await new Promise(resolve => setTimeout(resolve, 0));

        const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];
        const mockSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟完成任务
        setTimeout(() => {
          // 完成第一个任务
          messageHandler({
            id: 'msg-1',
            type: 'WORKER_RESULT',
            payload: {
              taskId: 'task-0',
              result: { id: 'segment-1', text: 'Result 1', timestamp: Date.now() },
            },
            timestamp: Date.now(),
          }, mockSender);

          // 完成第二个任务
          messageHandler({
            id: 'msg-2',
            type: 'WORKER_RESULT',
            payload: {
              taskId: 'task-1',
              result: { id: 'segment-1', text: 'Result 2', timestamp: Date.now() },
            },
            timestamp: Date.now(),
          }, mockSender);
        }, 10);

        const [result1, result2] = await Promise.all([task1Promise, task2Promise]);

        expect(result1.text).toBe('Result 1');
        expect(result2.text).toBe('Result 2');
        expect(chrome.runtime.sendMessage).toHaveBeenCalledTimes(2);
      }, 15000);
    });

    describe('消息处理', () => {
      let messageHandler: (message: ServiceMessage, sender: chrome.runtime.MessageSender) => void;

      beforeEach(() => {
        messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      });

      it('应该验证消息来源', () => {
        const message: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: { taskId: 'task-0', result: {} },
          timestamp: Date.now(),
        };

        // 无效来源
        const invalidSender = {
          url: 'chrome-extension://test/invalid.html',
        };

        // 不应该处理无效来源的消息
        expect(() => messageHandler(message, invalidSender)).not.toThrow();
        
        // 验证没有创建待处理任务
        expect(workerPool['_pendingTasks'].size).toBe(0);
      });

      it('应该处理未知任务ID的结果', () => {
        const message: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: { taskId: 'unknown-task', result: {} },
          timestamp: Date.now(),
        };

        const validSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 应该优雅地处理未知任务ID
        expect(() => messageHandler(message, validSender)).not.toThrow();
      });

      it('应该处理无效的消息格式', () => {
        const invalidMessages = [
          { id: 'msg-1', type: 'INVALID_TYPE', payload: {}, timestamp: Date.now() },
          { id: 'msg-2', type: 'WORKER_RESULT', payload: null, timestamp: Date.now() },
          { id: 'msg-3', type: 'WORKER_RESULT', payload: { noTaskId: true }, timestamp: Date.now() },
        ];

        const validSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        invalidMessages.forEach(message => {
          expect(() => messageHandler(message as ServiceMessage, validSender)).not.toThrow();
        });
      });

      it('应该处理无效的工作器响应', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        const runPromise = workerPool.run(mockImageSegment);

        // 等待消息处理器注册
        await new Promise(resolve => setTimeout(resolve, 0));

        // 发送无效响应（既没有result也没有error）
        const invalidMessage: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: { taskId: 'task-0' },
          timestamp: Date.now(),
        };

        const validSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟发送无效响应
        setTimeout(() => {
          messageHandler(invalidMessage, validSender);
        }, 10);

        await expect(runPromise).rejects.toThrow('Invalid worker response');
      }, 15000);
    });

    describe('资源管理', () => {
      it('应该在任务失败时释放资源', async () => {
        mockOffscreenManager.startAudio.mockRejectedValue(new Error('Audio start failed'));

        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        await expect(workerPool.run(mockImageSegment)).rejects.toThrow('Audio start failed');

        // 即使startAudio失败，也不应该调用stopAudio
        expect(mockOffscreenManager.stopAudio).not.toHaveBeenCalled();
      });

      it('应该正确管理Offscreen Document生命周期', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        const runPromise = workerPool.run(mockImageSegment);

        // 等待消息处理器注册
        await new Promise(resolve => setTimeout(resolve, 0));

        // 验证startAudio被调用
        expect(mockOffscreenManager.startAudio).toHaveBeenCalledTimes(1);

        const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];
        const responseMessage: ServiceMessage = {
          id: 'msg-1',
          type: 'WORKER_RESULT',
          payload: {
            taskId: 'task-0',
            result: { id: 'segment-1', text: 'Processed', timestamp: Date.now() },
          },
          timestamp: Date.now(),
        };

        const mockSender = {
          url: 'chrome-extension://test/offscreen.html',
        };

        // 延迟响应
        setTimeout(() => {
          messageHandler(responseMessage, mockSender);
        }, 10);

        await runPromise;

        // 验证stopAudio被调用
        expect(mockOffscreenManager.stopAudio).toHaveBeenCalledTimes(1);
      }, 15000);
    });

    describe('销毁和清理', () => {
      it('应该清理所有待处理任务', async () => {
        const mockImageSegment: ImageSegment = {
          id: 'segment-1',
          position: { x: 0, y: 0, width: 100, height: 100 },
          data: new ArrayBuffer(1024),
          mimeType: 'image/png',
          timestamp: Date.now(),
        };

        // 启动一些任务但不完成它们
        const task1Promise = workerPool.run(mockImageSegment);
        const task2Promise = workerPool.run(mockImageSegment);

        // 等待任务被添加到待处理列表
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(workerPool['_pendingTasks'].size).toBe(2);

        workerPool.destroy();

        // 所有任务应该被拒绝
        await expect(task1Promise).rejects.toThrow('SharedWorkerPoolService is being destroyed.');
        await expect(task2Promise).rejects.toThrow('SharedWorkerPoolService is being destroyed.');

        expect(workerPool['_pendingTasks'].size).toBe(0);
        expect(chrome.runtime.onMessage.removeListener).toHaveBeenCalledWith(
          expect.any(Function)
        );
      });

      it('应该处理空状态的销毁', () => {
        expect(() => workerPool.destroy()).not.toThrow();
        expect(chrome.runtime.onMessage.removeListener).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('全局单例管理', () => {
    it('应该初始化全局Worker池', () => {
      expect(getSharedWorkerPoolInstance()).toBeNull();

      initializeSharedWorkerPool();

      const instance = getSharedWorkerPoolInstance();
      expect(instance).not.toBeNull();
      expect(instance).toBeInstanceOf(SharedWorkerPoolService);

      // 清理
      instance?.destroy();
    });

    it('应该防止重复初始化', () => {
      initializeSharedWorkerPool();
      const firstInstance = getSharedWorkerPoolInstance();

      initializeSharedWorkerPool();
      const secondInstance = getSharedWorkerPoolInstance();

      expect(firstInstance).toBe(secondInstance);

      // 清理
      firstInstance?.destroy();
    });

    it('应该通过getSharedWorkerPool获取实例', () => {
      initializeSharedWorkerPool();

      const instance = getSharedWorkerPool();
      expect(instance).toBe(getSharedWorkerPoolInstance());

      // 清理
      instance.destroy();
    });

    it('应该在未初始化时抛出错误', () => {
      expect(() => getSharedWorkerPool()).toThrow(
        'Shared worker pool has not been initialized'
      );
    });
  });

  describe('并发和性能', () => {
    beforeEach(() => {
      workerPool = new SharedWorkerPoolService();
    });

    afterEach(() => {
      workerPool?.destroy();
    });

    it('应该处理高并发任务', async () => {
      const mockImageSegment: ImageSegment = {
        id: 'segment-1',
        position: { x: 0, y: 0, width: 100, height: 100 },
        data: new ArrayBuffer(1024),
        mimeType: 'image/png',
        timestamp: Date.now(),
      };

      const taskCount = 10;
      const taskPromises = Array.from({ length: taskCount }, () =>
        workerPool.run(mockImageSegment)
      );

      // 等待任务被添加到待处理列表
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(workerPool['_pendingTasks'].size).toBe(taskCount);

      const messageHandler = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSender = {
        url: 'chrome-extension://test/offscreen.html',
      };

      // 完成所有任务
      for (let i = 0; i < taskCount; i++) {
        messageHandler({
          id: `msg-${i}`,
          type: 'WORKER_RESULT',
          payload: {
            taskId: `task-${i}`,
            result: { id: 'segment-1', text: `Result ${i}`, timestamp: Date.now() },
          },
          timestamp: Date.now(),
        }, mockSender);
      }

      const results = await Promise.all(taskPromises);

      expect(results).toHaveLength(taskCount);
      expect(workerPool['_pendingTasks'].size).toBe(0);
      expect(mockOffscreenManager.startAudio).toHaveBeenCalledTimes(taskCount);
      expect(mockOffscreenManager.stopAudio).toHaveBeenCalledTimes(taskCount);
    });
  });
});