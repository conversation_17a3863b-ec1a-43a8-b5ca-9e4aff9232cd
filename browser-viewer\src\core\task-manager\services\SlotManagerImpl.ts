/**
 * 槽位管理器实现
 * 
 * 职责：
 * - 管理浏览器标签页槽位的生命周期
 * - 提供槽位分配和释放功能
 * - 维护槽位与任务的映射关系
 */

import { WorkerWindowManager } from '@/background/WorkerWindowManager';
import { TabId } from '@/shared/types';
import { createLogger } from '@/shared/utils';
import { SlotManager, SlotState } from '../interfaces';
import { Task } from '../Task';

export class SlotManagerImpl implements SlotManager {
  private readonly _logger = createLogger('SlotManager');
  private readonly _slots: SlotState[] = [];
  private readonly _workerWindowManager: WorkerWindowManager;

  constructor(workerWindowManager: WorkerWindowManager) {
    this._workerWindowManager = workerWindowManager;
  }

  getAllSlots(): SlotState[] {
    return [...this._slots]; // 返回副本以防止外部修改
  }

  getAvailableSlots(): SlotState[] {
    return this._slots.filter(slot => !slot.isBusy);
  }

  getSlotByTabId(tabId: TabId): SlotState | undefined {
    return this._slots.find(slot => slot.tabId === tabId);
  }

  async addSlots(count: number): Promise<SlotState[]> {
    if (count <= 0) {
      return [];
    }

    this._logger.info(`正在添加 ${count} 个新槽位...`);
    const newSlots: SlotState[] = [];

    for (let i = 0; i < count; i++) {
      try {
        // 为了确保ID的唯一性，我们总是基于现有最大ID进行递增
        const newSlotId = (this._slots.length > 0 ? Math.max(...this._slots.map(s => s.id)) : -1) + 1;
        
        const tab = await this._workerWindowManager.createTab({ 
          active: false, 
          url: 'about:blank' 
        });

        if (tab.id) {
          const newSlot: SlotState = {
            id: newSlotId,
            tabId: tab.id,
            isBusy: false,
            currentTask: null,
            lastDomain: null,
          };

          this._slots.push(newSlot);
          newSlots.push(newSlot);
          
          this._logger.info(`新槽位 ${newSlotId} (Tab ID ${tab.id}) 已创建`);
        } else {
          this._logger.error(`为新槽位创建标签页失败，未返回 tab ID`);
        }
      } catch (error) {
        this._logger.error(`创建新槽位时出错:`, error);
      }
    }

    this._logger.info(`成功添加 ${newSlots.length}/${count} 个槽位`);
    return newSlots;
  }

  async removeSlot(slotId: number): Promise<void> {
    const slotIndex = this._slots.findIndex(slot => slot.id === slotId);
    
    if (slotIndex === -1) {
      this._logger.warn(`尝试移除不存在的槽位: ${slotId}`);
      return;
    }

    const slot = this._slots[slotIndex];
    
    if (slot.isBusy && slot.currentTask) {
      this._logger.warn(`尝试移除忙碌的槽位 ${slotId}，先取消其任务`);
      slot.currentTask.cancel();
    }

    try {
      await this._workerWindowManager.removeTab(slot.tabId);
      this._slots.splice(slotIndex, 1);
      this._logger.info(`槽位 ${slotId} (Tab ${slot.tabId}) 已移除`);
    } catch (error) {
      this._logger.error(`移除槽位 ${slotId} 时出错:`, error);
      // 即使标签页移除失败，也要从内存中移除槽位记录
      this._slots.splice(slotIndex, 1);
    }
  }

  markSlotBusy(slotId: number, task: Task): void {
    const slot = this._slots.find(s => s.id === slotId);
    
    if (!slot) {
      this._logger.error(`尝试标记不存在的槽位为忙碌: ${slotId}`);
      return;
    }

    if (slot.isBusy) {
      this._logger.warn(`槽位 ${slotId} 已经处于忙碌状态`);
    }

    slot.isBusy = true;
    slot.currentTask = task;
    
    this._logger.debug(`槽位 ${slotId} 已标记为忙碌，分配给任务 ${task._id}`);
  }

  markSlotFree(slotId: number, lastDomain?: string): void {
    const slot = this._slots.find(s => s.id === slotId);
    
    if (!slot) {
      this._logger.error(`尝试标记不存在的槽位为空闲: ${slotId}`);
      return;
    }

    slot.isBusy = false;
    slot.currentTask = null;
    if (lastDomain !== undefined) {
      slot.lastDomain = lastDomain;
    }
    
    this._logger.debug(`槽位 ${slotId} 已标记为空闲，最后处理域名: ${slot.lastDomain}`);
  }

  async shutdownAllSlots(): Promise<void> {
    if (this._slots.length === 0) {
      this._logger.debug('没有槽位需要关闭');
      return;
    }

    this._logger.info(`正在关闭所有 ${this._slots.length} 个槽位...`);
    
    // 复制一份槽位列表，因为我们将要修改 _slots 数组
    const slotsToClose = [...this._slots];
    this._slots.length = 0; // 立即清空，防止竞态条件

    // 取消所有正在运行的任务
    for (const slot of slotsToClose) {
      if (slot.isBusy && slot.currentTask) {
        this._logger.info(`取消槽位 ${slot.id} 中的任务 ${slot.currentTask._id}`);
        slot.currentTask.cancel();
      }
    }

    // 并行关闭所有标签页
    const removalPromises = slotsToClose.map(slot => 
      this._workerWindowManager.removeTab(slot.tabId)
        .catch(e => this._logger.warn(`关闭标签页 ${slot.tabId} 失败，可能已被关闭:`, e))
    );
    
    await Promise.all(removalPromises);
    this._logger.info('所有槽位均已关闭');
  }

  getRunningTasksCount(): number {
    return this._slots.filter(slot => slot.isBusy).length;
  }

  /**
   * 根据最大并发数调整槽位数量
   * @param maxConcurrency 最大并发数
   * @param pendingTaskCount 待处理任务数
   */
  async adjustSlotsForConcurrency(maxConcurrency: number, pendingTaskCount: number): Promise<void> {
    const currentSlotCount = this._slots.length;
    const runningTaskCount = this.getRunningTasksCount();
    
    // 计算理论上需要的槽位总数
    const requiredSlots = Math.min(maxConcurrency, runningTaskCount + pendingTaskCount);
    
    if (currentSlotCount < requiredSlots) {
      // 扩容
      const slotsToAdd = requiredSlots - currentSlotCount;
      this._logger.info(`需要扩容 ${slotsToAdd} 个槽位 (目标: ${requiredSlots}, 当前: ${currentSlotCount})`);
      await this.addSlots(slotsToAdd);
    } else if (currentSlotCount > maxConcurrency) {
      // 需要缩容，但不立即执行，等待任务自然完成后进行
      this._logger.info(`当前槽位数 ${currentSlotCount} 超过最大并发数 ${maxConcurrency}，将在任务完成后进行缩容`);
    }
  }

  /**
   * 检查并执行平滑缩容
   * 在任务完成时调用，检查是否需要关闭多余的槽位
   */
  async performSmoothScaleDown(maxConcurrency: number, justCompletedSlotId: number): Promise<void> {
    if (this._slots.length <= maxConcurrency) {
      return; // 不需要缩容
    }

    this._logger.info(`平滑缩容: 当前槽位数 ${this._slots.length} > 最大并发数 ${maxConcurrency}，关闭刚完成的槽位 ${justCompletedSlotId}`);
    await this.removeSlot(justCompletedSlotId);
  }

  /**
   * 为指定槽位寻找最合适的任务
   * 优先寻找同域名的任务以利用缓存
   */
  findBestTaskForSlot(slot: SlotState, availableTasks: any[]): number {
    if (availableTasks.length === 0) {
      return -1;
    }
    
    // 1. 尝试同源亲和性匹配
    if (slot.lastDomain) {
      const affinityIndex = availableTasks.findIndex(config => {
        try {
          return new URL(config.urls[0]).hostname === slot.lastDomain;
        } catch {
          return false; // 无效URL，不匹配
        }
      });
      
      if (affinityIndex !== -1) {
        this._logger.info(`槽位 ${slot.id} 找到同域名任务 (域名: ${slot.lastDomain})`);
        return affinityIndex;
      }
    }
    
    // 2. 如果没有亲和性匹配，则寻找优先级最高的任务
    let highestPriorityIndex = 0;
    let highestPriority = availableTasks[0].priority ?? 10; // 默认优先级为10
    
    for (let i = 1; i < availableTasks.length; i++) {
      const currentPriority = availableTasks[i].priority ?? 10;
      if (currentPriority < highestPriority) { // 数字越小优先级越高
        highestPriority = currentPriority;
        highestPriorityIndex = i;
      }
    }
    
    this._logger.info(`槽位 ${slot.id} 选择优先级最高的任务 (优先级: ${highestPriority})`);
    return highestPriorityIndex;
  }
}