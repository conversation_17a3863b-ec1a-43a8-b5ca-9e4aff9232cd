/**
 * 统一状态存储实现
 * 
 * 基于 chrome.storage 的统一状态管理系统
 * 支持事务、订阅、冲突解决等企业级特性
 */

import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import {
  ConflictResolution,
  StateChangeEvent,
  StateKey,
  StateStore,
  StateStoreConfig,
  StateStoreStats,
  StateSubscription,
  TransactionContext
} from './interfaces/StateStore';

import { SecurityService } from '@/services/security-service';
import { createLogger } from '@/shared/utils';

// 默认配置
const DEFAULT_CONFIG: StateStoreConfig = {
  defaultConflictResolution: ConflictResolution.OVERWRITE,
  enableCompression: false,
  enableEncryption: true,
  persistenceInterval: 5000,
  maxCacheSize: 1000,
  transactionTimeout: 30000
};

// 内部存储项结构
interface StorageItem<T = any> {
  value: T;
  timestamp: number;
  version: number;
  metadata?: Record<string, any>;
}

// 事务操作类型
interface TransactionOperation {
  type: 'save' | 'delete';
  key: StateKey;
  value?: any;
  oldValue?: any;
}

/**
 * 事务上下文实现
 */
class TransactionContextImpl implements TransactionContext {
  private readonly _operations: TransactionOperation[] = [];
  private readonly _store: UnifiedStateStore;
  private _completed = false;
  private readonly _startTime = Date.now();

  constructor(store: UnifiedStateStore, private readonly _timeout: number) {
    this._store = store;
    
    // 设置事务超时
    setTimeout(() => {
      if (!this._completed) {
        void this.rollback();
      }
    }, _timeout);
  }

  save<T>(key: StateKey, data: T): void {
    this._checkCompleted();
    
    // 记录当前值以便回滚
    const oldValue = this._store.getCachedValue(key);
    
    this._operations.push({
      type: 'save',
      key,
      value: data,
      oldValue
    });
  }

  load<T>(key: StateKey): T | null {
    this._checkCompleted();
    
    // 先检查事务中的操作
    for (let i = this._operations.length - 1; i >= 0; i--) {
      const op = this._operations[i];
      if (op.key === key) {
        if (op.type === 'delete') {
          return null;
        } else if (op.type === 'save') {
          return op.value;
        }
      }
    }
    
    // 从存储中读取
    return this._store.getCachedValue<T>(key);
  }

  delete(key: StateKey): void {
    this._checkCompleted();
    
    const oldValue = this._store.getCachedValue(key);
    
    this._operations.push({
      type: 'delete',
      key,
      oldValue
    });
  }

  async commit(): Promise<void> {
    this._checkCompleted();
    
    try {
      // 执行所有操作
      for (const operation of this._operations) {
        if (operation.type === 'save') {
          await this._store.directSave(operation.key, operation.value);
        } else if (operation.type === 'delete') {
          await this._store.directDelete(operation.key);
        }
      }
      
      this._completed = true;
      this._store.incrementTransactionCount();
    } catch (error) {
      this._store.incrementFailedTransactionCount();
      throw error;
    }
  }

  rollback(): void {
    this._checkCompleted();
    this._completed = true;
    // 回滚操作不需要实际执行，因为我们还没有提交到存储
  }

  isCompleted(): boolean {
    return this._completed;
  }

  private _checkCompleted(): void {
    if (this._completed) {
      throw new Error('Transaction has been completed');
    }
    
    if (Date.now() - this._startTime > this._timeout) {
      this._completed = true;
      throw new Error('Transaction has timed out');
    }
  }
}

/**
 * 统一状态存储实现
 */
export class UnifiedStateStore implements StateStore {
  private readonly _logger = createLogger('UnifiedStateStore');
  private readonly _config: StateStoreConfig;
  private readonly _cache = new Map<StateKey, StorageItem>();
  private readonly _changeSubject = new Subject<StateChangeEvent>();
  private readonly _securityService: SecurityService;
  
  // 统计信息
  private _transactionCount = 0;
  private _failedTransactionCount = 0;
  private _cacheHits = 0;
  private _cacheRequests = 0;
  private _lastPersistTime = 0;

  constructor(config: Partial<StateStoreConfig> = {}, securityService?: SecurityService) {
    this._config = { ...DEFAULT_CONFIG, ...config };
    this._securityService = securityService || SecurityService.getInstance();
    
    // 启动定期持久化
    if (this._config.persistenceInterval > 0) {
      setInterval(() => {
        void this._persistCache();
      }, this._config.persistenceInterval);
    }
  }

  async save<T>(key: StateKey, data: T, conflictResolution?: ConflictResolution): Promise<void> {
    const resolution = conflictResolution ?? this._config.defaultConflictResolution;
    
    // 检查冲突
    const existing = await this.load<T>(key);
    if (existing !== null && resolution !== ConflictResolution.OVERWRITE) {
      await this._handleConflict(key, existing, data, resolution);
      return;
    }
    
    await this.directSave(key, data);
  }

  async load<T>(key: StateKey): Promise<T | null> {
    this._cacheRequests++;
    
    // 先检查缓存
    if (this._cache.has(key)) {
      this._cacheHits++;
      const item = this._cache.get(key)!;
      return item.value as T;
    }
    
    // 从持久化存储加载
    try {
      const rawData = await this._loadFromStorage(key);
      if (rawData === null) {
        return null;
      }
      
      const item: StorageItem<T> = {
        value: rawData,
        timestamp: Date.now(),
        version: 1
      };
      
      // 更新缓存
      this._updateCache(key, item);
      
      return rawData as T;
    } catch (error) {
      this._logger.error(`加载状态失败: ${key}`, error);
      return null;
    }
  }

  async delete(key: StateKey): Promise<void> {
    const oldValue = this.getCachedValue(key);
    await this.directDelete(key);
    
    this._emitChangeEvent({
      key,
      oldValue,
      newValue: null,
      timestamp: Date.now(),
      source: 'UnifiedStateStore'
    });
  }

  async exists(key: StateKey): Promise<boolean> {
    const value = await this.load(key);
    return value !== null;
  }

  async keys(): Promise<StateKey[]> {
    // 获取所有存储的键
    const storageKeys = await this._getAllStorageKeys();
    const cacheKeys = Array.from(this._cache.keys());
    
    // 合并并去重
    const allKeys = new Set([...storageKeys, ...cacheKeys]);
    return Array.from(allKeys);
  }

  async clear(): Promise<void> {
    // 清空缓存
    this._cache.clear();
    
    // 清空持久化存储
    await this._clearStorage();
    
    this._logger.info('状态存储已清空');
  }

  subscribe<T>(key: StateKey, callback: (event: StateChangeEvent<T>) => void): StateSubscription {
    const subscription = this._changeSubject
      .pipe(
        filter(event => event.key === key),
        map(event => event as StateChangeEvent<T>)
      )
      .subscribe(callback);
    
    return {
      unsubscribe: () => subscription.unsubscribe()
    };
  }

  subscribeAll(callback: (event: StateChangeEvent) => void): StateSubscription {
    const subscription = this._changeSubject.subscribe(callback);
    
    return {
      unsubscribe: () => subscription.unsubscribe()
    };
  }

  getChangeStream<T>(key: StateKey): Observable<StateChangeEvent<T>> {
    return this._changeSubject.pipe(
      filter(event => event.key === key),
      map(event => event as StateChangeEvent<T>)
    );
  }

  async withTransaction<T>(operation: (ctx: TransactionContext) => Promise<T>): Promise<T> {
    const context = new TransactionContextImpl(this, this._config.transactionTimeout);
    
    try {
      const result = await operation(context);
      await context.commit();
      return result;
    } catch (error) {
      if (!context.isCompleted()) {
        context.rollback();
      }
      throw error;
    }
  }

  async batch(operations: Array<{ type: 'save' | 'delete', key: StateKey, data?: any }>): Promise<void> {
    await this.withTransaction(async (ctx) => {
      for (const op of operations) {
        if (op.type === 'save') {
          ctx.save(op.key, op.data);
        } else if (op.type === 'delete') {
          ctx.delete(op.key);
        }
      }
    });
  }

  async getStats(): Promise<StateStoreStats> {
    const keys = await this.keys();
    const totalSize = await this._calculateTotalSize();
    
    return {
      totalKeys: keys.length,
      totalSize,
      cacheHitRate: this._cacheRequests > 0 ? this._cacheHits / this._cacheRequests : 0,
      lastPersistTime: this._lastPersistTime,
      transactionCount: this._transactionCount,
      failedTransactionCount: this._failedTransactionCount
    };
  }

  // 内部方法
  async directSave<T>(key: StateKey, data: T): Promise<void> {
    const oldValue = this.getCachedValue(key);
    
    const item: StorageItem<T> = {
      value: data,
      timestamp: Date.now(),
      version: (this._cache.get(key)?.version ?? 0) + 1
    };
    
    // 更新缓存
    this._updateCache(key, item);
    
    // 持久化到存储
    await this._saveToStorage(key, data);
    
    // 发出变更事件
    this._emitChangeEvent({
      key,
      oldValue,
      newValue: data,
      timestamp: Date.now(),
      source: 'UnifiedStateStore'
    });
  }

  async directDelete(key: StateKey): Promise<void> {
    this._cache.delete(key);
    await this._deleteFromStorage(key);
  }

  getCachedValue<T>(key: StateKey): T | null {
    const item = this._cache.get(key);
    return item ? item.value as T : null;
  }

  incrementTransactionCount(): void {
    this._transactionCount++;
  }

  incrementFailedTransactionCount(): void {
    this._failedTransactionCount++;
  }

  private _updateCache<T>(key: StateKey, item: StorageItem<T>): void {
    // 检查缓存大小限制
    if (this._cache.size >= this._config.maxCacheSize && !this._cache.has(key)) {
      // 移除最旧的项
      const oldestKey = this._findOldestCacheKey();
      if (oldestKey) {
        this._cache.delete(oldestKey);
      }
    }
    
    this._cache.set(key, item);
  }

  private _findOldestCacheKey(): StateKey | null {
    let oldestKey: StateKey | null = null;
    let oldestTime = Infinity;
    
    for (const [key, item] of this._cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }

  private async _handleConflict<T>(
    key: StateKey,
    existing: T,
    newValue: T,
    resolution: ConflictResolution
  ): Promise<void> {
    switch (resolution) {
      case ConflictResolution.KEEP_EXISTING:
        // 不做任何操作
        break;
      case ConflictResolution.MERGE:
        const merged = this._mergeValues(existing, newValue);
        await this.directSave(key, merged);
        break;
      case ConflictResolution.FAIL:
        throw new Error(`状态冲突: 键 "${key}" 已存在`);
      default:
        await this.directSave(key, newValue);
    }
  }

  private _mergeValues<T>(existing: T, newValue: T): T {
    // 简单的对象合并策略
    if (typeof existing === 'object' && typeof newValue === 'object' && 
        existing !== null && newValue !== null) {
      return { ...existing as any, ...newValue as any };
    }
    
    // 对于非对象类型，使用新值
    return newValue;
  }

  private _emitChangeEvent(event: StateChangeEvent): void {
    this._changeSubject.next(event);
  }

  private async _persistCache(): Promise<void> {
    try {
      for (const [key, item] of this._cache.entries()) {
        await this._saveToStorage(key, item.value);
      }
      this._lastPersistTime = Date.now();
    } catch (error) {
      this._logger.error('缓存持久化失败', error);
    }
  }

  private async _saveToStorage(key: StateKey, data: any): Promise<void> {
    try {
      let serializedData = JSON.stringify(data);
      
      if (this._config.enableEncryption) {
        serializedData = await this._securityService.encryptData(serializedData);
      }
      
      await chrome.storage.local.set({ [key]: serializedData });
    } catch (error) {
      this._logger.error(`保存到存储失败: ${key}`, error);
      throw error;
    }
  }

  private async _loadFromStorage(key: StateKey): Promise<any> {
    try {
      const result = await chrome.storage.local.get(key);
      if (!(key in result)) {
        return null;
      }
      
      let data = result[key];
      
      if (this._config.enableEncryption && typeof data === 'string') {
        const decrypted = await this._securityService.decryptData(data);
        if (decrypted) {
          data = decrypted;
        }
      }
      
      return typeof data === 'string' ? JSON.parse(data) : data;
    } catch (error) {
      this._logger.error(`从存储加载失败: ${key}`, error);
      throw error;
    }
  }

  private async _deleteFromStorage(key: StateKey): Promise<void> {
    try {
      await chrome.storage.local.remove(key);
    } catch (error) {
      this._logger.error(`从存储删除失败: ${key}`, error);
      throw error;
    }
  }

  private async _getAllStorageKeys(): Promise<string[]> {
    try {
      const result = await chrome.storage.local.get(null);
      return Object.keys(result);
    } catch (error) {
      this._logger.error('获取存储键失败', error);
      return [];
    }
  }

  private async _clearStorage(): Promise<void> {
    try {
      await chrome.storage.local.clear();
    } catch (error) {
      this._logger.error('清空存储失败', error);
      throw error;
    }
  }

  private async _calculateTotalSize(): Promise<number> {
    try {
      const result = await chrome.storage.local.get(null);
      const totalSize = JSON.stringify(result).length;
      return totalSize;
    } catch (error) {
      this._logger.error('计算存储大小失败', error);
      return 0;
    }
  }
}