# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 任务管理元逻辑

### 用户要求的任务开始前，需要

- 查看 `.claude-memory/tasks` 目录中的任务文件，通过文件名前缀快速识别状态：
  - `PENDING_TASK_*` - 待开始的任务
  - `ACTIVE_TASK_*` - 进行中的任务  
  - `DONE_TASK_*` - 已完成的任务
  - `FAILED_TASK_*` - 失败的任务
- 如果是新任务，使用 `PENDING_TASK_{编号}_{简述}.md` 格式创建文件
- 开始任务时，将文件重命名为 `ACTIVE_TASK_{编号}_{简述}.md`

### 任务执行中

- 每完成一个重要的子任务后，立即更新对应的任务记录文件
- 在执行关键操作前，检查任务记录文件中的相关注意事项和约束条件
- 遇到问题时，在任务记录文件中记录问题和解决方案

### 任务完成后

- **成功完成**: 将任务文件重命名为 `DONE_TASK_{编号}_{简述}.md`，标记状态为"DONE"
- **遇到失败**: 将任务文件重命名为 `FAILED_TASK_{编号}_{简述}.md`，标记状态为"FAILED"
- **主动取消**: 将任务文件重命名为 `CANCELLED_TASK_{编号}_{简述}.md`，标记状态为"CANCELLED"
- 记录最终结果、失败原因或取消原因
- 如有必要，更新 `GENERAL_RULES.md` 中的项目规则

## 核心工作规则

- 始终遵循 `.claude-memory/GENERAL_RULES.md` 中定义的项目规则
- **必须使用中文与用户对话**
- 优先编辑现有文件，避免创建新文件
- 开发命令和架构信息参考 `.claude-memory/DEVELOPMENT_GUIDE.md` 和 `PROJECT_ARCHITECTURE.md`

## 状态记忆系统

- 当任务必须使用相关信息时，可从对应文件中查看

### 通用文档

- `.claude-memory/GENERAL_RULES.md`：项目通用规则和约束
- `.claude-memory/PROJECT_ARCHITECTURE.md`：项目架构和技术栈信息
- `.claude-memory/DEVELOPMENT_GUIDE.md`：开发指南和常用命令
- `.claude-memory/TASK_TEMPLATE.md`：新任务记录的模板

### 任务管理文件

任务文件使用状态前缀命名，便于快速识别：

- `PENDING_TASK_*.md`：待开始的任务
- `ACTIVE_TASK_*.md`：进行中的任务
- `DONE_TASK_*.md`：已完成的任务
- `FAILED_TASK_*.md`：失败的任务
- `CANCELLED_TASK_*.md`：已取消的任务

每次操作都应该参考和更新相应的记忆文件，确保工作的连续性和一致性。
