/**
 * WebSocket服务
 * 实时通信管理
 */

import { message } from 'antd'
import { useAuthStore } from '../stores/authStore'

export interface WebSocketMessage {
  type: string
  data?: any
  timestamp: string
}

export interface TaskUpdateMessage extends WebSocketMessage {
  type: 'task_update'
  data: {
    task_id: string
    status: string
    progress?: number
  }
}

export interface NotificationMessage extends WebSocketMessage {
  type: 'notification'
  data: {
    title: string
    content: string
    level: 'info' | 'success' | 'warning' | 'error'
  }
}

class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private messageHandlers: Map<string, Set<(data: any) => void>> = new Map()

  constructor() {
    this.connect()
  }

  private connect() {
    const { token } = useAuthStore.getState()
    
    if (!token) {
      console.warn('No auth token available for WebSocket connection')
      return
    }

    const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/api/v1/ws/connect?token=${token}`
    
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.scheduleReconnect()
    }
  }

  private handleOpen() {
    console.log('WebSocket connected')
    this.reconnectAttempts = 0
    this.startHeartbeat()
  }

  private handleMessage(event: MessageEvent) {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // 处理特定类型的消息
      switch (message.type) {
        case 'connection_established':
          console.log('WebSocket connection established')
          break
          
        case 'pong':
          // 心跳响应
          break
          
        case 'task_update':
          this.notifyHandlers('task_update', message.data)
          break
          
        case 'notification':
          this.handleNotification(message as NotificationMessage)
          break
          
        case 'system_message':
          message.info(message.data?.content || '系统消息')
          break
          
        default:
          this.notifyHandlers(message.type, message.data)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  private handleClose(event: CloseEvent) {
    console.log('WebSocket disconnected:', event.code, event.reason)
    this.stopHeartbeat()
    
    if (event.code !== 1000) { // 非正常关闭
      this.scheduleReconnect()
    }
  }

  private handleError(error: Event) {
    console.error('WebSocket error:', error)
  }

  private handleNotification(message: NotificationMessage) {
    const { title, content, level } = message.data
    
    switch (level) {
      case 'success':
        message.success(content)
        break
      case 'warning':
        message.warning(content)
        break
      case 'error':
        message.error(content)
        break
      default:
        message.info(content)
    }
    
    this.notifyHandlers('notification', message.data)
  }

  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts}`)
    
    setTimeout(() => {
      this.connect()
    }, this.reconnectInterval * this.reconnectAttempts)
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({
          type: 'ping',
          timestamp: new Date().toISOString(),
        })
      }
    }, 30000) // 30秒心跳
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private notifyHandlers(type: string, data: any) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error('Message handler error:', error)
        }
      })
    }
  }

  // 发送消息
  send(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // 订阅消息类型
  subscribe(type: string, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set())
    }
    this.messageHandlers.get(type)!.add(handler)
    
    return () => {
      this.unsubscribe(type, handler)
    }
  }

  // 取消订阅
  unsubscribe(type: string, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(type)
      }
    }
  }

  // 订阅任务更新
  subscribeToTaskUpdates(handler: (data: TaskUpdateMessage['data']) => void) {
    return this.subscribe('task_update', handler)
  }

  // 订阅通知
  subscribeToNotifications(handler: (data: NotificationMessage['data']) => void) {
    return this.subscribe('notification', handler)
  }

  // 请求任务状态
  requestTaskStatus(taskId: string) {
    this.send({
      type: 'task_status_request',
      task_id: taskId,
      timestamp: new Date().toISOString(),
    })
  }

  // 订阅任务
  subscribeToTask(taskId: string) {
    this.send({
      type: 'subscribe_task',
      task_id: taskId,
      timestamp: new Date().toISOString(),
    })
  }

  // 关闭连接
  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  // 获取连接状态
  get isConnected() {
    return this.ws?.readyState === WebSocket.OPEN
  }
}

// 创建全局WebSocket服务实例
export const wsService = new WebSocketService()

export default wsService
