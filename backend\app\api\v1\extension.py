"""
浏览器插件集成API
处理插件认证、数据同步、任务通信等功能
"""

from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Header, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.models.data import Data, DataType, DataStatus
from app.services.task_orchestrator import TaskOrchestratorService
from app.services.data_processor import DataProcessorService
from app.utils.logger import get_logger

router = APIRouter(prefix="/extension", tags=["浏览器插件"])
logger = get_logger(__name__)


class ExtensionAuthRequest(BaseModel):
    """插件认证请求"""

    extension_id: str
    version: str
    user_agent: str


class ExtensionAuthResponse(BaseModel):
    """插件认证响应"""

    success: bool
    session_token: str
    expires_in: int
    user_info: Dict[str, Any]
    platform_config: Dict[str, Any]


class DataSubmissionRequest(BaseModel):
    """数据提交请求"""

    task_id: Optional[str] = None
    data_type: DataType
    url: str
    title: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    screenshot: Optional[str] = None  # base64编码的截图
    metadata: Optional[Dict[str, Any]] = None


class TaskSyncRequest(BaseModel):
    """任务同步请求"""

    last_sync_time: Optional[str] = None
    extension_version: str


@router.post("/auth", response_model=ExtensionAuthResponse)
async def authenticate_extension(
    request: ExtensionAuthRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    插件认证
    验证插件身份并返回会话令牌
    """
    try:
        # 验证插件版本兼容性
        if not _is_compatible_version(request.version):
            raise HTTPException(
                status_code=400, detail=f"插件版本 {request.version} 不兼容，请更新插件"
            )

        # 生成会话令牌
        session_token = _generate_session_token(current_user.id, request.extension_id)

        # 获取平台配置
        platform_config = {
            "api_base_url": "/api/v1",
            "websocket_url": "/api/v1/ws/extension",
            "max_file_size": 10 * 1024 * 1024,  # 10MB
            "supported_data_types": [dt.value for dt in DataType],
            "heartbeat_interval": 30,  # 秒
        }

        # 记录插件连接
        logger.info(
            f"Extension authenticated: {request.extension_id} for user {current_user.username}"
        )

        return ExtensionAuthResponse(
            success=True,
            session_token=session_token,
            expires_in=3600,  # 1小时
            user_info={
                "id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
            },
            platform_config=platform_config,
        )

    except Exception as e:
        logger.error(f"Extension authentication failed: {e}")
        raise HTTPException(status_code=500, detail="认证失败")


@router.post("/data/submit")
async def submit_data(
    request: DataSubmissionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    x_extension_id: str = Header(..., alias="X-Extension-ID"),
):
    """
    提交数据
    插件向平台提交抓取的数据
    """
    try:
        data_processor = DataProcessorService(db)

        # 创建数据记录
        data = Data(
            name=request.title or f"来自 {request.url} 的数据",
            description=f"通过浏览器插件从 {request.url} 提取的数据",
            data_type=request.data_type,
            status=DataStatus.RAW,
            user_id=current_user.id,
            task_id=request.task_id,
            raw_data={
                "url": request.url,
                "title": request.title,
                "content": request.content,
                "screenshot": request.screenshot,
                "extracted_at": datetime.utcnow().isoformat(),
                "extension_id": x_extension_id,
            },
            metadata=request.metadata or {},
        )

        db.add(data)
        db.commit()
        db.refresh(data)

        # 如果关联了任务，更新任务进度
        if request.task_id:
            orchestrator = TaskOrchestratorService(db)
            await orchestrator.update_task_progress(
                request.task_id, message=f"收到来自插件的数据: {request.url}"
            )

        logger.info(f"Data submitted from extension: {data.id}")

        return {"success": True, "data_id": data.id, "message": "数据提交成功"}

    except Exception as e:
        logger.error(f"Data submission failed: {e}")
        raise HTTPException(status_code=500, detail="数据提交失败")


@router.get("/tasks/sync")
async def sync_tasks(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    last_sync_time: Optional[str] = Query(None),
    extension_version: str = Query(...),
):
    """
    同步任务
    获取用户的活跃任务列表
    """
    try:
        query = db.query(Task).filter(
            Task.user_id == current_user.id,
            Task.status.in_(
                [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]
            ),
        )

        # 如果提供了上次同步时间，只返回更新的任务
        if last_sync_time:
            from datetime import datetime

            sync_time = datetime.fromisoformat(last_sync_time.replace("Z", "+00:00"))
            query = query.filter(Task.updated_at > sync_time)

        tasks = query.order_by(Task.created_at.desc()).limit(50).all()

        task_list = []
        for task in tasks:
            task_list.append(
                {
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "config": task.config,
                    "progress_percentage": task.progress_percentage,
                    "created_at": task.created_at.isoformat(),
                    "updated_at": (
                        task.updated_at.isoformat() if task.updated_at else None
                    ),
                }
            )

        return {
            "success": True,
            "tasks": task_list,
            "sync_time": datetime.utcnow().isoformat(),
            "total_count": len(task_list),
        }

    except Exception as e:
        logger.error(f"Task sync failed: {e}")
        raise HTTPException(status_code=500, detail="任务同步失败")


@router.get("/tasks/{task_id}/urls")
async def get_task_urls(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    processed: Optional[bool] = Query(None),
):
    """
    获取任务URL列表
    返回任务中需要处理或已处理的URL
    """
    try:
        task = (
            db.query(Task)
            .filter(Task.id == task_id, Task.user_id == current_user.id)
            .first()
        )

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        urls = task.config.get("urls", [])

        # 获取已处理的URL
        processed_data = (
            db.query(Data)
            .filter(Data.task_id == task_id, Data.user_id == current_user.id)
            .all()
        )

        processed_urls = set()
        for data in processed_data:
            if data.raw_data and "url" in data.raw_data:
                processed_urls.add(data.raw_data["url"])

        result_urls = []
        for url in urls:
            is_processed = url in processed_urls

            # 根据processed参数过滤
            if processed is not None and is_processed != processed:
                continue

            result_urls.append({"url": url, "processed": is_processed})

        return {
            "success": True,
            "task_id": task_id,
            "urls": result_urls,
            "total_count": len(result_urls),
            "processed_count": len([u for u in result_urls if u["processed"]]),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get task URLs failed: {e}")
        raise HTTPException(status_code=500, detail="获取任务URL失败")


@router.post("/heartbeat")
async def extension_heartbeat(
    current_user: User = Depends(get_current_user),
    x_extension_id: str = Header(..., alias="X-Extension-ID"),
    x_extension_version: str = Header(..., alias="X-Extension-Version"),
):
    """
    插件心跳
    保持插件与平台的连接状态
    """
    try:
        # 记录心跳（可以存储到Redis中）
        from app.utils.redis_client import get_redis_client

        redis_client = await get_redis_client()

        heartbeat_key = f"extension_heartbeat:{current_user.id}:{x_extension_id}"
        heartbeat_data = {
            "user_id": current_user.id,
            "extension_id": x_extension_id,
            "extension_version": x_extension_version,
            "last_heartbeat": datetime.utcnow().isoformat(),
        }

        await redis_client.set_json(heartbeat_key, heartbeat_data, ex=60)  # 60秒过期

        return {
            "success": True,
            "server_time": datetime.utcnow().isoformat(),
            "next_heartbeat": 30,  # 下次心跳间隔（秒）
        }

    except Exception as e:
        logger.error(f"Extension heartbeat failed: {e}")
        raise HTTPException(status_code=500, detail="心跳失败")


@router.get("/config")
async def get_extension_config(current_user: User = Depends(get_current_user)):
    """
    获取插件配置
    返回插件运行所需的配置信息
    """
    try:
        config = {
            "data_extraction": {
                "auto_screenshot": True,
                "extract_text": True,
                "extract_links": False,
                "extract_images": False,
                "max_content_length": 100000,
            },
            "ui_settings": {
                "show_notifications": True,
                "auto_submit": False,
                "theme": "auto",
            },
            "performance": {
                "batch_size": 10,
                "delay_between_requests": 1000,
                "timeout": 30000,
            },
        }

        return {"success": True, "config": config}

    except Exception as e:
        logger.error(f"Get extension config failed: {e}")
        raise HTTPException(status_code=500, detail="获取配置失败")


# 辅助函数
def _is_compatible_version(version: str) -> bool:
    """检查插件版本兼容性"""
    try:
        # 简单的版本检查逻辑
        major, minor, patch = map(int, version.split("."))
        return major >= 1 and minor >= 0
    except:
        return False


def _generate_session_token(user_id: str, extension_id: str) -> str:
    """生成插件会话令牌"""
    import hashlib
    import time

    data = f"{user_id}:{extension_id}:{time.time()}"
    return hashlib.sha256(data.encode()).hexdigest()[:32]
