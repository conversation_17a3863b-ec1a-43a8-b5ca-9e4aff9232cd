/**
 * 任务相关类型定义
 * 与后端API保持一致的类型定义
 */

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

// 数据类型枚举
export enum DataType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  TABLE = 'TABLE',
  FORM = 'FORM',
  LINK = 'LINK',
  DOCUMENT = 'DOCUMENT',
  SCREENSHOT = 'SCREENSHOT',
  OTHER = 'OTHER',
}

// 数据状态枚举
export enum DataStatus {
  RAW = 'RAW',
  PROCESSING = 'PROCESSING',
  PROCESSED = 'PROCESSED',
  FAILED = 'FAILED',
  ARCHIVED = 'ARCHIVED',
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
  timestamp: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 任务创建请求类型
export interface CreateTaskRequest {
  name: string
  description?: string
  config: TaskConfigRequest
  priority?: TaskPriority
}

export interface TaskConfigRequest {
  urls: string[]
  maxPages?: number
  delay?: number
  timeout?: number
  userAgent?: string
  headers?: Record<string, string>
  cookies?: Record<string, string>
  proxy?: string
  screenshot?: boolean
  extractText?: boolean
  extractLinks?: boolean
  extractImages?: boolean
  customSelectors?: Record<string, string>
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string
  data?: any
  timestamp: string
}

export interface TaskUpdateMessage extends WebSocketMessage {
  type: 'task_update'
  data: {
    task_id: string
    status: TaskStatus
    progress?: number
  }
}

export interface NotificationMessage extends WebSocketMessage {
  type: 'notification'
  data: {
    title: string
    content: string
    level: 'info' | 'success' | 'warning' | 'error'
  }
}

// 任务指标类型
export interface TaskMetrics {
  task_id: string
  status: TaskStatus
  progress: number
  duration?: number
  created_at: string
  started_at?: string
  completed_at?: string
  error_message?: string
  performance_stats?: Record<string, any>
}

// 用户类型
export interface User {
  id: string
  username: string
  email: string
  full_name?: string
  avatar_url?: string
  is_active: boolean
  is_admin: boolean
  last_login?: string
  created_at: string
  updated_at?: string
}

// 数据类型
export interface Data {
  id: string
  name: string
  description?: string
  data_type: DataType
  status: DataStatus
  user_id: string
  task_id?: string
  raw_data?: any
  processed_data?: any
  metadata?: Record<string, any>
  file_path?: string
  file_size?: number
  file_type?: string
  processing_time?: number
  error_message?: string
  access_count: number
  last_accessed?: string
  created_at: string
  updated_at?: string
}
