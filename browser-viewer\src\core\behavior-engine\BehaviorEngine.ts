/**
 * 行为引擎 - 统一管理所有行为模拟功能
 */

import type {
  AgentEvent,
  TabId,
  TaskId
} from '@/shared/types';
import { BehaviorSubject, Subject } from 'rxjs';

import { MESSAGE_TYPES } from '@/shared/constants';
import { createLogger, getCurrentTimestamp } from '@/shared/utils';

import { BehaviorAnalyzer } from './BehaviorAnalyzer';
import { HumanBehaviorSimulator } from './HumanBehaviorSimulator';

export interface BehaviorEngineConfig {
  defaultProfile: keyof typeof import('@/shared/constants').BEHAVIOR_PROFILES;
  enableDistraction: boolean;
  enableAnalysis: boolean;
  adaptiveSpeed: boolean;
  humanDelay?: { min: number; max: number };
  pauseProbability?: number;
}

export class BehaviorEngine {
  private readonly _logger = createLogger('BehaviorEngine');
  
  // 核心组件
  private readonly _humanSimulator: HumanBehaviorSimulator;
  private readonly _behaviorAnalyzer: BehaviorAnalyzer;

  // 状态管理
  private readonly _isActive$ = new BehaviorSubject<boolean>(false);
  private readonly _events$ = new Subject<AgentEvent>();
  
  // 任务映射
  private readonly _taskTabMapping = new Map<TaskId, TabId>();
  private readonly _activeProfiles = new Map<TabId, string>();
  
  // 配置
  private readonly _config: BehaviorEngineConfig;
  
  constructor(
    config?: Partial<BehaviorEngineConfig>,
  ) {
    this._config = {
      defaultProfile: 'normal',
      enableDistraction: true,
      enableAnalysis: true,
      adaptiveSpeed: true,
      ...config
    };
    
    // 初始化组件
    this._humanSimulator = new HumanBehaviorSimulator(this._config.defaultProfile);
    this._behaviorAnalyzer = new BehaviorAnalyzer();

    this._logger.info('BehaviorEngine initialized', this._config);
  }
  
  start(): void {
    if (this._isActive$.value) {
      return;
    }
    
    this._logger.info('Starting BehaviorEngine');
    
    // 启动各个组件
    this._humanSimulator.start();

    if (this._config.enableAnalysis) {
      this._behaviorAnalyzer.start();
    }
    
    this._isActive$.next(true);
    
    this._emitEvent(MESSAGE_TYPES.SYSTEM_READY, {
      component: 'BehaviorEngine'
    });
    
    this._logger.info('BehaviorEngine started successfully');
  }
  
  stop(): void {
    if (!this._isActive$.value) {
      return;
    }
    
    this._logger.info('Stopping BehaviorEngine');
    
    try {
      // 停止各个组件
      this._humanSimulator.stop();
      this._behaviorAnalyzer.stop();
      
      // 清理任务映射
      this._taskTabMapping.clear();
      this._activeProfiles.clear();
      
      this._isActive$.next(false);
      
      this._emitEvent(MESSAGE_TYPES.SYSTEM_SHUTDOWN, {
        component: 'BehaviorEngine'
      });
      
      this._logger.info('BehaviorEngine stopped');
      
    } catch (error) {
      this._logger.error('Error stopping BehaviorEngine:', error);
      throw error;
    }
  }

  private _emitEvent(type: string, data: Record<string, unknown>): void {
    this._events$.next({
      type,
      data,
      source: 'BehaviorEngine',
      timestamp: getCurrentTimestamp()
    });
  }
}