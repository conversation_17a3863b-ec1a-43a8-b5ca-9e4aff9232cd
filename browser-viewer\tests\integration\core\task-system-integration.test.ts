/**
 * 任务系统集成测试
 * 测试 TaskScheduler 和相关核心组件的集成
 */
import { BehaviorSubject, Subject } from 'rxjs';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import type { Task } from '@/core/task-manager/Task';
import type { TaskSchedulerImpl } from '@/core/task-manager/TaskScheduler';

import { TEST_TIMEOUTS, TEST_URLS } from '../../fixtures/test-constants';
import { TestDataGenerator, TestUtils } from '../../utils/test-helpers';

// 使用 vi.mock 和异步工厂来处理异步依赖
vi.mock('@/core/task-manager/TaskScheduler', async (importOriginal) => {
  const original = await importOriginal<typeof import('@/core/task-manager/TaskScheduler')>();
  return {
    ...original,
    // 如果需要，这里可以覆盖模块的导出
  };
});

describe('任务系统集成测试', () => {
  let taskScheduler: TaskSchedulerImpl;
  let mockTasks: Task[];
  let availableSlots: BehaviorSubject<number>;

  beforeEach(async () => {
    // 动态导入模拟后的模块
    const { TaskSchedulerImpl } = await import('@/core/task-manager/TaskScheduler');
    // 确保导入的类被使用
    expect(TaskSchedulerImpl).toBeDefined();
    
    // 创建Subject用于测试
    const slotsChangedSubject = new Subject<void>();
    const taskEventsSubject = new Subject<any>();
    
    // 创建一个模拟实例
    taskScheduler = new (vi.fn(() => ({
      acquireSlot: vi.fn(),
      releaseSlot: vi.fn(),
      createTask: vi.fn(),
      slotsChanged$: slotsChangedSubject.asObservable(),
      taskEvents$: taskEventsSubject.asObservable(),
      // 根据需要添加其他方法的模拟
    })))() as unknown as TaskSchedulerImpl;

    // 为模拟实例提供具体的函数实现
    availableSlots = new BehaviorSubject<number>(2);
    
    vi.spyOn(taskScheduler, 'acquireSlot').mockImplementation(() => {
      if (availableSlots.value > 0) {
        availableSlots.next(availableSlots.value - 1);
        // 在模拟的 slotsChanged$ 上发出通知
        slotsChangedSubject.next();
        return true;
      }
      return false;
    });

    vi.spyOn(taskScheduler, 'releaseSlot').mockImplementation(() => {
      availableSlots.next(availableSlots.value + 1);
      slotsChangedSubject.next();
    });

    vi.spyOn(taskScheduler, 'createTask').mockImplementation((config) => {
      const task = TestUtils.createMockTask(config);
      vi.spyOn(task, 'start').mockImplementation(async () => {
        if (task.status$) {
          // 立即更新为running状态
          (task.status$ as any).next('running');
          
          // 模拟异步任务完成
          await new Promise<void>((resolve) => {
            setTimeout(() => {
              (task.status$ as any).next('completed');
              taskScheduler.releaseSlot();
              resolve();
            }, 5); // 缩短延迟时间
          });
        }
      });
      return task;
    });

    mockTasks = [
      TestUtils.createMockTask({ urls: [TEST_URLS.VALID], priority: 1 }),
      TestUtils.createMockTask({ urls: [TEST_URLS.HTTPS], priority: 5 }),
      TestUtils.createMockTask({ urls: [TEST_URLS.LOCAL], priority: 10 }),
    ];
    
    // 确保mockTasks被使用
    expect(mockTasks).toHaveLength(3);
  });

  afterEach(() => {
    vi.restoreAllMocks();
    TestUtils.cleanupTestEnvironment();
  });

  describe('任务创建与生命周期', () => {
    it('应该能够成功创建一个任务', () => {
      const config = TestUtils.createMockTaskConfig();
      const task = taskScheduler.createTask(config);
      expect(task).toBeDefined();
      expect(task._config).toEqual(config);
      expect(taskScheduler.createTask).toHaveBeenCalledWith(config);
    });

    it('当有可用槽位时，任务应该能够获取槽位并启动', async () => {
      const task = taskScheduler.createTask(TestUtils.createMockTaskConfig());
      
      const acquired = taskScheduler.acquireSlot();
      expect(acquired).toBe(true);

      await task.start();
      expect(task.start).toHaveBeenCalled();
    });

    it('当没有可用槽位时，任务应该无法获取槽位', () => {
      taskScheduler.acquireSlot(); // take first slot
      taskScheduler.acquireSlot(); // take second slot
      const acquired = taskScheduler.acquireSlot();
      expect(acquired).toBe(false);
    });

    it('任务完成后应该释放槽位', async () => {
      // 验证初始槽位数量
      expect(availableSlots.value).toBe(2);
      
      const task = taskScheduler.createTask(TestUtils.createMockTaskConfig());
      
      // 获取一个槽位
      const acquired = taskScheduler.acquireSlot();
      expect(acquired).toBe(true);
      expect(availableSlots.value).toBe(1); // 现在应该只有1个槽位
      
      // 启动任务（会在完成时释放槽位）
      await task.start();
      
      // 等待释放操作完成
      await TestUtils.sleep(50);
      
      // 验证槽位已被释放
      expect(availableSlots.value).toBe(2); // 应该恢复到2个槽位
    });
  });

  describe('批量任务处理', () => {
    it('应该处理大量并发任务而不崩溃', async () => {
      const taskCount = 20;
      const taskConfigs = Array.from({ length: taskCount }, (_, i) =>
        TestUtils.createMockTaskConfig({
          urls: [TestDataGenerator.randomUrl()],
          priority: TestDataGenerator.randomNumber(1, 10),
        })
      );

      const startTime = performance.now();
      
      const tasks = taskConfigs.map(config => taskScheduler.createTask(config));

      // Simulate running tasks when slots are available
      for (const task of tasks) {
        if (taskScheduler.acquireSlot()) {
          await task.start();
        }
      }

      const duration = performance.now() - startTime;

      expect(tasks).toHaveLength(taskCount);
      expect(duration).toBeLessThan(TEST_TIMEOUTS.LONG);
    });
  });

  describe('健壮性与错误处理', () => {
    it('在任务执行失败时，应该正确处理错误并最终释放资源', async () => {
      const failingTask = taskScheduler.createTask(TestUtils.createMockTaskConfig());
      
      vi.spyOn(failingTask, 'start').mockImplementation(async () => {
         // @ts-expect-error - status$ is on a mock object which doesn't have private fields typed
        if (failingTask.status$) failingTask.status$.next('running');
        throw new Error('Task execution failed');
      });

      taskScheduler.acquireSlot();

      await expect(failingTask.start()).rejects.toThrow('Task execution failed');
      
      // Assume scheduler logic catches this and releases the slot
      taskScheduler.releaseSlot();

      const canAcquireAgain = taskScheduler.acquireSlot();
      expect(canAcquireAgain).toBe(true);
    });
  });
});