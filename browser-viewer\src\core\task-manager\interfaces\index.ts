/**
 * TaskScheduler - 接口定义
 * 
 * TaskScheduler的独立的、可测试的服务接口
 */

import { AgentEvent, TabId, TaskConfig, TaskId, TaskMetrics } from '@/shared/types';
import { Observable } from 'rxjs';
import { Task } from '../Task';

// ============================================================================
// 槽位管理接口 - 负责浏览器标签页槽位的生命周期管理
// ============================================================================

export interface SlotState {
  id: number;
  tabId: TabId;
  isBusy: boolean;
  currentTask: Task | null;
  lastDomain: string | null;
}

export interface SlotManager {
  /**
   * 获取所有槽位状态
   */
  getAllSlots(): SlotState[];
  
  /**
   * 获取空闲的槽位
   */
  getAvailableSlots(): SlotState[];
  
  /**
   * 获取指定标签页的槽位
   */
  getSlotByTabId(tabId: TabId): SlotState | undefined;
  
  /**
   * 添加新的槽位
   */
  addSlots(count: number): Promise<SlotState[]>;
  
  /**
   * 移除指定的槽位
   */
  removeSlot(slotId: number): Promise<void>;
  
  /**
   * 标记槽位为忙碌状态
   */
  markSlotBusy(slotId: number, task: Task): void;
  
  /**
   * 标记槽位为空闲状态
   */
  markSlotFree(slotId: number, lastDomain?: string): void;
  
  /**
   * 关闭所有槽位
   */
  shutdownAllSlots(): Promise<void>;
  
  /**
   * 获取正在运行的任务数量
   */
  getRunningTasksCount(): number;

  /**
   * 根据最大并发数调整槽位数量
   */
  adjustSlotsForConcurrency(maxConcurrency: number, pendingTaskCount: number): Promise<void>;

  /**
   * 检查并执行平滑缩容
   */
  performSmoothScaleDown(maxConcurrency: number, justCompletedSlotId: number): Promise<void>;
}

// ============================================================================
// 任务状态管理接口 - 负责任务状态的持久化和恢复
// ============================================================================

export interface TaskStateManager {
  /**
   * 保存任务状态到存储
   */
  saveState(tasks: TaskMetrics[]): Promise<void>;
  
  /**
   * 从存储恢复任务状态
   */
  restoreState(): Promise<TaskConfig[]>;
  
  /**
   * 清除所有存储的状态
   */
  clearState(): Promise<void>;
  
  /**
   * 获取任务指标的Observable流
   */
  getTaskMetrics$(): Observable<TaskMetrics[]>;
  
  /**
   * 更新单个任务的指标
   */
  updateTaskMetrics(taskId: TaskId, metrics: Partial<TaskMetrics>): void;
  
  /**
   * 添加新任务指标
   */
  addTaskMetrics(taskId: TaskId, metrics: TaskMetrics): void;
  
  /**
   * 移除任务指标
   */
  removeTaskMetrics(taskId: TaskId): void;
  
  /**
   * 获取指定任务的指标
   */
  getTaskMetrics(taskId: TaskId): TaskMetrics | null;
  
  /**
   * 获取所有任务指标
   */
  getAllTaskMetrics(): TaskMetrics[];

  /**
   * 创建待处理任务指标
   */
  createPendingTaskMetrics(configs: TaskConfig[]): void;

  /**
   * 移除待处理任务指标
   */
  removePendingTaskMetrics(batchId: string, url: string): void;

  /**
   * 清理已完成的批处理任务
   */
  pruneBatches(maxCompletedBatches: number): void;

  /**
   * 根据批处理ID移除任务指标
   */
  removeTaskMetricsByBatchIds(batchIds: string[]): void;
}

// ============================================================================
// 批处理管理接口 - 负责批处理任务的组织和管理
// ============================================================================

export interface BatchId extends String {}

export interface BatchStatus {
  batchId: BatchId;
  totalTasks: number;
  completedTasks: number;
  runningTasks: number;
  failedTasks: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
}

export interface BatchManager {
  /**
   * 创建新的批处理任务
   */
  createBatch(configs: TaskConfig[]): BatchId;
  
  /**
   * 获取批处理状态
   */
  getBatchStatus(batchId: BatchId): BatchStatus | null;
  
  /**
   * 获取批处理中的所有任务配置
   */
  getBatchTasks(batchId: BatchId): TaskConfig[];
  
  /**
   * 启动指定批处理的所有任务
   */
  startBatch(batchId: BatchId): Promise<void>;
  
  /**
   * 暂停指定批处理的所有任务
   */
  pauseBatch(batchId: BatchId): Promise<void>;
  
  /**
   * 取消指定批处理的所有任务
   */
  cancelBatch(batchId: BatchId): Promise<void>;
  
  /**
   * 清理已完成的批处理任务
   */
  pruneBatches(maxCompletedBatches: number): void;
}

// ============================================================================
// 任务控制服务接口 - 负责单个任务的生命周期控制
// ============================================================================

export interface TaskControlService {
  /**
   * 暂停任务
   */
  pauseTask(taskId: TaskId): Promise<void>;
  
  /**
   * 恢复任务
   */
  resumeTask(taskId: TaskId): Promise<void>;
  
  /**
   * 取消任务
   */
  cancelTask(taskId: TaskId): void;
  
  /**
   * 获取任务实例
   */
  getTask(taskId: TaskId): Task | undefined;
  
  /**
   * 根据标签页ID获取任务
   */
  getTaskByTabId(tabId: TabId): Task | undefined;
  
  /**
   * 暂停所有任务
   */
  pauseAllTasks(): Promise<void>;
  
  /**
   * 清理所有非运行状态的任务
   */
  clearAllTasks(): void;

  /**
   * 添加任务到控制器
   */
  addTask(taskId: TaskId, task: Task): void;

  /**
   * 从控制器中移除任务
   */
  removeTask(taskId: TaskId): void;

  /**
   * 获取所有任务
   */
  getAllTasks(): Map<TaskId, Task>;
}

// ============================================================================
// 任务队列服务接口 - 负责任务队列的管理和调度算法
// ============================================================================

export interface TaskQueueService {
  /**
   * 添加任务到队列
   */
  enqueue(configs: TaskConfig[]): void;
  
  /**
   * 从队列中获取最适合指定槽位的任务
   */
  dequeue(slot: SlotState): TaskConfig | null;
  
  /**
   * 获取队列长度
   */
  getQueueLength(): number;
  
  /**
   * 清空队列
   */
  clearQueue(): void;
  
  /**
   * 将指定批处理的任务移到队列前端
   */
  prioritizeBatches(batchIds: string[]): void;
  
  /**
   * 从队列中移除指定批处理的任务
   */
  removeBatchTasks(batchIds: string[]): TaskConfig[];
  
  /**
   * 获取队列中的所有任务
   */
  getAllQueuedTasks(): TaskConfig[];

  /**
   * 向队列前端添加高优先级任务
   */
  prependTasks(configs: TaskConfig[]): void;
}

// ============================================================================
// 主任务调度器接口 - 核心调度逻辑的精简版本
// ============================================================================

export interface TaskScheduler {
  /**
   * 提交批处理任务
   */
  submitBatch(configs: TaskConfig[]): void;
  
  /**
   * 启动所有待处理任务
   */
  startAllPendingTasks(): Promise<void>;
  
  /**
   * 初始化调度器
   */
  initializeScheduler(): Promise<void>;
  
  /**
   * 关闭调度器
   */
  shutdown(): Promise<void>;
  
  /**
   * 任务指标流
   */
  get taskMetrics$(): Observable<TaskMetrics[]>;
  
  /**
   * 任务事件流
   */
  get taskEvents$(): Observable<AgentEvent>;

  // 兼容性方法 - 与原有接口保持一致
  pauseTask(taskId: TaskId): Promise<void>;
  resumeTask(taskId: TaskId): Promise<void>;
  cancelTask(taskId: TaskId): void;
  getTaskMetrics(taskId: TaskId): TaskMetrics | null;
  getTask(taskId: TaskId): Task | undefined;
  getAllTasks(): TaskMetrics[];
  getRunningTasksCount(): number;
  getTaskByTabId(tabId: number): Task | undefined;
  pauseAllTasks(): Promise<void>;
  clearAllTasks(): Promise<void>;
  startSelectedTasks(batchIds: string[]): Promise<void>;
  pauseSelectedTasks(batchIds: string[]): Promise<void>;
  clearSelectedTasks(batchIds: string[]): Promise<void>;
}

// ============================================================================
// 任务提交服务接口 - 专门负责接收和预处理新任务
// ============================================================================

export interface TaskSubmissionService {
  /**
   * 提交批处理任务
   */
  submitBatch(configs: TaskConfig[]): void;
  
  /**
   * 验证任务配置
   */
  validateTaskConfig(config: TaskConfig): boolean;
  
  /**
   * 预处理任务配置（如生成ID、设置默认值等）
   */
  preprocessTaskConfig(config: TaskConfig): TaskConfig;
}

// ============================================================================
// 任务查询服务接口 - 提供任务状态查询功能
// ============================================================================

export interface TaskQueryService {
  /**
   * 获取任务指标
   */
  getTaskMetrics(taskId: TaskId): TaskMetrics | null;
  
  /**
   * 获取所有任务
   */
  getAllTasks(): TaskMetrics[];
  
  /**
   * 获取正在运行的任务数量
   */
  getRunningTasksCount(): number;
  
  /**
   * 根据批处理ID获取任务
   */
  getTasksByBatchId(batchId: string): TaskMetrics[];
  
  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: string): TaskMetrics[];
}

// ============================================================================
// 批量操作服务接口 - 处理多任务的批量操作
// ============================================================================

export interface BatchOperationService {
  /**
   * 启动指定批处理的任务
   */
  startSelectedTasks(batchIds: string[]): Promise<void>;
  
  /**
   * 暂停指定批处理的任务
   */
  pauseSelectedTasks(batchIds: string[]): Promise<void>;
  
  /**
   * 清理指定批处理的任务
   */
  clearSelectedTasks(batchIds: string[]): void;
}