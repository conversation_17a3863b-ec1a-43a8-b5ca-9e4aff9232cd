import { STORAGE_KEYS } from '@/shared/constants';
import sanitizeHtml from 'sanitize-html';

// 全局常量，用于模式切换
declare const __DEV_MODE__: boolean;
const DEV_MODE = typeof __DEV_MODE__ !== 'undefined' ? __DEV_MODE__ : true;

/**
 * 提供安全相关功能的服务，包括内容净化和数据加密。
 * 这是一个单例服务。
 */
export class SecurityService {
  private static _instance: SecurityService;
  private _cryptoKey?: CryptoKey;
  private _keyInitializationPromise: Promise<void> | null = null;

  private constructor() {
    // 异步初始化将在首次需要时触发
  }

  public static getInstance(): SecurityService {
    if (!SecurityService._instance) {
      SecurityService._instance = new SecurityService();
    }
    return SecurityService._instance;
  }

  /**
   * 确保加密密钥已初始化。此方法会处理并发调用。
   */
  private async _ensureKeyInitialized(): Promise<void> {
    if (this._cryptoKey) {
      return;
    }
    this._keyInitializationPromise ??= this._initializeCryptoKey();
    await this._keyInitializationPromise;
  }

  /**
   * 初始化安全服务，包括加密密钥。
   * 此方法通过调用ensureKeyInitialized来预热密钥。
   */
  public async initialize(): Promise<void> {
    await this._ensureKeyInitialized();
  }

  /**
   * 初始化用于对称加密的密钥。
   * 在开发模式下，使用一个固定的、可预测的密钥以方便调试。
   * 在生产模式下，密钥将从 chrome.storage.session 中加载，如果不存在则创建并保存。
   */
  private async _initializeCryptoKey(): Promise<void> {
    // 在非浏览器环境（如测试）或Service Worker中，应使用 self 检查 WebCrypto API
    if (typeof self === 'undefined' || !self.crypto?.subtle) {
      console.warn('WebCrypto API not available. Skipping key initialization.');
      return;
    }

    // 在开发模式下，使用固定的密钥来保证重载时的一致性
    if (DEV_MODE) {
      console.warn('DEV_MODE: 正在使用固定的、不安全密钥进行开发。请勿在生产环境中使用。');
      // 使用一个32字节的字符串作为原始密钥材料
      const fixedKeyMaterial = new TextEncoder().encode('a-very-secret-dev-key-for-agent!');
      this._cryptoKey = await self.crypto.subtle.importKey(
        'raw',
        fixedKeyMaterial,
        { name: 'AES-GCM' },
        true,
        ['encrypt', 'decrypt']
      );
      return;
    }

    // --- 生产环境逻辑 ---
    try {
      const storedKeyData = await chrome.storage.session.get(STORAGE_KEYS.SESSION_KEY);
      const keyArray: unknown = storedKeyData[STORAGE_KEYS.SESSION_KEY];

      if (keyArray && Array.isArray(keyArray) && keyArray.length > 0) {
        // 从存储的数组恢复密钥
        this._cryptoKey = await self.crypto.subtle.importKey(
          'raw',
          new Uint8Array(keyArray),
          { name: 'AES-GCM' },
          true,
          ['encrypt', 'decrypt']
        );
      } else {
        // 生成新密钥并存储为数组
        this._cryptoKey = await self.crypto.subtle.generateKey(
          {
            name: 'AES-GCM',
            length: 256,
          },
          true, // 可导出
          ['encrypt', 'decrypt']
        );
        const exportedKey = await self.crypto.subtle.exportKey('raw', this._cryptoKey);
        // 将 ArrayBuffer 转换为普通数组进行存储
        const keyAsArray = Array.from(new Uint8Array(exportedKey));
        await chrome.storage.session.set({ [STORAGE_KEYS.SESSION_KEY]: keyAsArray });
      }
    } catch (error) {
      console.error('CryptoKey initialization/loading failed:', error);
      // 在生产环境中，密钥失败是严重问题，向上抛出
      throw new Error(`CryptoKey initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 使用sanitize-html净化HTML字符串，移除潜在的恶意内容。
   * @param dirtyHtml - 未经处理的HTML字符串。
   * @returns 净化后的HTML字符串。
   */
  public sanitizeContent(dirtyHtml: string): string {
    return sanitizeHtml(dirtyHtml, {
      allowedTags: ['p', 'h1', 'h2', 'h3', 'ul', 'ol', 'li', 'img'],
      // 默认情况下，sanitize-html 会移除所有 script, style 等危险标签
      // 同时它有一个安全的默认属性列表 (e.g. for img: src, alt, title)
      // 这比黑名单更安全，并能满足需求
      allowedAttributes: {
        img: ['src', 'alt', 'title', 'width', 'height'],
      },
    });
  }

  /**
   * 加密文本数据。
   * @param plaintext - 需要加密的明文。
   * @returns 加密后的密文（Base64编码）。
   */
  public async encryptData(plaintext: string): Promise<string | null> {
    await this._ensureKeyInitialized();
    if (!this._cryptoKey) {
      console.error('CryptoKey is not available for encryption.');
      return null;
    }
    try {
      const iv = self.crypto.getRandomValues(new Uint8Array(12));
      const encodedText = new TextEncoder().encode(plaintext);

      const ciphertext = await self.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this._cryptoKey,
        encodedText
      );
      
      const combined = new Uint8Array(iv.length + ciphertext.byteLength);
      combined.set(iv, 0);
      combined.set(new Uint8Array(ciphertext), iv.length);

      return self.btoa(String.fromCharCode.apply(null, Array.from(combined)));
    } catch (error) {
      console.error('Encryption failed:', error);
      return null;
    }
  }

  /**
   * 解密文本数据。
   * @param ciphertextBase64 - 加密后的密文（Base64编码）。
   * @returns 解密后的明文。
   */
  public async decryptData(ciphertextBase64: string): Promise<string | null> {
    await this._ensureKeyInitialized();
    if (!this._cryptoKey) {
      console.error('CryptoKey is not available for decryption.');
      // 向上抛出错误以通知调用者
      throw new Error('CryptoKey is not available for decryption.');
    }
    try {
      const combined = new Uint8Array(Array.from(self.atob(ciphertextBase64), c => c.charCodeAt(0)));
      const iv = combined.slice(0, 12);
      const ciphertext = combined.slice(12);
      
      const decrypted = await self.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this._cryptoKey,
        ciphertext
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      // 提供更明确的错误信息
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
} 