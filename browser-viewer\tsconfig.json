{"compilerOptions": {"target": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable", "WebWorker"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "module": "ESNext", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/services/*": ["src/services/*"], "@/shared/*": ["src/shared/*"], "@/types/*": ["src/types/*"]}, "types": ["vite/client", "vitest/globals", "chrome", "node", "webextension-polyfill", "tailwindcss"]}, "include": ["src/**/*", "tests/**/*", "vite.config.ts", "manifest.config.ts", "tailwind.config.ts", "eslint.config.js"], "exclude": ["node_modules", "dist", "coverage"]}