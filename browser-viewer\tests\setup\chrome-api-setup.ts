/**
 * Chrome Extension API Mock 设置文件
 * 模拟 Chrome Extension APIs 用于测试
 */

import { vi } from 'vitest';

// Mock chrome.tabs API
const mockTabs = {
  create: vi.fn().mockImplementation((createProperties) => {
    const tabId = Math.floor(Math.random() * 1000) + 1;
    const tab = { 
      id: tabId, 
      url: createProperties?.url ?? 'about:blank',
      status: 'complete',
      active: createProperties?.active ?? false,
      windowId: 1,
      index: 0,
      highlighted: false,
      pinned: false,
      incognito: false,
      selected: createProperties?.active ?? false,
      discarded: false,
      autoDiscardable: true,
      title: 'Test Tab',
      width: 1920,
      height: 1080
    };
    return Promise.resolve(tab);
  }),
  update: vi.fn().mockResolvedValue({ id: 1 }),
  remove: vi.fn().mockResolvedValue(undefined),
  query: vi.fn().mockResolvedValue([{ id: 1, url: 'about:blank' }]),
  get: vi.fn().mockImplementation((tabId) => {
    return Promise.resolve({ 
      id: tabId, 
      url: 'https://example.com',
      status: 'complete',
      active: true,
      windowId: 1,
      index: 0,
      highlighted: false,
      pinned: false,
      incognito: false,
      selected: true,
      discarded: false,
      autoDiscardable: true,
      title: 'Test Tab',
      width: 1920,
      height: 1080
    });
  }),
  captureVisibleTab: vi.fn().mockResolvedValue('data:image/png;base64,mock-image-data'),
  executeScript: vi.fn().mockResolvedValue([{ result: 'mock-result' }]),
  insertCSS: vi.fn().mockResolvedValue(undefined),
  removeCSS: vi.fn().mockResolvedValue(undefined),
  onCreated: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onUpdated: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onRemoved: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.runtime API
const mockRuntime = {
  sendMessage: vi.fn().mockResolvedValue({ success: true }),
  connect: vi.fn().mockReturnValue({
    postMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn(),
    },
    onDisconnect: {
      addListener: vi.fn(),
      removeListener: vi.fn(),
    },
  }),
  getURL: vi.fn().mockImplementation((path: string) => `chrome-extension://mock-id/${path}`),
  getManifest: vi.fn().mockReturnValue({
    version: '1.0.0',
    name: 'Mock Extension',
  }),
  onMessage: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onConnect: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onStartup: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onInstalled: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  id: 'mock-extension-id',
  lastError: null,
};

// Mock chrome.storage API
const mockStorage = {
  local: {
    get: vi.fn().mockImplementation((keys) => {
      const result: Record<string, any> = {};
      // 为privacy settings提供默认值
      const defaultValues: Record<string, any> = {
        'agent:privacy_settings': {
          dataMasking: true,
          excludeSensitiveElements: true,
          anonymizeUserData: true,
          enableDataCollection: false
        }
      };
      
      if (typeof keys === 'string') {
        result[keys] = defaultValues[keys] ?? null;
      } else if (Array.isArray(keys)) {
        keys.forEach(key => result[key] = defaultValues[key] ?? null);
      } else if (typeof keys === 'object' && keys !== null) {
        Object.keys(keys).forEach(key => {
          result[key] = defaultValues[key] !== undefined ? defaultValues[key] : keys[key];
        });
      }
      return Promise.resolve(result);
    }),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined),
    getBytesInUse: vi.fn().mockResolvedValue(0),
    onChanged: {
      addListener: vi.fn(),
      removeListener: vi.fn(),
      hasListener: vi.fn(),
    },
  },
  session: {
    get: vi.fn().mockResolvedValue({}),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined),
  },
  sync: {
    get: vi.fn().mockResolvedValue({}),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined),
    clear: vi.fn().mockResolvedValue(undefined),
  },
  onChanged: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.scripting API
const mockScripting = {
  executeScript: vi.fn().mockResolvedValue([{ result: 'mock-result' }]),
  insertCSS: vi.fn().mockResolvedValue(undefined),
  removeCSS: vi.fn().mockResolvedValue(undefined),
  registerContentScripts: vi.fn().mockResolvedValue(undefined),
  unregisterContentScripts: vi.fn().mockResolvedValue(undefined),
  getRegisteredContentScripts: vi.fn().mockResolvedValue([]),
};

// Mock chrome.offscreen API
const mockOffscreen = {
  createDocument: vi.fn().mockResolvedValue(undefined),
  closeDocument: vi.fn().mockResolvedValue(undefined),
  hasDocument: vi.fn().mockResolvedValue(false),
};

// Mock chrome.action API
const mockAction = {
  setBadgeText: vi.fn().mockResolvedValue(undefined),
  setBadgeBackgroundColor: vi.fn().mockResolvedValue(undefined),
  setIcon: vi.fn().mockResolvedValue(undefined),
  setTitle: vi.fn().mockResolvedValue(undefined),
  setPopup: vi.fn().mockResolvedValue(undefined),
  enable: vi.fn().mockResolvedValue(undefined),
  disable: vi.fn().mockResolvedValue(undefined),
  onClicked: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.permissions API
const mockPermissions = {
  request: vi.fn().mockResolvedValue(true),
  remove: vi.fn().mockResolvedValue(true),
  contains: vi.fn().mockResolvedValue(true),
  getAll: vi.fn().mockResolvedValue({
    permissions: ['tabs', 'storage', 'activeTab'],
    origins: ['<all_urls>'],
  }),
  onAdded: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onRemoved: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.contextMenus API
const mockContextMenus = {
  create: vi.fn().mockReturnValue('mock-menu-id'),
  update: vi.fn().mockResolvedValue(undefined),
  remove: vi.fn().mockResolvedValue(undefined),
  removeAll: vi.fn().mockResolvedValue(undefined),
  onClicked: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.alarms API
const mockAlarms = {
  create: vi.fn().mockResolvedValue(undefined),
  get: vi.fn().mockResolvedValue(null),
  getAll: vi.fn().mockResolvedValue([]),
  clear: vi.fn().mockResolvedValue(true),
  clearAll: vi.fn().mockResolvedValue(true),
  onAlarm: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// Mock chrome.notifications API
const mockNotifications = {
  create: vi.fn().mockResolvedValue('mock-notification-id'),
  update: vi.fn().mockResolvedValue(true),
  clear: vi.fn().mockResolvedValue(true),
  getAll: vi.fn().mockResolvedValue({}),
  getPermissionLevel: vi.fn().mockResolvedValue('granted'),
  onClicked: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
  onClosed: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
    hasListener: vi.fn(),
  },
};

// 创建完整的 chrome 对象
const mockChrome = {
  tabs: mockTabs,
  runtime: mockRuntime,
  storage: mockStorage,
  scripting: mockScripting,
  offscreen: mockOffscreen,
  action: mockAction,
  permissions: mockPermissions,
  contextMenus: mockContextMenus,
  alarms: mockAlarms,
  notifications: mockNotifications,
};

// 设置全局 chrome 对象
Object.defineProperty(globalThis, 'chrome', {
  value: mockChrome,
  writable: true,
  configurable: true,
});

// 设置 browser 对象 (WebExtensions API)
Object.defineProperty(globalThis, 'browser', {
  value: mockChrome,
  writable: true,
  configurable: true,
});

// 导出 mock 对象，供测试文件使用
export {
  mockAction, mockAlarms, mockChrome, mockContextMenus, mockNotifications, mockOffscreen, mockPermissions, mockRuntime, mockScripting, mockStorage, mockTabs
};

// 提供重置 mock 的工具函数
export function resetChromeMocks() {
  vi.clearAllMocks();
  
  // 重置特定的 mock 状态
  mockRuntime.lastError = null;
  
  // 可以添加更多重置逻辑
}

// 提供创建模拟标签页的工具函数
export function createMockTab(overrides: Partial<chrome.tabs.Tab> = {}): chrome.tabs.Tab {
  return {
    id: 1,
    index: 0,
    windowId: 1,
    highlighted: false,
    active: true,
    pinned: false,
    incognito: false,
    selected: true,
    discarded: false,
    autoDiscardable: true,
    url: 'https://example.com',
    title: 'Mock Tab',
    favIconUrl: 'https://example.com/favicon.ico',
    status: 'complete',
    width: 1920,
    height: 1080,
    ...overrides,
  } as chrome.tabs.Tab;
}

// 提供创建模拟消息的工具函数
export function createMockMessage(type: string, data: any = {}) {
  return {
    type,
    data,
    timestamp: Date.now(),
    id: Math.random().toString(36).substring(2, 11),
    ...data,
  };
}