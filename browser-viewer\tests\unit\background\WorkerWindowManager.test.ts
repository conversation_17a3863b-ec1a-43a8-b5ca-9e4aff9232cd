/**
 * WorkerWindowManager 单元测试
 * 测试工作窗口管理器的核心功能
 */

import { WorkerWindowManager } from '@/background/WorkerWindowManager';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TEST_TIMEOUTS } from '../../fixtures/test-constants';
import { TestUtils } from '../../utils/test-helpers';

describe('WorkerWindowManager', () => {
  let workerWindowManager: WorkerWindowManager;
  let mockChrome: any;

  beforeEach(() => {
    // 重置单例实例
    (WorkerWindowManager as any)._instance = null;
    
    // Mock Chrome APIs
    mockChrome = {
      tabs: {
        create: vi.fn()
          .mockResolvedValueOnce({ id: 1, windowId: 100 })
          .mockResolvedValueOnce({ id: 2, windowId: 100 })
          .mockResolvedValueOnce({ id: 3, windowId: 100 })
          .mockResolvedValueOnce({ id: 4, windowId: 100 })
          .mockResolvedValueOnce({ id: 5, windowId: 100 })
          .mockResolvedValue({ id: 6, windowId: 100 }),
        remove: vi.fn().mockResolvedValue(undefined),
        update: vi.fn().mockResolvedValue(undefined),
        group: vi.fn().mockResolvedValue(1), // 添加 group API
        ungroup: vi.fn().mockResolvedValue(undefined), // 添加 ungroup API
        query: vi.fn().mockResolvedValue([{ id: 1, groupId: 1 }]), // 添加 query API
        onRemoved: {
          addListener: vi.fn(),
          removeListener: vi.fn()
        },
        get: vi.fn().mockResolvedValue({ id: 1, active: false })
      },
      tabGroups: {
        create: vi.fn().mockResolvedValue({ id: 1 }),
        get: vi.fn().mockResolvedValue({ id: 1, title: 'Worker Tasks' }),
        update: vi.fn().mockResolvedValue(undefined),
        remove: vi.fn().mockResolvedValue(undefined)
      },
      windows: {
        create: vi.fn().mockResolvedValue({ id: 100, tabs: [{ id: 1 }] }),
        remove: vi.fn().mockResolvedValue(undefined),
        update: vi.fn().mockResolvedValue(undefined)
      }
    };

    vi.stubGlobal('chrome', mockChrome);
    
    workerWindowManager = WorkerWindowManager.getInstance();
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = WorkerWindowManager.getInstance();
      const instance2 = WorkerWindowManager.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBe(workerWindowManager);
    });

    it('应该只创建一个实例', () => {
      const instances = Array.from({ length: 5 }, () => WorkerWindowManager.getInstance());
      
      instances.forEach(instance => {
        expect(instance).toBe(workerWindowManager);
      });
    });
  });

  describe('初始化', () => {
    it('应该成功初始化', () => {
      workerWindowManager.initialize();
      
      expect(mockChrome.tabs.onRemoved.addListener).toHaveBeenCalled();
    });

    it('应该防止重复初始化', () => {
      workerWindowManager.initialize();
      workerWindowManager.initialize();
      
      // 只应该添加一次监听器
      expect(mockChrome.tabs.onRemoved.addListener).toHaveBeenCalledTimes(1);
    });

    it('应该设置标签页移除监听器', () => {
      workerWindowManager.initialize();
      
      const listener = mockChrome.tabs.onRemoved.addListener.mock.calls[0][0];
      expect(typeof listener).toBe('function');
    });
  });

  describe('标签页创建', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该成功创建标签页', async () => {
      const options = { url: 'https://example.com' };
      
      const tab = await workerWindowManager.createTab(options);
      
      expect(tab).toEqual({ id: 1, windowId: 100 });
      expect(mockChrome.tabs.create).toHaveBeenCalledWith({
        ...options,
        active: false
      });
    });

    it('应该为创建的标签页分组', async () => {
      const options = { url: 'https://example.com' };
      
      await workerWindowManager.createTab(options);
      
      expect(mockChrome.tabs.group).toHaveBeenCalledWith({ tabIds: [1] });
    });

    it('应该处理标签页创建失败', async () => {
      // 重置并设置新的mock行为
      mockChrome.tabs.create.mockReset().mockRejectedValue(new Error('Create failed'));
      
      const url = 'https://example.com';
      
      await expect(workerWindowManager.createTab({ url }))
        .rejects.toThrow('Create failed');
    });

    it('应该使用现有的工作组', async () => {
      // 创建第一个标签页
      await workerWindowManager.createTab({ url: 'https://example1.com' });
      
      // 创建第二个标签页时应该使用相同的组
      await workerWindowManager.createTab({ url: 'https://example2.com' });
      
      // tabs.group 应该被调用两次，第一次创建组，第二次加入组
      expect(mockChrome.tabs.group).toHaveBeenCalledTimes(2);
    });
  });

  describe('标签页移除', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该成功移除标签页', async () => {
      const tab = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      expect(tab.id).toBeDefined();
      await workerWindowManager.removeTab(tab.id!);
      
      expect(mockChrome.tabs.remove).toHaveBeenCalledWith(tab.id);
    });

    it('应该处理标签页移除失败', async () => {
      mockChrome.tabs.remove.mockRejectedValue(new Error('Remove failed'));
      
      // Mock logger.error to prevent stderr output
      const mockError = vi.fn();
      vi.spyOn(workerWindowManager['_logger'], 'error').mockImplementation(mockError);
      
      const tab = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      expect(tab.id).toBeDefined();
      // 根据实现，移除失败不会抛错，只会记录日志
      await expect(workerWindowManager.removeTab(tab.id!))
        .resolves.not.toThrow();
        
      // 验证错误被记录
      expect(mockError).toHaveBeenCalledWith(`移除标签页 ${tab.id} 失败:`, new Error('Remove failed'));
    });

    it('应该在最后一个标签页移除时清理组', async () => {
      const tab = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      // 模拟标签页被移除的事件
      const listener = mockChrome.tabs.onRemoved.addListener.mock.calls[0][0];
      listener(tab.id);
      
      // 等待异步清理完成
      await TestUtils.sleep(10);
      
      // 实现中调用的是 ungroup 而不是 remove
      expect(mockChrome.tabs.ungroup).toHaveBeenCalled();
    });
  });

  describe('渲染模式管理', () => { // 注意，我们已经不设置无头模式和可视化模式这种区分了
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    // 注意：setMode 功能已被移除，因为我们不再区分无头模式和可视化模式

    it('应该处理标签组更新失败', async () => {
      mockChrome.tabGroups.update.mockRejectedValue(new Error('TabGroup update failed'));
      
      // 在创建标签页时tabGroups.update会被调用，应该抛出错误
      await expect(workerWindowManager.createTab({ url: 'https://example.com' }))
        .rejects.toThrow('TabGroup update failed');
    });
  });

  describe('资源清理', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该清理工作组', async () => {
      await workerWindowManager.createTab({ url: 'https://example.com' });
      
      // 调用私有方法进行测试
      await (workerWindowManager as any).cleanupGroup();
      
      expect(mockChrome.tabs.ungroup).toHaveBeenCalled();
    });

    it('应该处理组清理失败', async () => {
      mockChrome.tabs.ungroup.mockRejectedValue(new Error('Ungroup failed'));
      
      // Mock logger.warn to prevent stderr output
      const mockWarn = vi.fn();
      vi.spyOn(workerWindowManager['_logger'], 'warn').mockImplementation(mockWarn);
      
      await workerWindowManager.createTab({ url: 'https://example.com' });
      
      // 调用私有方法，应该不抛出错误
      await expect((workerWindowManager as any).cleanupGroup())
        .resolves.not.toThrow();
        
      // 验证警告被记录
      expect(mockWarn).toHaveBeenCalledWith(
        expect.stringContaining('解散标签组'),
        new Error('Ungroup failed')
      );
    });

    it('应该在组不存在时跳过清理', async () => {
      // 不创建任何标签页，直接清理
      await (workerWindowManager as any).cleanupGroup();
      
      expect(mockChrome.tabs.ungroup).not.toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该处理Chrome API不可用', async () => {
      vi.stubGlobal('chrome', undefined);
      
      const manager = WorkerWindowManager.getInstance();
      
      await expect(manager.createTab({ url: 'https://example.com' }))
        .rejects.toThrow();
    });

    it('应该处理标签组API错误', async () => {
      mockChrome.tabGroups.create.mockRejectedValue(new Error('TabGroup API failed'));
      
      // 应该仍然能创建标签页，即使分组失败
      const tab = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      expect(tab.id).toBe(1);
    });

    it('应该处理标签组不存在的情况', async () => {
      mockChrome.tabGroups.get.mockRejectedValue(new Error('Group not found'));
      
      const tab = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      expect(tab.id).toBe(1);
      // 应该创建新的组 (通过tabs.group而不是tabGroups.create)
      expect(mockChrome.tabs.group).toHaveBeenCalled();
    });
  });

  describe('性能考虑', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该复用工作组', async () => {
      const urls = Array.from({ length: 5 }, (_, i) => `https://example${i}.com`);
      
      for (const url of urls) {
        await workerWindowManager.createTab({ url });
      }
      
      // 只应该创建一个组（第一次调用tabs.group时创建组）
      // 检查tabGroups.update只被调用一次（创建组时）
      expect(mockChrome.tabGroups.update).toHaveBeenCalledTimes(1);
    });

    it('应该在合理时间内创建标签页', async () => {
      const measure = new TestUtils.PerformanceMeasure();
      
      await workerWindowManager.createTab({ url: 'https://example.com' });
      
      measure.mark('tab_creation_complete');
      expect(measure.getMeasurement('tab_creation_complete')).toBeLessThan(TEST_TIMEOUTS.SHORT);
    });

    it('应该支持并发标签页创建', async () => {
      const urls = Array.from({ length: 10 }, (_, i) => `https://example${i}.com`);
      
      const measure = new TestUtils.PerformanceMeasure();
      
      const promises = urls.map(url => workerWindowManager.createTab({ url }));
      await Promise.all(promises);
      
      measure.mark('concurrent_creation_complete');
      expect(measure.getMeasurement('concurrent_creation_complete')).toBeLessThan(TEST_TIMEOUTS.MEDIUM);
    });
  });

  describe('状态管理', () => {
    beforeEach(() => {
      workerWindowManager.initialize();
    });

    it('应该跟踪受管理的标签页', async () => {
      const tab1 = await workerWindowManager.createTab({ url: 'https://example1.com' });
      const tab2 = await workerWindowManager.createTab({ url: 'https://example2.com' });
      
      // 访问私有属性进行测试
      const managedTabs = (workerWindowManager as any)._managedTabIds;
      
      expect(managedTabs.has(tab1.id)).toBe(true);
      expect(managedTabs.has(tab2.id)).toBe(true);
      expect(managedTabs.size).toBe(2);
    });

    it('应该在标签页移除时更新状态', async () => {
      const tabId = await workerWindowManager.createTab({ url: 'https://example.com' });
      
      // 模拟标签页被移除的事件
      const listener = mockChrome.tabs.onRemoved.addListener.mock.calls[0][0];
      listener(tabId);
      
      const managedTabs = (workerWindowManager as any)._managedTabIds;
      expect(managedTabs.has(tabId)).toBe(false);
    });

    it('应该维护组ID状态', async () => {
      await workerWindowManager.createTab({ url: 'https://example.com' });
      
      const groupId = (workerWindowManager as any)._workerGroupId;
      expect(groupId).toBe(1);
    });
  });
});