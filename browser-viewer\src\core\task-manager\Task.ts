/**
 * 任务实体类
 */

import { ExceptionService } from '@/services/exception-service/ExceptionService';
import { StateSnapshot, TabState } from '@/services/exception-service/StateSnapshot';
import { NotificationService } from '@/services/notification-service/NotificationService';
import { PrivacyService } from '@/services/privacy-service';
import { SecurityService } from '@/services/security-service';
import { MESSAGE_TYPES } from '@/shared/constants';
import { CONTENT_SCRIPT_PATH } from '@/shared/constants/script-paths';
import {
  type ExceptionEvent,
  ExceptionType,
  ImageSegment,
  TabId,
  TaskConfig,
  TaskId,
  TaskStatus,
  Timestamp
} from '@/shared/types';
import { createLogger, getCurrentTimestamp, Logger } from '@/shared/utils';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { HumanBehaviorSimulator } from '../behavior-engine/HumanBehaviorSimulator';
import { CaptureSystem } from '../capture-system/CaptureSystem';

/**
 * 当任务因需要用户干预（如验证码）而暂停时抛出的特定错误。
 */
class PausedForInterventionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PausedForInterventionError';
  }
}

// 事件类型定义
export interface StatusChangeEvent {
  type: 'statusChange';
  payload: { newStatus: TaskStatus; oldStatus: TaskStatus };
}
export interface UrlProcessedEvent {
  type: 'urlProcessed';
  payload: { url: string; success: boolean; error?: string; data?: string; };
}

export type TaskEvent = StatusChangeEvent | UrlProcessedEvent;

export interface SerializedTaskState {
  id: TaskId;
  config: TaskConfig;
  batchId: string;
  status: TaskStatus;
  currentUrlIndex: number;
  processedUrls: string[];
  createdAt: Timestamp;
  startedAt?: Timestamp;
  pausedAt?: Timestamp;
  completedAt?: Timestamp;
  lastActivity: Timestamp;
}

export class Task {
  private readonly _logger: Logger;
  public readonly batchId: string;
  
  // 任务状态
  private readonly _status$ = new BehaviorSubject<TaskStatus>('pending' as TaskStatus);
  private readonly _events$ = new Subject<TaskEvent>();
  
  // 映射URL到会话ID，用于后续检索产物
  private readonly _urlSessionMap = new Map<string, string>();
  // 用于等待全页扫描完成的Promise解析器
  private readonly _pageScanCompletionPromises = new Map<string, (result: { success: boolean; data?: string; error?: string }) => void>();
  
  // 用于等待内容脚本准备就绪的Promise解析器
  private _contentScriptReadyPromise?: Promise<void>;
  private _contentScriptReadyResolver?: () => void;
  private _contentScriptReady: boolean = false;
  
  // 执行上下文
  private _tabId?: TabId;
  private _currentUrl?: string;
  private _currentUrlIndex: number = 0;
  
  // 时间跟踪
  private readonly _createdAt: Timestamp;
  private _startedAt?: Timestamp;
  private _pausedAt?: Timestamp;
  private _completedAt?: Timestamp;
  
  // 执行统计
  private _processedUrls: string[] = [];
  private _lastActivity: Timestamp;
  
  // 异常处理
  private _lastKnownGoodState: TabState | null = null;
  private _exceptionSubscription?: Subscription;
  
  // 核心执行组件
  private readonly _behaviorSimulator: HumanBehaviorSimulator;
  private readonly _captureSystem?: CaptureSystem;
  
  
  constructor(
    public readonly _id: TaskId,
    public readonly _config: TaskConfig,
    private readonly _exceptionService: ExceptionService | null,
    private readonly _stateSnapshot: StateSnapshot | null,
    private readonly _notificationService: NotificationService | null,
    private readonly _privacyService: PrivacyService | null,
    private readonly _securityService: SecurityService | null,
  ) {
    this._logger = createLogger(`Task-${_id}`);
    this._createdAt = getCurrentTimestamp();
    this._lastActivity = this._createdAt;
    this.batchId = _config.batchId!;

    this._behaviorSimulator = new HumanBehaviorSimulator(_config.behaviorProfile);

    // 初始化CaptureSystem - 为开发模式提供完整功能
    if (this._securityService && this._privacyService) {
      this._captureSystem = new CaptureSystem(this._privacyService);
      this._logger.info('CaptureSystem initialized with security and privacy services.');
    } else {
      this._logger.warn('CaptureSystem not initialized: missing security or privacy services. Running in simplified mode.');
    }

    this._logger.info(`Task ${_id} created with ${_config.urls.length} URLs`);
  }
  
  // 任务控制方法
  async start(tabId: TabId): Promise<void> {
    if (this._status$.value !== 'pending') {
      throw new Error(`Cannot start task in ${this._status$.value} state`);
    }
    
    this._tabId = tabId; // 任务执行的标签页由外部注入
    this._logger.info(`Starting task execution in assigned tab ${tabId}`);
    
    this._startedAt = getCurrentTimestamp();
    this._updateStatus('running');
    this._updateLastActivity(); // 确保开始时记录活动
    
    if (this._exceptionService) {
      this._exceptionSubscription = this._exceptionService.onException.subscribe(
        (event) => {
          void this._handleException(event);
        }
      );
    }
    
    try {
      await this._executeTask();
    } catch (error) {
      this._logger.error('Task execution failed:', error);
      this._updateStatus('failed');
    }
  }
  
  async pause(): Promise<void> {
    if (this._status$.value !== 'running') {
      throw new Error(`Cannot pause task in ${this._status$.value} state`);
    }
    
    this._logger.info('Pausing task');
    this._pausedAt = getCurrentTimestamp();
    this._updateStatus('paused');
    
    // 暂停当前执行
    if (this._tabId) {
      await this._pauseTabExecution(this._tabId);
      // 任务暂停时，停止视觉监控
      void this._sendMessageToTab(this._tabId, MESSAGE_TYPES.VISUAL_TASK_STOP_MONITORING);
    }
  }
  
  async resume(): Promise<void> {
    if (this._status$.value !== 'paused') {
      throw new Error(`Cannot resume task in ${this._status$.value} state`);
    }
    
    this._logger.info('Resuming task');
    this._pausedAt = undefined;
    this._updateStatus('running');
    
    // 恢复执行
    await this._continueExecution();
  }
  
  cancel(): void {
    const currentStatus = this._status$.value;
    if (currentStatus === 'completed' || currentStatus === 'cancelled') {
      return;
    }
    
    this._logger.info('Cancelling task');
    this._updateStatus('cancelled');
    
    // 任务取消时，停止视觉监控
    if (this._tabId) {
      void this._sendMessageToTab(this._tabId, MESSAGE_TYPES.VISUAL_TASK_STOP_MONITORING);
    }
    
    // 清理资源
    this._cleanup();
  }

  // 组件访问器
  getBehaviorSimulator(): HumanBehaviorSimulator {
    return this._behaviorSimulator;
  }

  getCaptureSystem(): CaptureSystem | undefined {
    return this._captureSystem;
  }

  // 健康检查
  async checkHealth(): Promise<boolean> {
    const now = getCurrentTimestamp();
    const timeSinceLastActivity = now - this._lastActivity;
    
    // 如果3分钟内没有活动，认为任务可能有问题
    if (timeSinceLastActivity > 3 * 60 * 1000) {
      this._logger.warn('Task appears to be inactive');
      return false;
    }
    
    // 检查标签页是否仍然存在
    if (this._tabId) {
      try {
        await chrome.tabs.get(this._tabId);
        return true;
      } catch {
        this._logger.warn('Task tab no longer exists');
        return false;
      }
    }
    
    return true;
  }
  
  // 获取任务信息
  getStatus(): TaskStatus {
    return this._status$.value;
  }
  
  getProgress(): number {
    return this._processedUrls.length / this._config.urls.length;
  }
  
  getCurrentUrl(): string | undefined {
    return this._currentUrl;
  }
  
  getProcessedUrls(): string[] {
    return [...this._processedUrls];
  }
  
  getRemainingUrls(): string[] {
    return this._config.urls.slice(this._currentUrlIndex);
  }
  
  getExecutionTime(): number {
    if (!this._startedAt) return 0;
    
    const endTime = this._completedAt ?? getCurrentTimestamp();
    return endTime - this._startedAt;
  }
  
  // Observable接口
  get status$(): Observable<TaskStatus> {
    return this._status$.asObservable();
  }
  
  get events$(): Observable<TaskEvent> {
    return this._events$.asObservable();
  }
  
  // 处理单个截图切片完成
  public async handleViewportCapture(sessionId: string, segment: ImageSegment): Promise<void> {
    if (!this._captureSystem) {
      this._logger.warn('CaptureSystem not available, cannot handle viewport capture.');
      return;
    }
    await this._captureSystem.addSlice(sessionId, segment);
  }

  public async handleCaptureCompleted(sessionId: string, imageSegment: ImageSegment): Promise<void> {
    if (!this._captureSystem) {
      this._logger.warn('Capture system not initialized. Ignoring capture.');
      return;
    }
    try {
      await this._captureSystem.addSlice(sessionId, imageSegment);
    } catch (error) {
      this._logger.error(`Failed to handle captured slice for session ${sessionId}:`, error);
    }
  }

  // 处理截图切片失败
  public handleCaptureFailed(sessionId: string, error: string): void {
    this._logger.error(`Capture of a slice failed for session ${sessionId}: ${error}`);
    // 通知 captureSystem 终止会话
    this._captureSystem?.cancelSession(sessionId, error);
  }

  // 处理页面扫描完成信号
  public handleFullPageScanCompleted(sessionId: string, result: { success: boolean; data?: string; error?: string }): void {
    const url = this._findUrlBySessionId(sessionId);
    if (url) {
      this._logger.info(`Received FULL_PAGE_SCAN_COMPLETED for session ${sessionId} (URL: ${url})`);
      const resolver = this._pageScanCompletionPromises.get(url);
      if (resolver) {
        resolver(result);
        this._pageScanCompletionPromises.delete(url);
      } else {
        this._logger.warn(`No pending promise resolver found for URL: ${url}`);
      }
    } else {
      this._logger.warn(`Could not find URL for completed session ID: ${sessionId}`);
    }
  }
  
  /**
   * 处理来自内容脚本的"准备就绪"信号。
   */
  public handleContentScriptReady(): void {
    this._logger.info(`内容脚本已在标签页 ${this._tabId} 中准备就绪。`);
    this._contentScriptReady = true;
    
    // 如果有正在等待的Promise，则解决它
    if (this._contentScriptReadyResolver) {
      this._contentScriptReadyResolver();
      // 清理resolver，防止重复调用
      this._contentScriptReadyResolver = undefined;
    }
  }
  
  // 状态持久化
  serialize(): SerializedTaskState {
    return {
      id: this._id,
      config: this._config,
      batchId: this.batchId,
      status: this._status$.value,
      currentUrlIndex: this._currentUrlIndex,
      processedUrls: this._processedUrls,
      createdAt: this._createdAt,
      startedAt: this._startedAt,
      pausedAt: this._pausedAt,
      completedAt: this._completedAt,
      lastActivity: this._lastActivity
    };
  }
  
  static deserialize(
    state: SerializedTaskState,
    exceptionService: ExceptionService | null,
    stateSnapshot: StateSnapshot | null,
    notificationService: NotificationService | null,
    privacyService: PrivacyService | null,
    securityService: SecurityService | null,
  ): Task {
    const task = new Task(
      state.id,
      state.config,
      exceptionService,
      stateSnapshot,
      notificationService,
      privacyService,
      securityService,
    );
    
    // 恢复状态
    task._status$.next(state.status);
    task._currentUrlIndex = state.currentUrlIndex;
    task._processedUrls = state.processedUrls;
    // @ts-expect-error - 允许写入只读属性进行反序列化
    task._createdAt = state.createdAt;
    // @ts-expect-error - 允许写入只读属性进行反序列化
    task.batchId = state.batchId;
    task._startedAt = state.startedAt;
    task._pausedAt = state.pausedAt;
    task._completedAt = state.completedAt;
    task._lastActivity = state.lastActivity;
    
    task._logger.info('Task deserialized from state');
    
    // 如果任务在中断时是运行状态，则恢复后置为暂停，等待用户操作
    if (task.getStatus() === 'running') {
      task._updateStatus('paused');
      task._logger.info('Restored running task to paused state.');
    }
    
    return task;
  }
  
  /**
   * 任务的主执行循环，负责处理单个URL。
   */
  private async _executeTask(): Promise<void> {
    this._logger.info(`Starting execution for URL: ${this._config.urls[0]}`);
    
    const url = this._config.urls[0];
    if (!url) {
      this._logger.warn(`Task has no URL to process.`);
      this._updateStatus('completed');
      this._cleanup();
      return;
    }

    // 隐私检查
    if (this._privacyService && await this._privacyService.isUrlBlacklisted(url)) {
      this._logger.warn(`Skipping blacklisted URL: ${url}`);
      this._handleFailedUrlProcessing(url, 'URL is in the user-defined blacklist.');
    } else {
      try {
        await this._processUrlSafely(url);
      } catch (error) {
        if (this._handleTaskError(error, url)) {
          // 如果错误导致任务暂停，则直接返回
          return;
        }
      }
    }
    
    // 任务完成
    this._finalizeTask();
  }

  private async _processUrlSafely(url: string): Promise<void> {
    this._updateLastActivity();
    const result = await this._processUrl(url);
    
    if (result.success) {
      this._handleSuccessfulUrlProcessing(url, result.data);
    } else {
      this._handleFailedUrlProcessing(url, result.error);
    }
  }

  private _handleSuccessfulUrlProcessing(url: string, data?: string): void {
    this._events$.next({ type: 'urlProcessed', payload: { url, success: true, data } });
    this._processedUrls.push(url); // 记录处理过的URL
    this._updateLastActivity();
  }

  private _handleFailedUrlProcessing(url: string, error?: string): void {
    this._logger.warn(`Failed to process URL ${url}: ${error}`);
    this._events$.next({ type: 'urlProcessed', payload: { url, success: false, error } });
    if (!this._shouldContinueOnError()) {
      this._updateStatus('failed');
    }
  }

  private _handleTaskError(error: unknown, url: string): boolean {
    if (error instanceof PausedForInterventionError) {
      this._logger.info(`Task execution paused for user intervention, stopping loop.`);
      return true; // 任务已暂停
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    this._logger.error(`Critical error while processing URL ${url}:`, errorMessage);
    if (!this._shouldContinueOnError()) {
      this._updateStatus('failed');
    }
    return false; // 继续处理
  }

  private _finalizeTask(): void {
    // 在任务结束时，停止视觉监控
    if (this._tabId) {
      void this._sendMessageToTab(this._tabId, MESSAGE_TYPES.VISUAL_TASK_STOP_MONITORING);
    }
    
    if (this._status$.value === 'running') {
      this._logger.info('Task for URL processed. Task completed.');
      this._updateStatus('completed');
    } else {
      this._logger.warn(`Task execution stopped with status: ${this._status$.value}.`);
    }
    
    this._cleanup();
  }

  /**
   * 根据当前渲染模式，分发URL处理任务。
   * @param url 要处理的URL。
   */
  private async _processUrl(url: string): Promise<{ success: boolean; data?: string; error?: string }> {
    this._updateStatus('running');
    this._updateLastActivity();

    try {
      this._prepareUrlProcessing(url);

      if (!this._tabId) {
        throw new Error('Task was not assigned a tab to run in. This is a critical scheduler error.');
      }
      
      this._logger.info(`Navigating assigned tab ${this._tabId} to url: ${url}`);
      // 仅更新URL，不等待返回，因为我们将使用事件监听器
      void chrome.tabs.update(this._tabId, { url });
      
      // 等待标签页完成加载，包括所有重定向
      await this._waitForTabComplete(this._tabId, url);
      
      await this._setupTabForProcessing(this._tabId);
      
      return await this._executePageScan(url, this._tabId);
    } catch (error: unknown) {
      return this._handleProcessUrlError(error, url);
    }
  }

  private _prepareUrlProcessing(url: string): void {
    // 在处理每个URL之前，重置内容脚本的就绪状态
    this._contentScriptReady = false;
    this._contentScriptReadyResolver = undefined;
    this._currentUrl = url;
  }

  private async _setupTabForProcessing(tabId: TabId): Promise<void> {
    this._logger.info(`Waiting for content script to be ready in tab ${tabId}...`);
    
    // 技术方案决策: 采用编程式、按需注入，以实现最高级别的安全隔离。
    // 1. 为什么不使用 manifest.json 静态注入?
    //    - 静态注入会将脚本插入到用户访问的所有页面，即使与任务无关，存在潜在的安全风险和性能开销。
    //    - 按需注入确保脚本只存在于由任务管理器创建和控制的标签页中。
    //
    // 2. 为什么使用动态 import() 而不是直接注入文件?
    //    - 问题: chrome.scripting.executeScript({ files: [...] }) 无法直接执行ES模块，会导致 "Cannot use import statement outside a module" 错误。
    //    - 第一次尝试(失败): 通过注入loader脚本来创建<script type="module">标签。但这会导致脚本在页面的"主世界(Main World)"执行，无法访问扩展API，
    //      并引发 "This script should only be loaded in a browser extension" 错误。
    //    - 最终方案: chrome.scripting.executeScript({ func: ... }) 能确保代码在具有扩展API访问权限的"隔离世界(Isolated World)"中启动。
    //      在该函数内部，我们使用动态 import() 来加载模块。这完美结合了两者的优点：在正确的上下文中，以正确的方式加载模块。
    await chrome.scripting.executeScript({
      target: { tabId: tabId, allFrames: true },
      func: async (scriptPath: string) => {
        try {
          // chrome.runtime.getURL 获取模块在扩展中的绝对路径
          const url = chrome.runtime.getURL(scriptPath);
          // 使用动态 import() 加载模块
          await import(url);
          console.log('Content script module imported successfully.');
        } catch (e) {
          console.error(`Error importing content script from path: ${scriptPath}`, e);
        }
      },
      args: [CONTENT_SCRIPT_PATH],
    });

    // 等待内容脚本注入并报告就绪
    await this._waitForContentScriptReady();
    
    // 在主要操作前捕获一个已知良好状态
    await this._captureInitialState(tabId);
    
    // 启动视觉任务监控
    await this._sendMessageToTab(tabId, MESSAGE_TYPES.VISUAL_TASK_START_MONITORING, { taskId: this._id });
  }

  private async _captureInitialState(tabId: TabId): Promise<void> {
    if (this._stateSnapshot) {
      this._lastKnownGoodState = await this._stateSnapshot.capture(tabId);
      if (this._lastKnownGoodState) {
        this._logger.info(`已为标签页 ${tabId} 成功捕获初始状态快照。`);
      }
    }
  }

  private async _executePageScan(url: string, tabId: TabId): Promise<{ success: boolean; data?: string; error?: string }> {
    if (!this._captureSystem) {
      this._logger.warn('Capture system not available. Skipping capture and marking URL as processed.');
      return { success: true, data: 'Capture system not available.' };
    }

    const sessionId = this._captureSystem.startSession(this._id, tabId);
    this._urlSessionMap.set(url, sessionId);

    const result = await this._performPageScan(url, sessionId, tabId);
    
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.error };
    }
  }

  private async _performPageScan(url: string, sessionId: string, tabId: TabId): Promise<{ success: boolean; data?: string; error?: string }> {
    // 创建一个Promise，等待全页扫描完成
    const pageScanPromise = new Promise<{ success: boolean; data?: string; error?: string }>((resolve) => {
      this._pageScanCompletionPromises.set(url, resolve);
    });

    // 向内容脚本发送消息，开始全页扫描
    const defaultOptions = {
      quality: 1,
      blockSize: 1000,
      overlap: 100,
      format: 'png' as const
    };
    
    // 从配置中获取基础选项，然后（如果可用）应用节流选项
    const baseOptions = this._config.captureOptions ?? defaultOptions;
    const finalOptions = this._captureSystem
      ? this._captureSystem.getCaptureOptions(baseOptions as unknown as Record<string, unknown>)
      : baseOptions;
    
    const messagePayload = {
      taskId: this._id,
      sessionId,
      captureOptions: finalOptions,
    };
    
    await this._sendMessageToTab(tabId, MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN, messagePayload);

    this._logger.info(`Session ${sessionId} started for url: ${url}`);
    const result = await pageScanPromise;
    this._pageScanCompletionPromises.delete(url); // 清理
    
    return result;
  }

  private _handleProcessUrlError(error: unknown, url: string): { success: boolean; data?: string; error?: string } {
    this._logger.error(`Critical error while processing URL ${url}:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    // 如果是需要用户干预的错误，则特殊处理
    if (error instanceof PausedForInterventionError) {
      throw error;
    }

    return { success: false, error: errorMessage };
  }

  private async _pauseTabExecution(tabId: TabId): Promise<void> {
    this._logger.info(`Pausing execution in tab ${tabId}`);
    // 当前版本仅记录暂停操作
    // 完整实现需要与content-script通信以停止正在进行的行为模拟
    this._logger.info(`Tab ${tabId} execution paused.`);
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          // 实现暂停机制：向内容脚本发送暂停信号
          console.log('Execution paused by task controller.');
          window.dispatchEvent(new CustomEvent('task:pause'));
        }
      });
    } catch (error) {
      this._logger.warn(`Could not execute pause script in tab ${tabId}:`, error);
    }
  }
  
  private async _continueExecution(): Promise<void> {
    if (this._currentUrl) {
      await this._processUrl(this._currentUrl);
    }
    await this._executeTask();
  }

  private async _waitForContentScriptReady(timeout: number = 30000): Promise<void> {
    // 检查是否在等待开始前就已经就绪（处理竞态条件）
    if (this._contentScriptReady) {
      this._logger.info('内容脚本已提前就绪。');
      return;
    }

    this._logger.info(`正在等待内容脚本就绪...`);

    return new Promise((resolve, reject) => {
      this._contentScriptReadyResolver = resolve;

      const timeoutId = setTimeout(() => {
        // 清理resolver，防止后续的"就绪"消息意外地解决了一个已超时的Promise
        this._contentScriptReadyResolver = undefined;
        reject(new Error('等待内容脚本就绪超时。'));
      }, timeout);

      // 当Promise被解决时（无论是成功还是失败），都清除超时定时器
      this._contentScriptReadyPromise = (this._contentScriptReadyPromise || Promise.resolve()).finally(() => {
        clearTimeout(timeoutId);
      });
    });
  }

  private _cleanup(): void {
    this._logger.info('Cleaning up task resources...');
    this._exceptionSubscription?.unsubscribe();
  }

  private _shouldContinueOnError(): boolean {
    // 检查任务配置，决定在遇到非致命错误时是否继续处理下一个URL
    return this._config.continueOnError ?? false;
  }
  
  private _updateStatus(status: TaskStatus): void {
    const oldStatus = this._status$.value;
    if (oldStatus === status) return;

    this._logger.info(`Status changed: ${oldStatus} -> ${status}`);
    this._status$.next(status);
    this._updateLastActivity();

    // 设置完成时间戳
    if (status === 'completed') {
      this._completedAt = getCurrentTimestamp();
      
      // 延迟清理会话，给下载功能留出时间
      // 10分钟后清理捕获会话（保留图像数据）
      setTimeout(() => {
        this._cleanupCaptureSessionsDelayed();
      }, 10 * 60 * 1000); // 10分钟
    }

    this._events$.next({
      type: 'statusChange',
      payload: { newStatus: status, oldStatus }
    });
  }
  
  private _updateLastActivity(): void {
    this._lastActivity = getCurrentTimestamp();
  }

  /**
   * 延迟清理捕获会话
   */
  private _cleanupCaptureSessionsDelayed(): void {
    if (this._captureSystem) {
      this._urlSessionMap.forEach((sessionId) => {
        this._captureSystem?.cleanupSession(sessionId);
      });
      this._urlSessionMap.clear();
      this._logger.info('Capture sessions cleaned up after delay.');
    }
  }

  // --- 异常处理 ---
  private readonly _handleException = async (event: ExceptionEvent): Promise<void> => {
    // 检查异常是否与此任务相关
    if (event.tabId !== this._tabId) {
      return;
    }

    this._logger.warn(`任务 ${this._id} 捕获到异常: ${event.type}`, event);

    switch (event.type) {
      case ExceptionType.REDIRECT_LOOP:
      case ExceptionType.AUTH_EXPIRED:
        await this._recoverFromCriticalError(event);
        break;
      
      case ExceptionType.CAPTCHA_DETECTED:
        // 对于验证码，仅暂停并通知，等待用户干预
        this._logger.info('检测到验证码，正在暂停任务等待用户解决...');
        await this.pause();
        // 此处可以集成通知服务来弹窗提示用户（仅在通知服务可用时）
        if (this._notificationService) {
          void this._notificationService.show({
            title: '需要您注意',
            message: `任务 ${this._id} 检测到验证码，已自动暂停。请手动处理后继续任务。`
          }, `captcha-${this._id}`);
        }
        break;

      case ExceptionType.BEHAVIOR_ANOMALY:
        // 对于行为异常，可以采取调整策略，例如增加随机性
        // 当前实现为记录日志，未来可扩展
        this._logger.warn('检测到行为异常，未来版本可增加动态策略调整。');
        break;
    }
  };

  private async _recoverFromCriticalError(event: ExceptionEvent): Promise<void> {
    this._logger.error(`检测到严重错误 ${event.type}，尝试恢复...`);
    
    // 暂停任务以停止任何进一步的操作
    if (this.getStatus() === 'running') {
      await this.pause();
    }

    if (this._lastKnownGoodState && this._tabId && this._stateSnapshot) {
      this._logger.info(`正在从上一个已知良好状态回滚...`);
      await this._stateSnapshot.restore(this._tabId, this._lastKnownGoodState);
      this._logger.info('回滚完成。任务已暂停，等待进一步指令。');
      // 任务已暂停，URL索引不增加，允许重试或由用户决定
    } else {
      this._logger.error('没有可用的良好状态进行回滚，任务将标记为失败。');
      this._updateStatus('failed');
    }
  }

  /**
   * 应用指定的性能节流级别。
   * 此方法由TaskScheduler调用，以响应全局资源压力。
   * @param level - 节流级别 (0=无, 1=轻度, 2=深度)。
   */
  public applyThrottling(level: number): void {
    if (level === 1) {
      this._logger.info('Throttling capture options for level 1');
      this._captureSystem?.setThrottledCaptureOptions({ quality: 75, format: 'jpeg' });
    } else if (level === 2) {
      this._logger.info('Throttling capture options for level 2');
      this._captureSystem?.setThrottledCaptureOptions({ quality: 50, format: 'jpeg' });
    } else {
      this._logger.info('Restoring default capture options');
      this._captureSystem?.setThrottledCaptureOptions(null);
    }
  }

  private _sendMessageToTab(tabId: TabId, type: string, payload?: unknown): Promise<unknown> {
    return new Promise((resolve, reject) => {
      chrome.tabs.sendMessage(tabId, { type, payload }, (response: unknown) => {
        if (chrome.runtime.lastError) {
          const message = chrome.runtime.lastError.message ?? '';
          // 在标签页关闭或导航等情况下，消息端口关闭是预期行为，不应视为严重错误。
          if (
            message.includes('The message port closed before a response was received') ||
            message.includes('Could not establish connection. Receiving end does not exist')
          ) {
            this._logger.warn(
              `Could not send message of type ${type} (tab might be closed or navigating): ${message}`
            );
            return resolve(undefined); // 优雅地处理，而不是抛出错误
          }

          // 对于其他未知错误，仍然作为异常抛出
          this._logger.error(`Error sending message of type ${type}:`, message);
          return reject(new Error(message));
        }
        
        // 检查来自内容脚本的响应是否表明操作失败
        if (response && typeof response === 'object' && 'success' in response && response.success === false) {
          const errorMsg = 'error' in response && typeof response.error === 'string' ? response.error : 'Unknown error from content script';
          this._logger.error(`Message response for type ${type} indicates failure:`, errorMsg);
          return reject(new Error(errorMsg));
        }
        
        // 否则，解析Promise并返回值
        const data = response && typeof response === 'object' && 'data' in response ? response.data : response;
        resolve(data);
      });
    });
  }

  /**
   * 获取指定URL的捕获图像
   * @param url 要获取图像的URL
   * @returns 该URL对应的图像数据数组
   */
  public getCapturedImages(url: string): ImageSegment[] {
    const sessionId = this._urlSessionMap.get(url);
    if (!sessionId || !this._captureSystem) {
      this._logger.warn(`No capture session found for URL: ${url}`);
      return [];
    }
    
    try {
      const images = this._captureSystem.getSessionImages(sessionId);
      this._logger.info(`Retrieved ${images.length} captured images for URL: ${url}, session: ${sessionId}`);
      return images;
    } catch (error) {
      this._logger.error(`Failed to retrieve images for URL ${url}:`, error);
      return [];
    }
  }

  private _findUrlBySessionId(sessionId: string): string | undefined {
    for (const [url, id] of this._urlSessionMap.entries()) {
      if (id === sessionId) {
        return url;
      }
    }
    return undefined;
  }

  /**
   * 检查此任务是否关联了给定的标签页ID。
   * @param tabId 要检查的标签页ID。
   * @returns 如果匹配则返回true，否则返回false。
   */
  public hasTab(tabId: number): boolean {
    return this._tabId === tabId;
  }

  /**
   * 彻底清除此任务的所有持久化和内存中的数据。
   * 主要用于在任务被从列表中永久移除时进行调用。
   */
  public purgeData(): void {
    this._logger.info(`Purging all data for task ${this._id}`);

    // 1. 清除由CaptureSystem管理的所有会话数据（如截图）
    if (this._captureSystem) {
      this._captureSystem.purgeAllSessionsForTask(this._id);
    }

    // 2. 清空内部的URL会话映射和等待的Promise
    this._urlSessionMap.clear();
    this._pageScanCompletionPromises.clear();

    this._logger.info(`Data purge completed for task ${this._id}.`);
  }

  /**
   * 等待指定的标签页完成加载。
   * @param tabId - 要等待的标签页ID。
   * @param url - 用于日志记录的URL。
   * @returns 当标签页加载完成时解析的Promise。
   */
  private _waitForTabComplete(tabId: TabId, url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this._logger.warn(`Timeout waiting for tab ${tabId} to complete loading ${url}`);
        listenerCleanup();
        reject(new Error(`Timeout waiting for tab ${tabId} to complete loading`));
      }, 30000); // 30秒超时

      const onTabUpdated = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo) => {
        // 仅当状态为 'complete' 时才响应，忽略 'loading' 阶段
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          this._logger.info(`Tab ${tabId} finished loading.`);
          listenerCleanup();
          resolve();
        }
      };

      const onTabRemoved = (removedTabId: number) => {
        if (removedTabId === tabId) {
          this._logger.warn(`Tab ${tabId} was closed while waiting for it to load.`);
          listenerCleanup();
          reject(new Error(`Tab ${tabId} was closed during loading`));
        }
      };
      
      const listenerCleanup = () => {
        clearTimeout(timeout);
        chrome.tabs.onUpdated.removeListener(onTabUpdated);
        chrome.tabs.onRemoved.removeListener(onTabRemoved);
      };

      chrome.tabs.onUpdated.addListener(onTabUpdated);
      chrome.tabs.onRemoved.addListener(onTabRemoved);
    });
  }
}