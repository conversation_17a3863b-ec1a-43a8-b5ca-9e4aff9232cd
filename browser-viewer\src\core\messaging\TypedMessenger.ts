/**
 * 类型安全的消息传递系统
 * 
 * - 编译时类型检查
 * - 消息格式一致性
 * - 运行时验证
 */

import { TabId, TaskId, TaskStatus } from '@/shared/types';
import { createLogger } from '@/shared/utils';
import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

// 消息订阅对象
export interface MessageSubscription {
  unsubscribe(): void;
}

// 消息验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// 消息元数据
export interface MessageMetadata {
  timestamp: number;
  sender: string;
  messageId: string;
  version: string;
}

// 基础消息接口
export interface BaseMessage<T = any> {
  type: string;
  payload: T;
  metadata: MessageMetadata;
}

// 消息注册表 - 定义所有支持的消息类型
export interface MessageRegistry {
  // 任务管理相关消息
  'TASK_STATUS_CHANGED': {
    taskId: TaskId;
    oldStatus: TaskStatus;
    newStatus: TaskStatus;
    timestamp: number;
  };
  
  'TASK_CREATED': {
    taskId: TaskId;
    batchId: string;
    urls: string[];
    config: any;
  };
  
  'TASK_COMPLETED': {
    taskId: TaskId;
    result: any;
    duration: number;
    errors?: string[];
  };
  
  'TASK_FAILED': {
    taskId: TaskId;
    error: string;
    stackTrace?: string;
    retryable: boolean;
  };
  
  // 数据提取相关消息
  'STRUCTURED_DATA_CAPTURED': {
    type: 'product' | 'article' | 'list';
    data: any;
    url: string;
    timestamp: number;
    confidence: number;
  };
  
  'VIEWPORT_CAPTURED': {
    segments: Array<{
      url: string;
      base64: string;
      bounds: { x: number; y: number; width: number; height: number };
    }>;
    fullPageHash: string;
  };
  
  // 系统状态相关消息
  'SYSTEM_STATUS_UPDATE': {
    cpuUsage: number;
    memoryUsage: number;
    networkUsage: number;
    activeTaskCount: number;
    queuedTaskCount: number;
  };
  
  'THROTTLING_LEVEL_CHANGED': {
    oldLevel: number;
    newLevel: number;
    reason: string;
    affectedTasks: TaskId[];
  };
  
  // 用户界面相关消息
  'POPUP_CONNECTED': {
    timestamp: number;
  };
  
  'POPUP_DISCONNECTED': {
    timestamp: number;
    reason?: string;
  };
  
  'USER_ACTION': {
    action: 'start' | 'pause' | 'stop' | 'clear';
    target?: 'all' | 'selected' | TaskId;
    batchIds?: string[];
  };
  
  // 错误和异常相关消息
  'EXCEPTION_OCCURRED': {
    type: 'network' | 'parsing' | 'security' | 'unknown';
    message: string;
    context: any;
    recoverable: boolean;
  };
  
  'RECOVERY_ACTION_TAKEN': {
    exceptionId: string;
    action: 'retry' | 'skip' | 'abort';
    success: boolean;
  };
  
  // 内容脚本相关消息
  'CONTENT_SCRIPT_READY': {
    tabId: TabId;
    url: string;
    capabilities: string[];
  };
  
  'PAGE_ANALYSIS_RESULT': {
    tabId: TabId;
    elements: any[];
    interactiveElements: any[];
    loadTime: number;
  };
  
  // 安全和隐私相关消息
  'PRIVACY_SETTINGS_CHANGED': {
    oldSettings: any;
    newSettings: any;
    affectedFeatures: string[];
  };
  
  'SECURITY_ALERT': {
    level: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    action: string;
    autoResolved: boolean;
  };
}

// 消息验证器接口
export interface MessageValidator<T = any> {
  validate(payload: T): ValidationResult;
}

// 消息处理器接口
export interface MessageHandler<K extends keyof MessageRegistry> {
  handle(message: BaseMessage<MessageRegistry[K]>): Promise<void> | void;
}

// 消息路由器接口
export interface MessageRouter {
  route<K extends keyof MessageRegistry>(
    messageType: K,
    message: BaseMessage<MessageRegistry[K]>
  ): Promise<void>;
}

/**
 * 类型安全的消息发送器
 */
export class TypedMessenger {
  private readonly _logger = createLogger('TypedMessenger');
  private readonly _messageSubject = new Subject<BaseMessage>();
  private readonly _validators = new Map<string, MessageValidator>();
  private readonly _handlers = new Map<string, MessageHandler<any>[]>();
  private readonly _messageId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  constructor(private readonly _senderId: string = 'TypedMessenger') {}

  /**
   * 发送类型安全的消息
   */
  async send<K extends keyof MessageRegistry>(
    type: K,
    payload: MessageRegistry[K],
    target?: 'popup' | 'background' | 'content' | 'all'
  ): Promise<void> {
    // 验证消息载荷
    const validator = this._validators.get(type);
    if (validator) {
      const validation = validator.validate(payload);
      if (!validation.isValid) {
        throw new Error(`消息验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 构建完整消息
    const message: BaseMessage<MessageRegistry[K]> = {
      type,
      payload,
      metadata: {
        timestamp: Date.now(),
        sender: this._senderId,
        messageId: this._messageId(),
        version: '1.0'
      }
    };

    // 发送消息
    await this._sendMessage(message, target);
    
    // 发出到内部流
    this._messageSubject.next(message);
    
    this._logger.debug(`消息已发送: ${type}`, { messageId: message.metadata.messageId });
  }

  /**
   * 监听特定类型的消息
   */
  listen<K extends keyof MessageRegistry>(
    type: K,
    handler: (payload: MessageRegistry[K], metadata: MessageMetadata) => void | Promise<void>
  ): MessageSubscription {
    const subscription = this._messageSubject.pipe(
      filter(message => message.type === type),
      map(message => message as BaseMessage<MessageRegistry[K]>)
    ).subscribe(async (message) => {
      try {
        await handler(message.payload, message.metadata);
      } catch (error) {
        this._logger.error(`消息处理失败: ${type}`, error);
      }
    });

    return {
      unsubscribe: () => subscription.unsubscribe()
    };
  }

  /**
   * 获取特定类型消息的Observable流
   */
  getMessageStream<K extends keyof MessageRegistry>(type: K): Observable<MessageRegistry[K]> {
    return this._messageSubject.pipe(
      filter(message => message.type === type),
      map(message => (message as BaseMessage<MessageRegistry[K]>).payload)
    );
  }

  /**
   * 批量发送消息
   */
  async sendBatch<K extends keyof MessageRegistry>(
    messages: Array<{ type: K; payload: MessageRegistry[K] }>,
    target?: 'popup' | 'background' | 'content' | 'all'
  ): Promise<void> {
    const sendPromises = messages.map(msg => this.send(msg.type, msg.payload, target));
    await Promise.all(sendPromises);
  }

  /**
   * 注册消息验证器
   */
  registerValidator<K extends keyof MessageRegistry>(
    type: K,
    validator: MessageValidator<MessageRegistry[K]>
  ): void {
    this._validators.set(type, validator);
    this._logger.debug(`验证器已注册: ${type}`);
  }

  /**
   * 注册消息处理器
   */
  registerHandler<K extends keyof MessageRegistry>(
    type: K,
    handler: MessageHandler<K>
  ): void {
    if (!this._handlers.has(type)) {
      this._handlers.set(type, []);
    }
    this._handlers.get(type)!.push(handler);
    this._logger.debug(`处理器已注册: ${type}`);
  }

  /**
   * 处理接收到的消息
   */
  async handleReceivedMessage(message: BaseMessage): Promise<void> {
    const handlers = this._handlers.get(message.type);
    if (handlers) {
      const handlePromises = handlers.map(handler => handler.handle(message));
      await Promise.all(handlePromises);
    }
    
    // 发出到内部流
    this._messageSubject.next(message);
  }

  /**
   * 创建请求-响应模式的消息
   */
  async request<K extends keyof MessageRegistry, R>(
    type: K,
    payload: MessageRegistry[K],
    timeout: number = 10000
  ): Promise<R> {
    return new Promise((resolve, reject) => {
      const messageId = this._messageId();
      const timeoutId = setTimeout(() => {
        reject(new Error(`消息请求超时: ${type}`));
      }, timeout);

      // 监听响应
      const subscription = this._messageSubject.pipe(
        filter(msg => msg.metadata.messageId === messageId && msg.type.endsWith('_RESPONSE'))
      ).subscribe(response => {
        clearTimeout(timeoutId);
        subscription.unsubscribe();
        resolve(response.payload as R);
      });

      // 发送请求
      void this.send(type, payload);
    });
  }

  /**
   * 获取消息统计信息
   */
  getStats(): {
    totalMessagesSent: number;
    totalMessagesReceived: number;
    registeredValidators: number;
    registeredHandlers: number;
  } {
    return {
      totalMessagesSent: 0, // 需要实现计数器
      totalMessagesReceived: 0,
      registeredValidators: this._validators.size,
      registeredHandlers: this._handlers.size
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this._messageSubject.complete();
    this._validators.clear();
    this._handlers.clear();
  }

  // 私有方法
  private async _sendMessage<K extends keyof MessageRegistry>(
    message: BaseMessage<MessageRegistry[K]>,
    target?: 'popup' | 'background' | 'content' | 'all'
  ): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        if (target === 'popup' || target === 'background' || !target) {
          // 发送给扩展的其他部分
          chrome.runtime.sendMessage(message);
        }
        
        if (target === 'content' || target === 'all') {
          // 发送给内容脚本
          const tabs = await chrome.tabs.query({ active: true });
          for (const tab of tabs) {
            if (tab.id) {
              try {
                await chrome.tabs.sendMessage(tab.id, message);
              } catch (error) {
                // 忽略无法发送到某些标签页的错误
                this._logger.debug(`无法发送消息到标签页 ${tab.id}:`, error);
              }
            }
          }
        }
      } else {
        // 在非扩展环境中，只发出到内部流
        this._logger.debug('非扩展环境，消息仅在内部处理');
      }
    } catch (error) {
      this._logger.error('发送消息失败', error);
      throw error;
    }
  }
}

/**
 * 消息验证器工厂
 */
export class MessageValidatorFactory {
  /**
   * 创建基于模式的验证器
   */
  static createSchemaValidator<T>(schema: any): MessageValidator<T> {
    return {
      validate(payload: T): ValidationResult {
        // 简单的验证实现
        // 在实际项目中可以使用 Joi, Yup 或其他验证库
        const errors: string[] = [];
        
        if (payload === null || payload === undefined) {
          errors.push('载荷不能为空');
        }
        
        return {
          isValid: errors.length === 0,
          errors
        };
      }
    };
  }

  /**
   * 创建必填字段验证器
   */
  static createRequiredFieldsValidator<T>(requiredFields: Array<keyof T>): MessageValidator<T> {
    return {
      validate(payload: T): ValidationResult {
        const errors: string[] = [];
        
        for (const field of requiredFields) {
          if (!(field in payload) || payload[field] === null || payload[field] === undefined) {
            errors.push(`必填字段缺失: ${String(field)}`);
          }
        }
        
        return {
          isValid: errors.length === 0,
          errors
        };
      }
    };
  }
}

/**
 * 全局消息总线实例
 */
let globalMessenger: TypedMessenger | null = null;

/**
 * 获取全局消息总线实例
 */
export function getGlobalMessenger(): TypedMessenger {
  if (!globalMessenger) {
    globalMessenger = new TypedMessenger('GlobalMessenger');
  }
  return globalMessenger;
}

/**
 * 设置全局消息总线实例
 */
export function setGlobalMessenger(messenger: TypedMessenger): void {
  globalMessenger = messenger;
}

/**
 * 便捷的消息发送函数
 */
export async function sendMessage<K extends keyof MessageRegistry>(
  type: K,
  payload: MessageRegistry[K],
  target?: 'popup' | 'background' | 'content' | 'all'
): Promise<void> {
  return getGlobalMessenger().send(type, payload, target);
}

/**
 * 便捷的消息监听函数
 */
export function listenToMessage<K extends keyof MessageRegistry>(
  type: K,
  handler: (payload: MessageRegistry[K], metadata: MessageMetadata) => void | Promise<void>
): MessageSubscription {
  return getGlobalMessenger().listen(type, handler);
}