/**
 * @file AdaptiveThrottler.ts
 * @description 实现自适应节流机制以管理系统资源。
 */
import type { PerformanceMetrics } from '@/shared/types';
import { createLogger } from '@/shared/utils';

// 节流级别阈值配置，根据CPU和内存使用情况动态调整
const THROTTLE_LEVELS = {
  LEVEL_0: { CPU: 0.4, MEMORY: 612 }, // 正常运行
  LEVEL_1: { CPU: 0.7, MEMORY: 1024 }, // 轻度节流
  LEVEL_2: { CPU: 0.85, MEMORY: 2048 }, // 深度节流
  MAX_LEVEL: 2,
};

type ThrottleAction = () => void;

/**
 * AdaptiveThrottler根据实时性能指标动态调整应用行为，
 * 以防止系统过载并确保稳定性。
 */
export class AdaptiveThrottler {
  private readonly _logger = createLogger('AdaptiveThrottler');
  private readonly _actions: Map<number, ThrottleAction[]> = new Map();
  private _currentLevel = 0;

  constructor() {
    this._logger.info('插件自适应节流器已连接。');
  }

  /**
   * 注册在达到特定节流级别时要执行的操作。
   * @param level - 节流级别。
   * @param action - 要执行的函数。
   */
  public on(level: number, action: ThrottleAction): void {
    const existingActions = this._actions.get(level) || [];
    this._actions.set(level, [...existingActions, action]);
  }

  /**
   * 根据最新的性能指标调整节流级别。
   * @param metrics - 当前系统的性能指标。
   */
  public adjust(metrics: PerformanceMetrics): void {
    const { cpu, memory } = metrics;
    let newLevel = 0;

    if (cpu > THROTTLE_LEVELS.LEVEL_2.CPU || memory > THROTTLE_LEVELS.LEVEL_2.MEMORY) {
      newLevel = 2;
    } else if (cpu > THROTTLE_LEVELS.LEVEL_1.CPU || memory > THROTTLE_LEVELS.LEVEL_1.MEMORY) {
      newLevel = 1;
    }

    if (newLevel !== this._currentLevel) {
      this._logger.info(`节流级别调整： ${this._currentLevel} -> ${newLevel}`);
      this._currentLevel = newLevel;
      this._executeActions();
    }
  }

  /**
   * 执行与当前节流级别关联的操作。
   */
  private _executeActions(): void {
    const actionsToRun = this._actions.get(this._currentLevel);
    if (actionsToRun) {
      this._logger.info(`正在为节流级别 ${this._currentLevel} 执行 ${actionsToRun.length} 个操作`);
      actionsToRun.forEach(action => action());
    }
  }
} 