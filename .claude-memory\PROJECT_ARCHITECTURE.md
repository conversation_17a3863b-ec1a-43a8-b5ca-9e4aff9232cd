# 项目架构文档

## 技术栈概览

### 核心框架

- **Python 3.x** - 主要开发语言
- **Playwright** - 网页自动化和截图
- **Mistral AI OCR API** - 图像文字识别
- **BeautifulSoup4** - HTML解析辅助
- **asyncio** - 异步编程支持

### 关键依赖

- `playwright` - 浏览器自动化
- `playwright-stealth` - 反反爬措施
- `beautifulsoup4` & `lxml` - HTML/XML解析
- `requests` - HTTP请求
- `python-dotenv` - 环境变量管理

## 项目架构

### 目录结构

```
agent-crawl/
├── crawler/                    # 爬虫模块
│   └── jingdong/              # 京东专用爬虫
│       ├── __init__.py
│       └── with_cookies.py    # 主爬虫脚本
├── ocr/                       # OCR处理模块
│   ├── mistral/              # Mistral AI集成
│   │   ├── __init__.py
│   │   ├── config.py         # API配置管理
│   │   └── client.py         # OCR客户端
│   └── process_images.py     # 批量图像处理
├── .claude-memory/           # 任务管理和记忆
│   ├── tasks/               # 任务记录文件
│   ├── GENERAL_RULES.md
│   ├── PROJECT_ARCHITECTURE.md
│   ├── DEVELOPMENT_GUIDE.md
│   └── TASK_TEMPLATE.md
├── .crawler_results/         # 爬取结果存储
│   ├── images/              # 截图文件
│   └── ocr/                 # OCR识别结果
└── 配置和脚本文件...
```

## 核心模块设计

### 1. 爬虫模块 (crawler/)

#### 主要功能

- 使用Playwright模拟真实用户行为
- Cookie会话管理和持久化
- 页面截图自动保存
- 商品页面数据抓取

#### 关键文件

- `with_cookies.py`: 主爬虫入口，包含菜单交互和会话管理

### 2. OCR模块 (ocr/)

#### 主要功能  

- Mistral AI OCR API集成
- 图像预处理和编码
- 批量图像识别
- 结果格式化和存储

#### 关键文件

- `mistral/config.py`: API配置和密钥管理
- `mistral/client.py`: OCR客户端实现  
- `process_images.py`: 批量处理脚本

### 3. 工作流集成

#### 数据流向

1. **爬虫阶段**: 访问京东商品页面 → 截图保存
2. **OCR阶段**: 读取截图 → API识别 → 结果存储
3. **集成模式**: 爬虫完成后自动触发OCR处理

#### 存储策略

- 截图: `.crawler_results/images/`
- OCR结果: `.crawler_results/ocr/`
- 会话数据: `.crawler_profile/`

## 配置管理

### 环境变量

- `MISTRAL_API_KEY`: OCR API密钥（必需）
- `MISTRAL_BASE_URL`: API基础URL（可选）

### 配置文件

- `.env`: 本地环境变量
- `login_info.json`: 登录会话信息

## 扩展性设计

### 模块化原则

- 爬虫与OCR模块完全解耦
- OCR模块支持优雅降级
- 支持多种电商平台扩展

### API设计

- 统一的错误处理机制
- 异步编程模型
- 可配置的重试策略

## 性能考虑

### 并发控制

- 异步I/O操作
- API请求频率限制
- 资源使用监控

### 内存管理

- 大图片文件处理优化
- 会话数据及时清理
- 批处理分批执行

---
*此文档随项目架构演进持续更新*
