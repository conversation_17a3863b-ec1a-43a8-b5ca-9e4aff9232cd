/**
 * @file ExceptionService.ts
 * @description 实时监测、识别和报告浏览器环境中发生的各种异常事件。
 */
import { ExceptionEvent, ExceptionType, TabId, TaskId } from '@/shared/types';
import { createLogger } from '@/shared/utils';
import { Subject } from 'rxjs';

/**
 * ExceptionService 负责监控潜在问题，例如验证码、重定向循环或授权失败。
 * 它提供了一个集中式系统来检测和报告异常，以便其他服务可以采取适当的行动。
 */
export class ExceptionService {
  private readonly _logger = createLogger('ExceptionService');
  private readonly _redirectHistory = new Map<TabId, { urls: string[]; timestamps: number[] }>();
  private readonly _REDIRECT_LOOP_THRESHOLD = 5; // 在短时间内（例如10秒）发生5次重定向
  private readonly _REDIRECT_TIME_WINDOW = 10000; // 10秒

  /** 当检测到异常时发出。 */
  public readonly onException = new Subject<ExceptionEvent>();

  constructor() {
    this._logger.info('插件服务异常管理器已连接。');
  }

  /**
   * 启动异常监控监听器。
   */
  public start(): void {
    this._logger.info('正在启动异常监控...');
    chrome.webNavigation.onCommitted.addListener(this._handleNavigation);
    chrome.webRequest.onCompleted.addListener(
      this._handleRequestCompleted,
      { urls: ['<all_urls>'], types: ['main_frame', 'sub_frame'] }
    );
    this._logger.info('异常监控已启动。');
  }

  /**
   * 停止异常监控监听器。
   */
  public stop(): void {
    this._logger.info('正在停止异常监控...');
    chrome.webNavigation.onCommitted.removeListener(this._handleNavigation);
    chrome.webRequest.onCompleted.removeListener(this._handleRequestCompleted);
    this._redirectHistory.clear();
    this._logger.info('异常监控已停止。');
  }

  /**
   * 检查给定标签页中是否存在验证码。
   * @param tabId - 要检查的标签页ID。
   * @param taskId - 与此检查关联的任务ID。
   */
  public async detectCaptcha(tabId: TabId, taskId?: TaskId): Promise<void> {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          return !!document.querySelector('iframe[src*="recaptcha"], iframe[src*="hcaptcha"]');
        },
      });

      if (results[0]?.result) {
        const tab = await chrome.tabs.get(tabId);
        this._dispatchException({
          type: ExceptionType.CAPTCHA_DETECTED,
          severity: 'critical',
          message: `在 tab ${tabId} 中检测到验证码。`,
          context: { url: tab.url },
          timestamp: Date.now(),
          taskId,
          tabId,
        });
      }
    } catch (error) {
      this._logger.error(`在 tab ${tabId} 中检测验证码时出错:`, error);
    }
  }

  /**
   * 分析用户或代理的行为序列，以检测异常模式。
   * 此为高级功能，旨在识别可能被网站检测为机器人的可疑行为。
   * @param tabId - 发生行为的标签页ID。
   * @param actions - 一个表示最近一系列操作的数组。
   */
  public analyzeBehavior(tabId: TabId, actions: { type: string; timestamp: number; x?: number; y?: number }[]): void {
    // 行为分析是一项复杂功能，当前版本我们先建立接口和基本框架。
    // 完整的熵值分析将在后续迭代中实现。

    // 1. 行为序列熵值分析：
    //   - 计算操作类型序列的香农熵。一个极低或极高的熵值都可能表示异常。
    //   - 例如，['click', 'scroll', 'wait', 'click'] 的序列比 ['click', 'click', 'click', 'click'] 更正常。
    //   - 低熵（高可预测性）可能表明脚本化行为。
    const sequenceEntropy = this._calculateEntropy(actions.map(a => a.type));
    if (sequenceEntropy < 0.5) { // 阈值为示例
      this._dispatchException({
        type: ExceptionType.BEHAVIOR_ANOMALY,
        severity: 'medium',
        message: `在 tab ${tabId} 检测到低熵行为序列。`,
        context: { entropy: sequenceEntropy, sequence: actions.map(a => a.type) },
        timestamp: Date.now(),
        tabId,
      });
    }

    // 2. 点击位置分布熵分析：
    //   - 将视口划分为网格（例如 10x10）。
    //   - 计算点击事件在这些网格中的分布熵。
    //   - 低熵（点击位置集中）可能表示机器人行为，例如总是点击屏幕正中央。
    const clickActions = actions.filter(a => a.type === 'click' && a.x !== undefined && a.y !== undefined);
    if (clickActions.length > 5) {
      const positionEntropy = this._calculateClickPositionEntropy(clickActions as {x: number, y: number}[]);
      if (positionEntropy < 1.0) { // 阈值为示例
        this._dispatchException({
          type: ExceptionType.BEHAVIOR_ANOMALY,
          severity: 'medium',
          message: `在 tab ${tabId} 检测到低熵点击位置分布。`,
          context: { entropy: positionEntropy, clicks: clickActions.length },
          timestamp: Date.now(),
          tabId,
        });
      }
    }
  }

  private readonly _handleNavigation = (details: chrome.webNavigation.WebNavigationTransitionCallbackDetails): void => {
    if (details.frameId !== 0) return;

    const { tabId, url, transitionType } = details;
    
    if (this._isRedirectTransition(transitionType)) {
      this._detectRedirectLoop(tabId, url);
    }
  };

  private _isRedirectTransition(transitionType: string): boolean {
    return transitionType.includes('redirect');
  }

  private _detectRedirectLoop(tabId: TabId, url: string): void {
    const history = this._redirectHistory.get(tabId) ?? { urls: [], timestamps: [] };

    const now = Date.now();
    history.urls.push(url);
    history.timestamps.push(now);

    // 清理并只保留时间窗口内的数据
    const windowStartIndex = history.timestamps.findIndex(ts => now - ts <= this._REDIRECT_TIME_WINDOW);
    if (windowStartIndex > 0) {
      history.urls = history.urls.slice(windowStartIndex);
      history.timestamps = history.timestamps.slice(windowStartIndex);
    }
    
    this._redirectHistory.set(tabId, history);

    // 在更新历史记录后进行判断
    if (history.urls.length >= this._REDIRECT_LOOP_THRESHOLD) {
      const uniqueUrls = new Set(history.urls);
      // 如果短时间内的大量重定向发生在少数几个URL之间，则判定为循环。
      // 这里的判断条件（小于阈值的一半）是一个启发式规则，可以在实践中调整。
      if (uniqueUrls.size < this._REDIRECT_LOOP_THRESHOLD / 2) {
        this._dispatchException({
          type: ExceptionType.REDIRECT_LOOP,
          severity: 'high',
          message: `在 tab ${tabId} 中检测到重定向循环。`,
          context: { recentUrls: history.urls },
          timestamp: now,
          tabId: tabId
        });
        // 清除历史记录以避免在同一循环上重复报警
        this._redirectHistory.delete(tabId);
      }
    }
  }

  private readonly _handleRequestCompleted = (details: chrome.webRequest.OnCompletedDetails): void => {
    if (details.frameId && details.frameId !== 0) return;

    if (details.statusCode === 401 || details.statusCode === 403) {
      this._dispatchException({
        type: ExceptionType.AUTH_EXPIRED,
        severity: 'high',
        message: `在 ${details.url} 检测到授权失败 (状态码: ${details.statusCode})。`,
        context: { url: details.url, statusCode: details.statusCode },
        timestamp: Date.now(),
        tabId: details.tabId,
      });
    }
  };

  private _dispatchException(event: ExceptionEvent): void {
    this._logger.warn(`检测到异常: ${event.type}`, event);
    this.onException.next(event);
  }

  // --- 行为分析辅助方法 ---

  /**
   * 计算一组离散事件的香农熵。
   * @param items - 字符串数组，代表事件序列。
   * @returns 返回计算出的熵值。熵值越高，表示序列的不确定性（随机性）越大。
   */
  private _calculateEntropy(items: string[]): number {
    if (items.length === 0) {
      return 0;
    }

    // 步骤 1: 计算每个独立项的频率
    const frequencyMap = new Map<string, number>();
    for (const item of items) {
      frequencyMap.set(item, (frequencyMap.get(item) ?? 0) + 1);
    }

    // 步骤 2: 应用香农熵公式: H = -sum(p(x) * log2(p(x)))
    let entropy = 0;
    const totalItems = items.length;
    for (const count of frequencyMap.values()) {
      const probability = count / totalItems;
      if (probability > 0) {
        entropy -= probability * Math.log2(probability);
      }
    }

    return entropy;
  }

  /**
   * 计算点击位置分布的香农熵。
   * @param actions - 包含点击坐标(x, y)的动作数组。
   * @param gridWidth - 用于分析的网格宽度。
   * @param gridHeight - 用于分析的网格高度。
   * @returns 返回点击位置分布的熵值。熵值越低，表示点击位置越集中、越可预测。
   */
  private _calculateClickPositionEntropy(
    actions: { x: number; y: number }[],
    // 假设视口维度，实际应用中应从上下文获取
    viewportWidth = 1920,
    viewportHeight = 1080,
    gridDivisions = 10
  ): number {
    if (actions.length === 0) {
      return 0;
    }

    const gridCellWidth = viewportWidth / gridDivisions;
    const gridCellHeight = viewportHeight / gridDivisions;

    // 将点击坐标映射到网格单元
    const gridPositions = actions.map(action => {
      const gridX = Math.floor(action.x / gridCellWidth);
      const gridY = Math.floor(action.y / gridCellHeight);
      return `${gridX},${gridY}`;
    });

    // 调用通用的熵计算方法
    return this._calculateEntropy(gridPositions);
  }
} 