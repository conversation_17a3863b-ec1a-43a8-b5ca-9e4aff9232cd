/**
 * 认证状态管理
 * 使用Zustand管理用户认证状态
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface User {
  id: string
  username: string
  email: string
  fullName?: string
  avatar?: string
  isAdmin: boolean
  lastLogin?: string
  createdAt: string
}

export interface AuthState {
  // 状态
  isAuthenticated: boolean
  user: User | null
  token: string | null
  refreshToken: string | null
  loading: boolean
  error: string | null

  // 操作
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  refreshAuth: () => Promise<void>
  updateUser: (user: Partial<User>) => void
  clearError: () => void
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      // 初始状态
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      loading: false,
      error: null,

      // 登录
      login: async (credentials: LoginCredentials) => {
        set((state) => {
          state.loading = true
          state.error = null
        })

        try {
          const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || '登录失败')
          }

          const data: LoginResponse = await response.json()

          set((state) => {
            state.isAuthenticated = true
            state.user = data.user
            state.token = data.access_token
            state.refreshToken = data.refresh_token
            state.loading = false
            state.error = null
          })
        } catch (error) {
          set((state) => {
            state.loading = false
            state.error = error instanceof Error ? error.message : '登录失败'
          })
          throw error
        }
      },

      // 登出
      logout: () => {
        set((state) => {
          state.isAuthenticated = false
          state.user = null
          state.token = null
          state.refreshToken = null
          state.error = null
        })
      },

      // 刷新认证
      refreshAuth: async () => {
        const { refreshToken } = get()
        
        if (!refreshToken) {
          get().logout()
          return
        }

        try {
          const response = await fetch('/api/v1/auth/refresh', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refresh_token: refreshToken }),
          })

          if (!response.ok) {
            throw new Error('Token refresh failed')
          }

          const data: LoginResponse = await response.json()

          set((state) => {
            state.token = data.access_token
            state.refreshToken = data.refresh_token
            state.user = data.user
          })
        } catch (error) {
          console.error('Token refresh failed:', error)
          get().logout()
        }
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        set((state) => {
          if (state.user) {
            state.user = { ...state.user, ...userData }
          }
        })
      },

      // 清除错误
      clearError: () => {
        set((state) => {
          state.error = null
        })
      },
    })),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
      }),
    }
  )
)
