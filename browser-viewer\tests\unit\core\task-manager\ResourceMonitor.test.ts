import { ResourceMonitor } from '@/core/task-manager/ResourceMonitor';
import type { Subscription } from 'rxjs';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TestUtils } from '../../../utils/test-helpers';

describe('ResourceMonitor', () => {
  let monitor: ResourceMonitor;

  beforeEach(() => {
    // 模拟浏览器环境
    vi.stubGlobal('performance', {
      now: vi.fn().mockReturnValue(1000),
      memory: {
        usedJSHeapSize: 1024 * 1024,
        totalJSHeapSize: 2 * 1024 * 1024,
        jsHeapSizeLimit: 4 * 1024 * 1024
      }
    });
    
    vi.stubGlobal('navigator', {
      connection: {
        downlink: 10,
        effectiveType: '4g',
        rtt: 100,
        saveData: false
      },
      deviceMemory: 8
    });
    
    vi.stubGlobal('PerformanceObserver', class MockPerformanceObserver {
      constructor(callback: () => void) {
        this.callback = callback;
      }
      callback: () => void;
      observe = vi.fn();
      disconnect = vi.fn();
    });
    
    vi.stubGlobal('fetch', vi.fn().mockResolvedValue({
      ok: true,
      text: () => Promise.resolve('test')
    }));
    
    vi.stubGlobal('requestAnimationFrame', vi.fn((callback: FrameRequestCallback) => {
      setTimeout(() => callback(1000), 16);
      return 1;
    }));
    
    vi.stubGlobal('cancelAnimationFrame', vi.fn());
    
    monitor = new ResourceMonitor();
    TestUtils.cleanupTestEnvironment();
  });

  describe('性能监控', () => {
    it('应该能够获取当前性能指标', () => {
      const metrics = monitor.getCurrentMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.cpu).toBe('number');
      expect(typeof metrics.memory).toBe('number');
      expect(typeof metrics.network).toBe('number');
      expect(typeof metrics.fps).toBe('number');
      expect(typeof metrics.latency).toBe('number');
      expect(typeof metrics.timestamp).toBe('number');
    });

    it('应该能够开始和停止监控', () => {
      monitor.start();
      
      // 验证监控已启动
      const metrics = monitor.getCurrentMetrics();
      expect(metrics).toBeDefined();
      
      monitor.stop();
      
      // 验证监控已停止
      expect(() => monitor.getCurrentMetrics()).not.toThrow();
    });

    it('应该能够检测资源压力', () => {
      monitor.start();
      
      // 测试资源压力检测
      expect(typeof monitor.isResourceStressed()).toBe('boolean');
      expect(typeof monitor.isResourceCritical()).toBe('boolean');
      expect(typeof monitor.getResourceUtilization()).toBe('number');
      
      monitor.stop();
    });
  });

  describe('Observable接口', () => {
    it('应该提供metrics$ Observable', () => {
      return new Promise<void>((resolve) => {
        let subscription: Subscription | null = null;
        let resolved = false;
        
        subscription = monitor.metrics$.subscribe(metrics => {
          if (resolved) return; // 防止重复执行
          
          expect(metrics).toHaveProperty('cpu');
          expect(metrics).toHaveProperty('memory');
          expect(metrics).toHaveProperty('network');
          expect(metrics).toHaveProperty('fps');
          expect(metrics).toHaveProperty('latency');
          expect(metrics).toHaveProperty('timestamp');
          
          resolved = true;
          subscription?.unsubscribe();
          monitor.stop();
          resolve();
        });
        
        monitor.start();
        
        // 添加超时机制防止无限等待
        setTimeout(() => {
          if (!resolved) {
            resolved = true;
            subscription?.unsubscribe();
            monitor.stop();
            resolve();
          }
        }, 1000);
      });
    });
    
    it('应该提供resourceChanges$ Observable', () => {
      return new Promise<void>((resolve) => {
        let changeCount = 0;
        let subscription: Subscription | null = null;
        
        subscription = monitor.resourceChanges$.subscribe(change => {
          expect(change).toHaveProperty('type');
          expect(change).toHaveProperty('metrics');
          expect(['normal', 'moderate', 'stressed', 'critical']).toContain(change.type);
          
          changeCount++;
          if (changeCount >= 1) {
            subscription?.unsubscribe();
            monitor.stop();
            resolve();
          }
        });
        
        monitor.start();
        
        // 增加超时时间并确保总是清理
        setTimeout(() => {
          subscription?.unsubscribe();
          monitor.stop();
          resolve();
        }, 1000);
      });
    });
  });

  describe('历史数据', () => {
    it('应该能够获取性能历史', () => {
      monitor.start();
      
      const history = monitor.getMetricsHistory();
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeGreaterThanOrEqual(0);
      
      monitor.stop();
    });

    it('应该能够计算平均指标', () => {
      monitor.start();
      
      const averageMetrics = monitor.getAverageMetrics(1);
      expect(averageMetrics).toBeDefined();
      expect(typeof averageMetrics.cpu).toBe('number');
      expect(typeof averageMetrics.memory).toBe('number');
      
      monitor.stop();
    });
    
    it('应该限制历史数据大小', () => {
      monitor.start();
      
      const history = monitor.getMetricsHistory();
      expect(history.length).toBeLessThanOrEqual(100); // MAX_HISTORY_SIZE
      
      monitor.stop();
    });
  });
  
  describe('错误处理', () => {
    it('应该处理API不可用的情况', () => {
      vi.stubGlobal('performance', undefined);
      
      expect(() => {
        const newMonitor = new ResourceMonitor();
        newMonitor.start();
        const metrics = newMonitor.getCurrentMetrics();
        expect(metrics.cpu).toBe(0);
        expect(metrics.memory).toBe(0);
        newMonitor.stop();
      }).not.toThrow();
    });
    
    it('应该处理网络API不可用的情况', () => {
      vi.stubGlobal('navigator', {});
      
      expect(() => {
        const newMonitor = new ResourceMonitor();
        newMonitor.start();
        const metrics = newMonitor.getCurrentMetrics();
        expect(metrics.network).toBe(0);
        newMonitor.stop();
      }).not.toThrow();
    });
  });
  
  describe('集成测试准备', () => {
    it('应该与TaskScheduler兼容', () => {
      expect(typeof monitor.start).toBe('function');
      expect(typeof monitor.stop).toBe('function');
      expect(typeof monitor.getCurrentMetrics).toBe('function');
      expect(typeof monitor.isResourceStressed).toBe('function');
      expect(typeof monitor.isResourceCritical).toBe('function');
      expect(typeof monitor.getResourceUtilization).toBe('function');
    });
    
    it('应该支持Observable模式', () => {
      expect(monitor.metrics$).toBeDefined();
      expect(monitor.resourceChanges$).toBeDefined();
      expect(typeof monitor.metrics$.subscribe).toBe('function');
      expect(typeof monitor.resourceChanges$.subscribe).toBe('function');
    });
  });
});