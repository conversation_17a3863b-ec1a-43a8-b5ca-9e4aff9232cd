"""
任务管理API接口
基于browser-viewer的任务调度设计
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

from app.core.database import get_db
from app.core.dependencies import get_current_user, standard_rate_limit
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.schemas.task import (
    TaskCreateRequest,
    TaskResponse,
    TaskUpdate,
    BatchResponse,
    TaskMetrics,
)
from app.services.task_orchestrator import TaskOrchestratorService
from app.services.task_scheduler import TaskSchedulerService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class TaskListResponse(BaseModel):
    """任务列表响应"""

    tasks: List[TaskResponse]
    total: int
    page: int
    size: int


@router.post("/", response_model=TaskResponse)
async def create_task(
    request: TaskCreateRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
):
    """创建新任务"""
    try:
        orchestrator = TaskOrchestratorService(db)
        task = await orchestrator.create_task(request, str(current_user.id))

        logger.info(f"Task created: {task.id} by user {current_user.username}")
        return task

    except Exception as e:
        logger.error(f"Create task error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create task",
        )


@router.post("/batch", response_model=BatchResponse)
async def create_batch_tasks(
    requests: List[TaskCreateRequest],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
):
    """批量创建任务"""
    try:
        if len(requests) > 50:  # 限制批量任务数量
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many tasks in batch (max 50)",
            )

        orchestrator = TaskOrchestratorService(db)
        batch = await orchestrator.submit_batch(requests, str(current_user.id))

        logger.info(f"Batch created: {batch.batch_id} with {batch.task_count} tasks")
        return batch

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create batch tasks error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create batch tasks",
        )


@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[TaskStatus] = Query(None),
    batch_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """获取用户任务列表"""
    try:
        query = db.query(Task).filter(Task.user_id == str(current_user.id))

        # 状态过滤
        if status:
            query = query.filter(Task.status == status)

        # 批次过滤
        if batch_id:
            query = query.filter(Task.batch_id == batch_id)

        # 按创建时间倒序
        query = query.order_by(Task.created_at.desc())

        # 分页
        total = query.count()
        tasks = query.offset((page - 1) * size).limit(size).all()

        return TaskListResponse(
            tasks=[TaskResponse.from_orm(task) for task in tasks],
            total=total,
            page=page,
            size=size,
        )

    except Exception as e:
        logger.error(f"Get tasks error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get tasks",
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """获取任务详情"""
    try:
        task = (
            db.query(Task)
            .filter(Task.id == task_id, Task.user_id == str(current_user.id))
            .first()
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Task not found"
            )

        return TaskResponse.from_orm(task)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get task error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get task",
        )


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: str,
    task_update: TaskUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """更新任务"""
    try:
        task = (
            db.query(Task)
            .filter(Task.id == task_id, Task.user_id == str(current_user.id))
            .first()
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Task not found"
            )

        # 检查任务状态是否允许更新
        if task.status in [TaskStatus.RUNNING, TaskStatus.COMPLETED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot update running or completed task",
            )

        # 更新任务
        update_data = task_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)

        task.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(task)

        logger.info(f"Task updated: {task_id}")
        return TaskResponse.from_orm(task)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update task error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update task",
        )


@router.post("/{task_id}/start")
async def start_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """启动任务"""
    try:
        orchestrator = TaskOrchestratorService(db)
        await orchestrator.start_task(task_id, str(current_user.id))

        logger.info(f"Task started: {task_id}")
        return {"message": "Task started successfully"}

    except Exception as e:
        logger.error(f"Start task error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start task",
        )


@router.post("/{task_id}/pause")
async def pause_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """暂停任务"""
    try:
        orchestrator = TaskOrchestratorService(db)
        await orchestrator.pause_task(task_id, str(current_user.id))

        logger.info(f"Task paused: {task_id}")
        return {"message": "Task paused successfully"}

    except Exception as e:
        logger.error(f"Pause task error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to pause task",
        )


@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """取消任务"""
    try:
        orchestrator = TaskOrchestratorService(db)
        await orchestrator.cancel_task(task_id, str(current_user.id))

        logger.info(f"Task cancelled: {task_id}")
        return {"message": "Task cancelled successfully"}

    except Exception as e:
        logger.error(f"Cancel task error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel task",
        )


@router.get("/{task_id}/metrics", response_model=TaskMetrics)
async def get_task_metrics(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """获取任务指标"""
    try:
        orchestrator = TaskOrchestratorService(db)
        metrics = await orchestrator.get_task_metrics(task_id, str(current_user.id))

        return metrics

    except Exception as e:
        logger.error(f"Get task metrics error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get task metrics",
        )


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """删除任务"""
    try:
        task = (
            db.query(Task)
            .filter(Task.id == task_id, Task.user_id == str(current_user.id))
            .first()
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Task not found"
            )

        # 检查任务状态
        if task.status == TaskStatus.RUNNING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete running task",
            )

        db.delete(task)
        db.commit()

        logger.info(f"Task deleted: {task_id}")
        return {"message": "Task deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete task error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete task",
        )
