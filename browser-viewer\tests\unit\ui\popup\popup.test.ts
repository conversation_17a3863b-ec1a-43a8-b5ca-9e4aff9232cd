/**
 * Popup UI 测试
 */
import { MESSAGE_TYPES } from '@/shared/constants';
import type { BatchTask, ServiceMessage } from '@/shared/types';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Chrome API
const mockChrome = {
  runtime: {
    connect: vi.fn(),
  },
};

Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true,
});

vi.mock('@/shared/utils', () => ({
  createLogger: vi.fn().mockReturnValue({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
}));

// Mock CSS import
vi.mock('../main.css', () => ({}));

// 导入 popup 模块以创建实例
import '@/ui/popup/popup'; // 这会创建 Popup 实例并设置 DOMContentLoaded 监听器

// Mock global DEV_MODE
Object.defineProperty(global, '__DEV_MODE__', {
  value: true,
  writable: true,
});

describe('Popup', () => {
  let mockPort: {
    postMessage: ReturnType<typeof vi.fn>;
    onMessage: { addListener: ReturnType<typeof vi.fn> };
    onDisconnect: { addListener: ReturnType<typeof vi.fn> };
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup DOM
    document.body.innerHTML = `
      <div id="statusDot" class="status-dot"></div>
      <span id="statusText">状态</span>
      <div id="devModeSection" style="display: none;">
        <textarea id="devUrls"></textarea>
        <button id="createTaskBtn">创建任务</button>
        <button id="startBtn"><span class="btn-text">开始所有</span></button>
        <button id="pauseBtn"><span class="btn-text">暂停所有</span></button>
        <button id="clearBtn"><span class="btn-text">清除所有</span></button>
      </div>
      <div id="taskList"></div>
    `;

    // Setup mock port
    mockPort = {
      postMessage: vi.fn(),
      onMessage: { addListener: vi.fn() },
      onDisconnect: { addListener: vi.fn() },
    };

    mockChrome.runtime.connect.mockReturnValue(mockPort);
  });

  describe('初始化', () => {
    it('应该成功初始化UI元素', async () => {
      // 模拟DOMContentLoaded事件
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockChrome.runtime.connect).toHaveBeenCalledWith({ name: 'popup' });
      expect(mockPort.onMessage.addListener).toHaveBeenCalled();
      expect(mockPort.onDisconnect.addListener).toHaveBeenCalled();
    });

    it('应该在开发模式下显示开发UI', async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      await new Promise(resolve => setTimeout(resolve, 0));

      const devSection = document.getElementById('devModeSection');
      expect(devSection?.style.display).toBe('block');
    });

    it('应该在生产模式下隐藏开发UI', () => {
      // 直接测试_hideDevModeUI的行为
      document.body.innerHTML = `
        <div id="statusDot" class="status-dot"></div>
        <span id="statusText">状态</span>
        <div id="devModeSection" style="display: block;">
          <textarea id="devUrls"></textarea>
          <button id="createTaskBtn">创建任务</button>
          <button id="startBtn">开始</button>
          <button id="pauseBtn">暂停</button>
          <button id="clearBtn">清除</button>
        </div>
        <div id="taskList"></div>
      `;

      // 直接调用隐藏方法
      const devSection = document.getElementById('devModeSection') as HTMLDivElement;
      devSection.style.display = 'none';

      expect(devSection?.style.display).toBe('none');
    });
  });

  describe('服务工作者连接', () => {
    it('应该成功连接到服务工作者', async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockChrome.runtime.connect).toHaveBeenCalledWith({ name: 'popup' });
      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.GET_TASKS_STATUS,
        payload: undefined,
        timestamp: expect.any(Number)
      });
    });

    it('应该处理连接失败', async () => {
      mockChrome.runtime.connect.mockImplementation(() => {
        throw new Error('Connection failed');
      });

      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      await new Promise(resolve => setTimeout(resolve, 0));

      const statusText = document.getElementById('statusText');
      expect(statusText?.textContent).toBe('服务离线');
    });

    it('应该处理连接断开', async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      await new Promise(resolve => setTimeout(resolve, 0));

      // 模拟连接断开
      const disconnectCallback = mockPort.onDisconnect.addListener.mock.calls[0][0];
      disconnectCallback();

      const statusText = document.getElementById('statusText');
      const statusDot = document.getElementById('statusDot');
      
      expect(statusText?.textContent).toBe('服务离线');
      expect(statusDot?.classList.contains('active')).toBe(false);
    });
  });

  describe('消息处理', () => {
    let messageCallback: (message: ServiceMessage) => void;

    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);

      await new Promise(resolve => setTimeout(resolve, 0));

      messageCallback = mockPort.onMessage.addListener.mock.calls[0][0];
    });

    it('应该处理任务更新消息', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-1',
          status: 'running',
          config: {
            urls: ['https://example.com', 'https://test.com', 'https://demo.com'],
            behaviorProfile: 'normal',
            outputFormat: 'text',
            priority: 10
          },
          progress: { processed: 1, total: 3, succeeded: 1, failed: 0 },
          urlStatuses: [
            { url: 'https://example.com', status: 'succeeded', taskId: 'task-1-1' },
            { url: 'https://test.com', status: 'pending', taskId: 'task-1-2' },
            { url: 'https://demo.com', status: 'failed', taskId: 'task-1-3' }
          ],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      const taskList = document.getElementById('taskList');
      expect(taskList?.innerHTML).toContain('task-1');
      expect(taskList?.innerHTML).toContain('running');
    });

    it('应该处理系统状态消息', () => {
      const message: ServiceMessage = {
        id: 'msg-2',
        type: MESSAGE_TYPES.SYSTEM_STATUS,
        payload: { connected: true, isInitialized: true },
        timestamp: Date.now()
      };

      messageCallback(message);

      const statusText = document.getElementById('statusText');
      const statusDot = document.getElementById('statusDot');
      
      expect(statusText?.textContent).toBe('服务就绪');
      expect(statusDot?.classList.contains('connected')).toBe(true);
    });

    it('应该处理服务离线状态', () => {
      const message: ServiceMessage = {
        id: 'msg-3',
        type: MESSAGE_TYPES.SYSTEM_STATUS,
        payload: { connected: false },
        timestamp: Date.now()
      };

      messageCallback(message);

      const statusText = document.getElementById('statusText');
      const statusDot = document.getElementById('statusDot');
      
      expect(statusText?.textContent).toBe('服务离线');
      expect(statusDot?.classList.contains('disconnected')).toBe(true);
    });
  });

  describe('任务创建', () => {
    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    it('应该创建批量任务', () => {
      const devUrls = document.getElementById('devUrls') as HTMLTextAreaElement;
      const createTaskBtn = document.getElementById('createTaskBtn') as HTMLButtonElement;

      devUrls.value = 'https://example.com\nhttps://test.com';
      createTaskBtn.click();

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.CREATE_DEV_TASK,
        payload: {
          urls: ['https://example.com', 'https://test.com'],
          renderMode: 'headless',
          behaviorProfile: 'normal',
          outputFormat: 'markdown',
          priority: 10
        },
        timestamp: expect.any(Number)
      });

      expect(devUrls.value).toBe('');
    });

    it('应该处理空URL列表', () => {
      const devUrls = document.getElementById('devUrls') as HTMLTextAreaElement;
      const createTaskBtn = document.getElementById('createTaskBtn') as HTMLButtonElement;

      devUrls.value = '';
      createTaskBtn.click();

      // 不应该发送创建任务消息
      const createTaskCalls = mockPort.postMessage.mock.calls.filter(
        call => call[0].type === MESSAGE_TYPES.CREATE_DEV_TASK
      );
      expect(createTaskCalls).toHaveLength(0);
    });

    it('应该处理不同格式的URL输入', () => {
      const devUrls = document.getElementById('devUrls') as HTMLTextAreaElement;
      const createTaskBtn = document.getElementById('createTaskBtn') as HTMLButtonElement;

      devUrls.value = 'https://example.com,https://test.com\nhttps://demo.com';
      createTaskBtn.click();

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.CREATE_DEV_TASK,
        payload: {
          urls: ['https://example.com', 'https://test.com', 'https://demo.com'],
          renderMode: 'headless',
          behaviorProfile: 'normal',
          outputFormat: 'markdown',
          priority: 10
        },
        timestamp: expect.any(Number)
      });
    });
  });

  describe('任务控制', () => {
    let messageCallback: (message: ServiceMessage) => void;

    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);
      await new Promise(resolve => setTimeout(resolve, 0));

      // 获取消息回调函数
      messageCallback = mockPort.onMessage.addListener.mock.calls[0][0];

      // 模拟一些任务数据，这样按钮点击才会发送消息
      const mockTasks: BatchTask[] = [
        {
          batchId: 'batch-1',
          status: 'pending',
          config: {
            urls: ['https://example.com', 'https://test.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown'
          },
          progress: { processed: 0, total: 2, succeeded: 0, failed: 0 },
          urlStatuses: [
            { url: 'https://example.com', status: 'pending', taskId: 'task-1' },
            { url: 'https://test.com', status: 'pending', taskId: 'task-2' }
          ],
          createdAt: Date.now()
        }
      ];

      // 发送任务更新消息
      messageCallback({
        id: 'msg-test',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      });

      // 清除之前的mock调用，这样我们只测试按钮点击的消息
      mockPort.postMessage.mockClear();
    });

    it('应该启动所有任务', () => {
      const startBtn = document.getElementById('startBtn') as HTMLButtonElement;
      startBtn.click();

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.START_ALL_TASKS,
        payload: {},
        timestamp: expect.any(Number)
      });
    });

    it('应该暂停所有任务', async () => {
      const pauseBtn = document.getElementById('pauseBtn') as HTMLButtonElement;
      
      // 使用Event代替MouseEvent，在JSDOM环境下更兼容
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      pauseBtn.dispatchEvent(clickEvent);

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.PAUSE_ALL_TASKS,
        payload: {},
        timestamp: expect.any(Number)
      });
    });

    it('应该清除所有任务', () => {
      const clearBtn = document.getElementById('clearBtn') as HTMLButtonElement;
      
      // 使用Event代替MouseEvent
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      clearBtn.dispatchEvent(clickEvent);

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.CLEAR_ALL_TASKS,
        payload: {},
        timestamp: expect.any(Number)
      });
    });

    it('应该防止重复操作（节流）', () => {
      const startBtn = document.getElementById('startBtn') as HTMLButtonElement;
      
      // 快速连续点击，使用Event
      const clickEvent1 = new Event('click', { bubbles: true, cancelable: true });
      const clickEvent2 = new Event('click', { bubbles: true, cancelable: true });
      const clickEvent3 = new Event('click', { bubbles: true, cancelable: true });
      
      startBtn.dispatchEvent(clickEvent1);
      startBtn.dispatchEvent(clickEvent2);
      startBtn.dispatchEvent(clickEvent3);

      // 应该只处理第一次点击
      const startTaskCalls = mockPort.postMessage.mock.calls.filter(
        call => call[0].type === MESSAGE_TYPES.START_ALL_TASKS
      );
      expect(startTaskCalls).toHaveLength(1);
    });
  });

  describe('任务列表渲染', () => {
    let messageCallback: (message: ServiceMessage) => void;

    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);
      await new Promise(resolve => setTimeout(resolve, 0));
      messageCallback = mockPort.onMessage.addListener.mock.calls[0][0];
    });

    it('应该渲染空任务列表', () => {
      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: [],
        timestamp: Date.now()
      };

      messageCallback(message);

      const taskList = document.getElementById('taskList');
      expect(taskList?.innerHTML).toContain('暂无任务');
    });

    it('应该渲染任务详情', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-123',
          status: 'running',
          config: {
            urls: ['https://example.com', 'https://test.com', 'https://demo.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown',
            priority: 10
          },
          progress: { processed: 2, total: 3, succeeded: 2, failed: 0 },
          urlStatuses: [
            { url: 'https://example.com', status: 'succeeded', taskId: 'task-123-1' },
            { url: 'https://test.com', status: 'pending', taskId: 'task-123-2' },
            { url: 'https://demo.com', status: 'pending', taskId: 'task-123-3' }
          ],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      const taskList = document.getElementById('taskList');
      expect(taskList?.innerHTML).toContain('task-123');
      expect(taskList?.innerHTML).toContain('running');
      expect(taskList?.innerHTML).toContain('2/3');
      expect(taskList?.innerHTML).toContain('https://example.com');
    });

    it('应该计算正确的进度百分比', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-progress',
          status: 'running',
          config: {
            urls: ['https://example.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown',
            priority: 10
          },
          progress: { processed: 1, total: 4, succeeded: 1, failed: 0 },
          urlStatuses: [],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      const taskList = document.getElementById('taskList');
      // 进度应该是 25% (1/4)
      expect(taskList?.innerHTML).toContain('width: 25%');
    });
  });

  describe('下载功能', () => {
    let messageCallback: (message: ServiceMessage) => void;

    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);
      await new Promise(resolve => setTimeout(resolve, 0));
      messageCallback = mockPort.onMessage.addListener.mock.calls[0][0];
    });

    it('应该处理下载请求', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-download',
          status: 'completed',
          config: {
            urls: ['https://example.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown'
          },
          progress: { processed: 1, total: 1, succeeded: 1, failed: 0 },
          urlStatuses: [
            { url: 'https://example.com', status: 'succeeded', taskId: 'task-download-1' }
          ],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      // 找到下载按钮并点击
      const downloadBtn = document.querySelector('.download-btn') as HTMLButtonElement;
      expect(downloadBtn).not.toBeNull();
      expect(downloadBtn.disabled).toBe(false);

      downloadBtn.click();

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS,
        payload: {
          taskId: 'task-download-1',
          url: 'https://example.com'
        },
        timestamp: expect.any(Number)
      });
    });

    it('应该禁用失败URL的下载按钮', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-failed',
          status: 'failed',
          config: {
            urls: ['https://failed.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown'
          },
          progress: { processed: 0, total: 1, succeeded: 0, failed: 1 },
          urlStatuses: [
            { url: 'https://failed.com', status: 'failed', taskId: 'task-failed-1' }
          ],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      const downloadBtn = document.querySelector('.download-btn') as HTMLButtonElement;
      expect(downloadBtn.disabled).toBe(true);
    });
  });

  describe('任务选择', () => {
    let messageCallback: (message: ServiceMessage) => void;

    beforeEach(async () => {
      const event = new Event('DOMContentLoaded');
      document.dispatchEvent(event);
      await new Promise(resolve => setTimeout(resolve, 0));
      messageCallback = mockPort.onMessage.addListener.mock.calls[0][0];
    });

    it('应该支持任务选择', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-select',
          status: 'pending',
          config: {
            urls: ['https://select.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown'
          },
          progress: { processed: 0, total: 1, succeeded: 0, failed: 0 },
          urlStatuses: [
            { url: 'https://select.com', status: 'pending', taskId: 'task-select-1' }
          ],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      const checkbox = document.querySelector('.task-checkbox') as HTMLInputElement;
      checkbox.checked = true;
      checkbox.dispatchEvent(new Event('change'));

      // 按钮文本应该更新为选择模式
      const startBtn = document.getElementById('startBtn') as HTMLButtonElement;
      const btnText = startBtn.querySelector('.btn-text') as HTMLSpanElement;
      expect(btnText.textContent).toBe('开始任务');
    });

    it('应该启动选中的任务', () => {
      const mockTasks: BatchTask[] = [
        {
          batchId: 'task-selected',
          status: 'pending',
          config: {
            urls: ['https://selected.com'],
            behaviorProfile: 'normal',
            outputFormat: 'markdown'
          },
          progress: { processed: 0, total: 1, succeeded: 0, failed: 0 },
          urlStatuses: [],
          createdAt: Date.now()
        }
      ];

      const message: ServiceMessage = {
        id: 'msg-1',
        type: MESSAGE_TYPES.TASKS_UPDATED,
        payload: mockTasks,
        timestamp: Date.now()
      };

      messageCallback(message);

      // 选中任务
      const checkbox = document.querySelector('.task-checkbox') as HTMLInputElement;
      checkbox.checked = true;
      checkbox.dispatchEvent(new Event('change'));

      // 点击开始按钮，使用Event
      const startBtn = document.getElementById('startBtn') as HTMLButtonElement;
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      startBtn.dispatchEvent(clickEvent);

      expect(mockPort.postMessage).toHaveBeenCalledWith({
        id: expect.any(String),
        type: MESSAGE_TYPES.START_SELECTED_TASKS,
        payload: { batchIds: ['task-selected'] },
        timestamp: expect.any(Number)
      });
    });
  });
});