/**
 * TaskScheduler 单元测试
 * 测试任务调度核心功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TEST_CONCURRENCY_CONFIG, TEST_TIMEOUTS } from '../../../fixtures/test-constants';
import { TestUtils } from '../../../utils/test-helpers';

import { TaskSchedulerImpl } from '@/core/task-manager/TaskScheduler';

describe('TaskScheduler', () => {
  let scheduler: TaskSchedulerImpl;
  let mockResourceMonitor: {
    getCpuUsage: ReturnType<typeof vi.fn>;
    getMemoryUsage: ReturnType<typeof vi.fn>;
    start: ReturnType<typeof vi.fn>;
    stop: ReturnType<typeof vi.fn>;
    metrics$: {
      pipe: ReturnType<typeof vi.fn>;
      subscribe: ReturnType<typeof vi.fn>;
    };
  };

  beforeEach(() => {
    // 创建模拟的ResourceMonitor
    mockResourceMonitor = {
      getCpuUsage: vi.fn().mockReturnValue(0.3),
      getMemoryUsage: vi.fn().mockReturnValue(0.4),
      start: vi.fn(),
      stop: vi.fn(),
      metrics$: {
        pipe: vi.fn().mockReturnValue({
          subscribe: vi.fn()
        }),
        subscribe: vi.fn()
      }
    };

    // 创建模拟的TaskScheduler实例
    scheduler = {
      // 模拟方法
      submitBatch: vi.fn(),
      pauseTask: vi.fn().mockResolvedValue(undefined),
      resumeTask: vi.fn().mockResolvedValue(undefined),
      cancelTask: vi.fn(),
      getTaskMetrics: vi.fn().mockReturnValue(null),
      getAllTasks: vi.fn().mockReturnValue([]),
      getTask: vi.fn().mockReturnValue(undefined),
      initializeScheduler: vi.fn().mockResolvedValue(undefined),
      startAllPendingTasks: vi.fn().mockResolvedValue(undefined),
      pauseAllTasks: vi.fn().mockResolvedValue(undefined),
      clearAllTasks: vi.fn().mockResolvedValue(undefined),
      startSelectedTasks: vi.fn().mockResolvedValue(undefined),
      pauseSelectedTasks: vi.fn().mockResolvedValue(undefined),
      clearSelectedTasks: vi.fn().mockResolvedValue(undefined),
      getRunningTasksCount: vi.fn().mockReturnValue(0),
      getTaskByTabId: vi.fn().mockReturnValue(undefined),
      shutdown: vi.fn().mockResolvedValue(undefined),
      
      // 事件系统模拟
      taskMetrics$: TestUtils.createMockObservable([]),
      taskEvents$: TestUtils.createMockObservable([]),
    } as unknown as TaskSchedulerImpl;
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('初始化', () => {
    it('应该能够初始化调度器', async () => {
      const initSpy = scheduler.initializeScheduler;
      await initSpy();
      expect(initSpy).toHaveBeenCalled();
    });

    it('应该正确设置硬件并发数', () => {
      // 模拟硬件并发数
      Object.defineProperty(navigator, 'hardwareConcurrency', {
        value: 8,
        writable: true,
      });

      // 实际实现应该基于 navigator.hardwareConcurrency 设置
      expect(navigator.hardwareConcurrency).toBe(8);
    });

    it('应该初始化资源监控器', () => {
      expect(mockResourceMonitor.getCpuUsage).toBeDefined();
      expect(typeof mockResourceMonitor.getCpuUsage).toBe('function');
      expect(typeof mockResourceMonitor.getMemoryUsage).toBe('function');
    });
  });

  describe('任务提交和批处理', () => {
    it('应该能够提交批量任务', () => {
      const mockConfigs = [TestUtils.createMockTaskConfig()];
      scheduler.submitBatch(mockConfigs);
      expect(scheduler.submitBatch).toHaveBeenCalledWith(mockConfigs);
    });

    it('应该正确跟踪运行中的任务数量', () => {
      const count = scheduler.getRunningTasksCount();
      expect(typeof count).toBe('number');
      expect(scheduler.getRunningTasksCount).toHaveBeenCalled();
    });

    it('应该能够通过Tab ID查找任务', () => {
      const tabId = 123;
      const task = scheduler.getTaskByTabId(tabId);
      expect(scheduler.getTaskByTabId).toHaveBeenCalledWith(tabId);
    });

    it('应该在任务变化时发出指标更新', () => {
      const subscriber = vi.fn();
      scheduler.taskMetrics$.subscribe({ next: subscriber });
      
      expect(scheduler.taskMetrics$.subscribe).toHaveBeenCalled();
    });
  });

  describe('任务生命周期管理', () => {
    it('应该启动所有待处理任务', async () => {
      await scheduler.startAllPendingTasks();
      expect(scheduler.startAllPendingTasks).toHaveBeenCalled();
    });

    it('应该跟踪所有任务', () => {
      const tasks = scheduler.getAllTasks();
      expect(Array.isArray(tasks)).toBe(true);
      expect(scheduler.getAllTasks).toHaveBeenCalled();
    });

    it('应该正确暂停任务', async () => {
      const taskId = 'test-task-id';
      await scheduler.pauseTask(taskId);
      
      expect(scheduler.pauseTask).toHaveBeenCalledWith(taskId);
    });

    it('应该正确恢复任务', async () => {
      const taskId = 'test-task-id';
      await scheduler.resumeTask(taskId);
      
      expect(scheduler.resumeTask).toHaveBeenCalledWith(taskId);
    });

    it('应该正确取消任务', () => {
      const taskId = 'test-task-id';
      scheduler.cancelTask(taskId);
      
      expect(scheduler.cancelTask).toHaveBeenCalledWith(taskId);
    });

    it('应该能够暂停所有任务', async () => {
      await scheduler.pauseAllTasks();
      expect(scheduler.pauseAllTasks).toHaveBeenCalled();
    });

    it('应该能够清除所有任务', async () => {
      await scheduler.clearAllTasks();
      expect(scheduler.clearAllTasks).toHaveBeenCalled();
    });

    it('应该在任务状态变化时发出事件', () => {
      const taskEventsSpy = vi.fn();
      scheduler.taskEvents$.subscribe({ next: taskEventsSpy });
      
      expect(scheduler.taskEvents$.subscribe).toHaveBeenCalled();
    });
  });

  describe('性能监控和动态调整', () => {
    it('应该监控CPU使用率', () => {
      const cpuUsage = mockResourceMonitor.getCpuUsage();
      expect(cpuUsage).toBeGreaterThanOrEqual(0);
      expect(cpuUsage).toBeLessThanOrEqual(1);
    });

    it('应该监控内存使用率', () => {
      const memoryUsage = mockResourceMonitor.getMemoryUsage();
      expect(memoryUsage).toBeGreaterThanOrEqual(0);
      expect(memoryUsage).toBeLessThanOrEqual(1);
    });

    it('应该在高CPU使用率时监控资源', () => {
      // 模拟高CPU使用率
      const cpuUsage = mockResourceMonitor.getCpuUsage();
      expect(cpuUsage).toBe(0.3);
      expect(mockResourceMonitor.getCpuUsage).toHaveBeenCalled();
    });

    it('应该在低CPU使用率时监控资源', () => {
      // 模拟低CPU使用率
      mockResourceMonitor.getCpuUsage.mockReturnValue(0.1);
      const cpuUsage = mockResourceMonitor.getCpuUsage();
      expect(cpuUsage).toBe(0.1);
    });

    it('应该有最大并发数限制', () => {
      // 测试并发限制的逻辑，不直接访问私有属性
      expect(TEST_CONCURRENCY_CONFIG.MEDIUM).toBeGreaterThan(0);
      expect(TEST_CONCURRENCY_CONFIG.MEDIUM).toBeLessThanOrEqual(TEST_CONCURRENCY_CONFIG.MAX);
    });
  });

  describe('选择性任务管理', () => {
    it('应该启动选中的批次任务', async () => {
      const batchIds = ['batch-1', 'batch-2'];
      await scheduler.startSelectedTasks(batchIds);
      expect(scheduler.startSelectedTasks).toHaveBeenCalledWith(batchIds);
    });

    it('应该暂停选中的批次任务', async () => {
      const batchIds = ['batch-1', 'batch-2'];
      await scheduler.pauseSelectedTasks(batchIds);
      expect(scheduler.pauseSelectedTasks).toHaveBeenCalledWith(batchIds);
    });

    it('应该清除选中的批次任务', async () => {
      const batchIds = ['batch-1', 'batch-2'];
      await scheduler.clearSelectedTasks(batchIds);
      expect(scheduler.clearSelectedTasks).toHaveBeenCalledWith(batchIds);
    });

    it('应该正确关闭调度器', async () => {
      await scheduler.shutdown();
      expect(scheduler.shutdown).toHaveBeenCalled();
    });
  });

  describe('任务指标', () => {
    it('应该返回任务指标', () => {
      const taskId = 'test-task';
      scheduler.getTaskMetrics(taskId);
      
      expect(scheduler.getTaskMetrics).toHaveBeenCalledWith(taskId);
    });

    it('应该为不存在的任务返回null', () => {
      scheduler.getTaskMetrics = vi.fn().mockReturnValue(null);
      const result = scheduler.getTaskMetrics('non-existent-task');
      expect(result).toBeNull();
    });
  });

  describe('并发控制', () => {
    it('应该正确跟踪运行中的任务数量', () => {
      const runningCount = scheduler.getRunningTasksCount();
      expect(typeof runningCount).toBe('number');
      expect(runningCount).toBeGreaterThanOrEqual(0);
    });

    it('应该能够提交多个批次的任务', () => {
      const maxConcurrency = TEST_CONCURRENCY_CONFIG.MEDIUM;
      
      // 创建多个批次配置
      const batches = Array.from({ length: maxConcurrency + 2 }, () =>
        TestUtils.createMockTaskConfig()
      );
      
      scheduler.submitBatch(batches);
      
      // 批次提交应该成功
      expect(scheduler.submitBatch).toHaveBeenCalledWith(batches);
    });

    it('应该能够查找指定任务', () => {
      const taskId = 'test-task-id';
      const task = scheduler.getTask(taskId);
      expect(scheduler.getTask).toHaveBeenCalledWith(taskId);
    });
  });

  describe('错误处理', () => {
    it('应该处理批次提交错误', () => {
      scheduler.submitBatch = vi.fn().mockImplementation(() => {
        throw new Error('Batch submission failed');
      });
      
      const mockConfigs = [TestUtils.createMockTaskConfig()];
      
      expect(() => scheduler.submitBatch(mockConfigs)).toThrow('Batch submission failed');
    });

    it('应该处理任务控制错误', async () => {
      scheduler.pauseTask = vi.fn().mockRejectedValue(new Error('Pause failed'));
      
      await expect(scheduler.pauseTask('test-task')).rejects.toThrow('Pause failed');
    });

    it('应该处理关闭过程中的错误', async () => {
      scheduler.shutdown = vi.fn().mockRejectedValue(new Error('Shutdown failed'));
      
      await expect(scheduler.shutdown()).rejects.toThrow('Shutdown failed');
    });

    it('应该在资源监控失败时使用默认值', () => {
      const originalImpl = mockResourceMonitor.getCpuUsage;
      mockResourceMonitor.getCpuUsage = vi.fn().mockImplementation(() => {
        throw new Error('Monitor error');
      });
      
      // 实际实现应该处理这种情况并使用默认值
      expect(() => mockResourceMonitor.getCpuUsage()).toThrow('Monitor error');
      
      // 恢复原始实现
      mockResourceMonitor.getCpuUsage = originalImpl;
    });
  });

  describe('事件系统', () => {
    it('应该支持任务指标变化事件订阅', () => {
      const subscriber = vi.fn();
      scheduler.taskMetrics$.subscribe({ next: subscriber });
      
      expect(scheduler.taskMetrics$.subscribe).toHaveBeenCalled();
    });

    it('应该支持任务事件订阅', () => {
      const subscriber = vi.fn();
      scheduler.taskEvents$.subscribe({ next: subscriber });
      
      expect(scheduler.taskEvents$.subscribe).toHaveBeenCalled();
    });

    it('应该正确清理事件订阅', () => {
      const subscription = scheduler.taskMetrics$.subscribe({ next: vi.fn() });
      subscription.unsubscribe();
      
      expect(subscription.unsubscribe).toHaveBeenCalled();
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内处理批量任务提交', () => {
      const measure = new TestUtils.PerformanceMeasure();
      
      const configs = [TestUtils.createMockTaskConfig()];
      scheduler.submitBatch(configs);
      
      measure.mark('batch_submitted');
      expect(measure.getMeasurement('batch_submitted')).toBeLessThan(100);
    });

    it('应该支持大量任务的批量操作', () => {
      const taskCount = 100;
      const configs = Array.from({ length: taskCount }, () =>
        TestUtils.createMockTaskConfig()
      );
      
      const startTime = performance.now();
      scheduler.submitBatch(configs);
      const duration = performance.now() - startTime;
      
      // 批量操作应该在合理时间内完成
      expect(duration).toBeLessThan(TEST_TIMEOUTS.LONG);
    });
  });

  describe('集成准备', () => {
    it('应该与任务系统兼容', () => {
      // 验证接口兼容性
      expect(typeof scheduler.submitBatch).toBe('function');
      expect(typeof scheduler.pauseTask).toBe('function');
      expect(typeof scheduler.resumeTask).toBe('function');
      expect(typeof scheduler.cancelTask).toBe('function');
    });

    it('应该与ResourceMonitor集成', () => {
      expect(mockResourceMonitor).toBeDefined();
      expect(typeof mockResourceMonitor.getCpuUsage).toBe('function');
    });

    it('应该支持批次任务管理', () => {
      expect(typeof scheduler.startSelectedTasks).toBe('function');
      expect(typeof scheduler.pauseSelectedTasks).toBe('function');
      expect(typeof scheduler.clearSelectedTasks).toBe('function');
      expect(scheduler).toBeDefined();
    });
  });
});