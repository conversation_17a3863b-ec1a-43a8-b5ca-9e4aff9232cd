"""
数懒平台配置管理
支持环境变量和配置文件
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""

    # 基础配置
    APP_NAME: str = "数懒平台"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"

    # 数据库配置
    DATABASE_URL: str = (
        "postgresql://user:password@localhost:5432/digital_lazy_platform"
    )
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0

    # 消息队列配置
    RABBITMQ_URL: str = "amqp://guest:guest@localhost:5672/"
    TASK_QUEUE_NAME: str = "task_queue"
    DATA_QUEUE_NAME: str = "data_queue"

    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [".jpg", ".jpeg", ".png", ".pdf", ".txt"]

    # AI服务配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4-vision-preview"
    OPENAI_MAX_TOKENS: int = 4000

    # 任务调度配置
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT_SECONDS: int = 300
    RETRY_MAX_ATTEMPTS: int = 3
    RETRY_DELAY_SECONDS: int = 5

    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # CORS配置
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # 浏览器插件配置
    EXTENSION_AUTH_TIMEOUT: int = 300  # 5分钟
    EXTENSION_HEARTBEAT_INTERVAL: int = 30  # 30秒

    # 数据处理配置
    IMAGE_PROCESSING_QUALITY: int = 85
    TEXT_EXTRACTION_CONFIDENCE: float = 0.8
    DATA_RETENTION_DAYS: int = 30

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 确保上传目录存在
upload_path = Path(settings.UPLOAD_DIR)
upload_path.mkdir(exist_ok=True)

# 开发环境特殊配置
if settings.DEBUG:
    settings.LOG_LEVEL = "DEBUG"
    settings.ALLOWED_HOSTS.extend(["http://localhost:*", "http://127.0.0.1:*"])


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.DATABASE_URL


def get_redis_url() -> str:
    """获取Redis连接URL"""
    if settings.REDIS_PASSWORD:
        return f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_URL.split('@')[-1]}"
    return settings.REDIS_URL


def get_upload_path() -> Path:
    """获取上传文件路径"""
    return Path(settings.UPLOAD_DIR)


def is_development() -> bool:
    """判断是否为开发环境"""
    return settings.DEBUG


def get_cors_origins() -> List[str]:
    """获取CORS允许的源"""
    return settings.ALLOWED_HOSTS
