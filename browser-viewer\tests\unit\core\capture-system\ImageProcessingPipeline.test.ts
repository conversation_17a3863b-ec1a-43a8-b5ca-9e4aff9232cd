/**
 * ImageProcessingPipeline 测试
 */
import { ImageProcessingPipeline } from '@/core/capture-system/ImageProcessingPipeline';
import { MESSAGE_TYPES } from '@/shared/constants';
import type { ContentSegment, ImageSegment } from '@/shared/types';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
  },
};

Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true,
});

vi.mock('@/shared/utils', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  })),
}));

describe('ImageProcessingPipeline', () => {
  let pipeline: ImageProcessingPipeline;

  beforeEach(() => {
    vi.clearAllMocks();
    pipeline = new ImageProcessingPipeline();
  });

  describe('初始化', () => {
    it('应该成功初始化管道', () => {
      expect(pipeline).toBeInstanceOf(ImageProcessingPipeline);
    });
  });

  describe('图像分段处理', () => {
    it('应该处理空分段数组', async () => {
      const result = await pipeline.processSegments([]);
      expect(result).toEqual([]);
      expect(mockChrome.runtime.sendMessage).not.toHaveBeenCalled();
    });

    it('应该成功处理图像分段', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 100, height: 100 },
          timestamp: Date.now()
        },
        {
          id: 'segment-2', 
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 100, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      const mockProcessedSegments: ContentSegment[] = [
        {
          id: 'content-1',
          content: 'Processed text 1',
          type: 'text',
          position: { x: 0, y: 0, width: 100, height: 100 },
          confidence: 0.95
        },
        {
          id: 'content-2',
          content: 'Processed text 2', 
          type: 'text',
          position: { x: 0, y: 100, width: 100, height: 100 },
          confidence: 0.90
        }
      ];

      mockChrome.runtime.sendMessage.mockResolvedValue(mockProcessedSegments);

      const result = await pipeline.processSegments(mockSegments);

      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.PROCESS_IMAGE_SEGMENTS,
        payload: { segments: mockSegments }
      });

      expect(result).toEqual(mockProcessedSegments);
      expect(result.length).toBe(2);
    });

    it('应该正确排序处理后的分段', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 200, width: 100, height: 100 },
          timestamp: Date.now()
        },
        {
          id: 'segment-2',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 100, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      const mockProcessedSegments: ContentSegment[] = [
        {
          id: 'content-1',
          content: 'Text at y=200',
          type: 'text',
          position: { x: 0, y: 200, width: 100, height: 100 },
          confidence: 0.95
        },
        {
          id: 'content-2', 
          content: 'Text at y=100',
          type: 'text',
          position: { x: 0, y: 100, width: 100, height: 100 },
          confidence: 0.90
        }
      ];

      mockChrome.runtime.sendMessage.mockResolvedValue(mockProcessedSegments);

      const result = await pipeline.processSegments(mockSegments);

      // 结果应该按y位置排序
      expect(result[0].position.y).toBeLessThan(result[1].position.y);
      expect(result[0].content).toBe('Text at y=100');
      expect(result[1].content).toBe('Text at y=200');
    });

    it('应该处理单个分段', async () => {
      const mockSegment: ImageSegment = {
        id: 'single-segment',
        data: new ArrayBuffer(50),
        mimeType: 'image/png',
        position: { x: 0, y: 0, width: 100, height: 100 },
        timestamp: Date.now()
      };

      const mockProcessedSegment: ContentSegment = {
        id: 'single-content',
        content: 'Single processed text',
        type: 'text',
        position: { x: 0, y: 0, width: 100, height: 100 },
        confidence: 0.98
      };

      mockChrome.runtime.sendMessage.mockResolvedValue([mockProcessedSegment]);

      const result = await pipeline.processSegments([mockSegment]);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockProcessedSegment);
    });
  });

  describe('错误处理', () => {
    it('应该处理消息发送失败', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      mockChrome.runtime.sendMessage.mockRejectedValue(new Error('Message sending failed'));

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toEqual([]);
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.PROCESS_IMAGE_SEGMENTS,
        payload: { segments: mockSegments }
      });
    });

    it('应该处理后台脚本不可用的情况', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      mockChrome.runtime.sendMessage.mockRejectedValue(
        new Error('Could not establish connection. Receiving end does not exist.')
      );

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toEqual([]);
    });

    it('应该处理无效响应', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      mockChrome.runtime.sendMessage.mockResolvedValue(null);

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toEqual([]);
    });

    it('应该处理网络超时', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'segment-1',
          data: new ArrayBuffer(100),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 100, height: 100 },
          timestamp: Date.now()
        }
      ];

      mockChrome.runtime.sendMessage.mockRejectedValue(new Error('Timeout'));

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toEqual([]);
    });
  });

  describe('大批量处理', () => {
    it('应该处理大量分段', async () => {
      const mockSegments: ImageSegment[] = Array.from({ length: 100 }, (_, i) => ({
        id: `segment-${i}`,
        data: new ArrayBuffer(100),
        mimeType: 'image/png' as const,
        position: { x: 0, y: i * 100, width: 100, height: 100 },
        timestamp: Date.now()
      }));

      const mockProcessedSegments: ContentSegment[] = mockSegments.map((segment, i) => ({
        id: `content-${i}`,
        content: `Processed text ${i}`,
        type: 'text' as const,
        position: segment.position,
        confidence: 0.90 + (i % 10) * 0.01
      }));

      mockChrome.runtime.sendMessage.mockResolvedValue(mockProcessedSegments);

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toHaveLength(100);
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.PROCESS_IMAGE_SEGMENTS,
        payload: { segments: mockSegments }
      });
    });

    it('应该处理不同尺寸的分段', async () => {
      const mockSegments: ImageSegment[] = [
        {
          id: 'small-segment',
          data: new ArrayBuffer(25),
          mimeType: 'image/png',
          position: { x: 0, y: 0, width: 50, height: 50 },
          timestamp: Date.now()
        },
        {
          id: 'large-segment',
          data: new ArrayBuffer(400),
          mimeType: 'image/png',
          position: { x: 0, y: 50, width: 200, height: 200 },
          timestamp: Date.now()
        }
      ];

      const mockProcessedSegments: ContentSegment[] = [
        {
          id: 'small-content',
          content: 'Small text',
          type: 'text',
          position: { x: 0, y: 0, width: 50, height: 50 },
          confidence: 0.85
        },
        {
          id: 'large-content',
          content: 'Large text content',
          type: 'text',
          position: { x: 0, y: 50, width: 200, height: 200 },
          confidence: 0.95
        }
      ];

      mockChrome.runtime.sendMessage.mockResolvedValue(mockProcessedSegments);

      const result = await pipeline.processSegments(mockSegments);

      expect(result).toHaveLength(2);
      expect(result[0].position.width).toBe(50);
      expect(result[1].position.width).toBe(200);
    });
  });

  describe('性能测试', () => {
    it('应该快速处理空数组', async () => {
      const startTime = performance.now();
      const result = await pipeline.processSegments([]);
      const endTime = performance.now();

      expect(result).toEqual([]);
      expect(endTime - startTime).toBeLessThan(10); // 应该在10ms内完成
    });

    it('应该有效处理中等大小的批次', async () => {
      const mockSegments: ImageSegment[] = Array.from({ length: 10 }, (_, i) => ({
        id: `segment-${i}`,
        data: new ArrayBuffer(100),
        mimeType: 'image/png' as const,
        position: { x: 0, y: i * 100, width: 100, height: 100 },
        timestamp: Date.now()
      }));

      const mockProcessedSegments: ContentSegment[] = mockSegments.map((segment, i) => ({
        id: `content-${i}`,
        content: `Text ${i}`,
        type: 'text' as const,
        position: segment.position,
        confidence: 0.90
      }));

      mockChrome.runtime.sendMessage.mockResolvedValue(mockProcessedSegments);

      const startTime = performance.now();
      const result = await pipeline.processSegments(mockSegments);
      const endTime = performance.now();

      expect(result).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
    });
  });
});