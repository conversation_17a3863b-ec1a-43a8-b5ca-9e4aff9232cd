/**
 * 策略选择器
 * 根据URL动态选择最合适的数据提取策略。
 */
import { MESSAGE_TYPES } from '@/shared/constants';
import { createLogger, Logger } from '@/shared/utils';
import { DefaultScreenshotStrategy } from './DefaultScreenshotStrategy';
import { ExtractionStrategy, StrategyMessageBuilder } from './ExtractionStrategy';
import { JdProductStrategy } from './JdProductStrategy';

type StrategyDefinition = {
  pattern: RegExp;
  strategy: () => ExtractionStrategy;
  messageBuilder: StrategyMessageBuilder;
};

export class StrategySelector {
  private readonly logger: Logger = createLogger('StrategySelector');

  // 策略注册表
  // 注意：顺序很重要，选择器会使用第一个匹配的策略。
  private readonly strategies: StrategyDefinition[] = [
    {
      pattern: /item\.jd\.com/,
      strategy: () => new JdProductStrategy(this.logger),
      messageBuilder: (payload) => ({ type: MESSAGE_TYPES.STRUCTURED_DATA_CAPTURED, payload }),
    },
    // --- 在此添加更多特定网站的策略 ---
  ];

  private readonly defaultStrategy: StrategyDefinition = {
    pattern: /.*/,
    strategy: () => new DefaultScreenshotStrategy(this.logger),
    messageBuilder: (payload) => ({ type: MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED, payload }),
  };

  /**
   * 根据提供的URL选择合适的提取策略及其消息构建器。
   * @param url 要评估的URL。
   * @returns 返回一个包含策略实例和相应消息构建器的对象。
   */
  public select(url: string): { strategy: ExtractionStrategy; messageBuilder: StrategyMessageBuilder } {
    const found = this.strategies.find(s => s.pattern.test(url));
    if (found) {
      this.logger.info(`URL "${url}" matched pattern "${found.pattern}". Using strategy: ${found.strategy().constructor.name}`);
      return { strategy: found.strategy(), messageBuilder: found.messageBuilder };
    }

    this.logger.info(`URL "${url}" did not match any specific pattern. Using default strategy.`);
    return { strategy: this.defaultStrategy.strategy(), messageBuilder: this.defaultStrategy.messageBuilder };
  }
} 