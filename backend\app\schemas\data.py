"""
数据相关Schema
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from datetime import datetime

from app.models.data import DataType, DataStatus


class DataResponse(BaseModel):
    """数据响应模型"""

    id: str
    name: str
    description: Optional[str]
    data_type: DataType
    status: DataStatus
    user_id: str
    task_id: Optional[str]
    raw_data: Optional[Dict[str, Any]]
    processed_data: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]
    file_path: Optional[str]
    file_size: Optional[int]
    file_type: Optional[str]
    processing_time: Optional[float]
    error_message: Optional[str]
    access_count: int
    last_accessed: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class DataQuery(BaseModel):
    """数据查询模型"""

    data_type: Optional[DataType] = None
    status: Optional[DataStatus] = None
    task_id: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search_text: Optional[str] = None
    tags: Optional[List[str]] = None
    page: int = 1
    size: int = 20


class DataUploadResponse(BaseModel):
    """数据上传响应"""

    data_id: str
    file_name: str
    file_size: int
    file_type: str
    upload_time: datetime
    message: str = "文件上传成功"


class DataProcessRequest(BaseModel):
    """数据处理请求"""

    data_id: str
    processing_type: str
    parameters: Optional[Dict[str, Any]] = None


class DataExportRequest(BaseModel):
    """数据导出请求"""

    data_ids: List[str]
    export_format: str = "json"  # json, csv, excel
    include_metadata: bool = True


class DataExportResponse(BaseModel):
    """数据导出响应"""

    export_id: str
    file_url: str
    file_size: int
    export_time: datetime
    expires_at: datetime
