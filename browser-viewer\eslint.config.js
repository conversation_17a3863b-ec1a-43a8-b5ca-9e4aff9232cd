import vuePlugin from 'eslint-plugin-vue';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import vueParser from 'vue-eslint-parser';

export default tseslint.config(
  // Global ignores
  {
    ignores: [
      'dist/',
      'node_modules/',
      'coverage/',
      '**/*.d.ts',
    ],
  },
  
  // Base configs
  ...tseslint.configs.recommendedTypeChecked,
  ...vuePlugin.configs['flat/recommended'],

  // Configuration for TypeScript files
  {
    files: ['**/*.{ts,js,mjs,cjs}'],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.webextensions,
        __DEV__: 'readonly',
        __PROD__: 'readonly',
        __VERSION__: 'readonly',
        __BUILD_TIME__: 'readonly',
      },
    },
  },

  // Configuration for Vue files
  {
    files: ['**/*.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: tseslint.parser,
        project: true,
        tsconfigRootDir: import.meta.dirname,
        extraFileExtensions: ['.vue'],
      },
      globals: {
        ...globals.browser,
        ...globals.webextensions,
        __DEV__: 'readonly',
        __PROD__: 'readonly',
        __VERSION__: 'readonly',
        __BUILD_TIME__: 'readonly',
      },
    },
  },

  // Global rules overrides
  {
    rules: {
      'vue/multi-word-component-names': 'off',
      'vue/no-unused-vars': 'warn',
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-non-null-assertion': 'warn',
      'prefer-const': 'error',
      'no-var': 'error',
    },
  }
); 