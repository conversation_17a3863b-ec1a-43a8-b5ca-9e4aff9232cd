/**
 * 数懒孪生代理 - 共享工具函数
 *
 * 该文件作为共享工具的主入口，导出所有纯函数和依赖扩展API的函数。
 */

// 首先，从纯工具模块中导出所有与环境无关的函数
export * from './image-utils';
export * from './pure-utils';

import browser from 'webextension-polyfill';

// DOM/BOM 相关工具 (依赖浏览器环境)
export function waitForElement(selector: string, timeout: number = 5000): Promise<Element> {
  return new Promise((resolve, reject) => {
    const el = document.querySelector(selector);
    if (el) {
      resolve(el);
      return;
    }

    const observer = new MutationObserver(() => {
      const el = document.querySelector(selector);
      if (el) {
        observer.disconnect();
        resolve(el);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

export function getViewportSize(): { width: number; height: number } {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
}

export function getScrollPosition(): { x: number; y: number } {
  return {
    x: window.scrollX,
    y: window.scrollY,
  };
}


// 浏览器/扩展API相关工具
/**
 * 检查当前浏览器是否为基于Chromium的浏览器。
 * @returns {boolean} 如果是Chromium则为true，否则为false。
 */
export function isChromiumBrowser(): boolean {
  // `chrome.runtime.id` 在非Chromium浏览器（如Firefox）中可能不存在
  // 此外，检查 `navigator.userAgent` 是一种更可靠的后备方法
  return (
    typeof chrome !== 'undefined' &&
    typeof chrome.runtime?.id !== 'undefined' &&
    (navigator.userAgent.includes('Chrome') || navigator.userAgent.includes('Edg'))
  );
}

/**
 * 从浏览器扩展的本地存储中异步获取数据。
 * @param key - 要获取的数据的键。
 * @param defaultValue - 如果未找到键，则返回的默认值。
 * @returns {Promise<T>} 返回存储的数据或默认值。
 */
export async function getStorageData<T>(key: string, defaultValue: T): Promise<T> {
  const result = await browser.storage.local.get(key);
  return (result[key] as T) ?? defaultValue;
}

/**
 * 将数据异步设置到浏览器扩展的本地存储中。
 * @param key - 要设置的数据的键。
 * @param value - 要设置的值。
 */
export async function setStorageData<T>(key:string, value: T): Promise<void> {
  await browser.storage.local.set({ [key]: value });
}


/**
 * 测试给定的图像URL是否可以通过消息传递从后台脚本成功获取。
 * @param imageUrl - 要测试的图像URL。
 * @returns {Promise<boolean>} 如果图像可以被获取，则解析为true的Promise。
 */
export function testImageProxy(imageUrl: string): Promise<boolean> {
  return new Promise((resolve) => {
    const timeoutId = setTimeout(() => {
      cleanup();
      resolve(false);
    }, 2000);

    const messageHandler = (message: unknown) => {
      // 假设的响应消息格式
      const data = message as { type: string; success: boolean, url: string };
      if (data.type === 'PROXY_IMAGE_RESPONSE' && data.url === imageUrl) {
        cleanup();
        resolve(data.success);
      }
    };
    
    const cleanup = () => {
      clearTimeout(timeoutId);
      chrome.runtime.onMessage.removeListener(messageHandler);
    };

    chrome.runtime.onMessage.addListener(messageHandler);
    
    // 假设的消息格式
    void chrome.runtime.sendMessage({
      type: 'PROXY_IMAGE_REQUEST',
      payload: { url: imageUrl },
    });
  });
}