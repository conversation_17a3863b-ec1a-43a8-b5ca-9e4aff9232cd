"""
数懒平台后端服务 - 简化版本用于测试
不依赖数据库和Redis的基本FastAPI服务
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="数懒平台API - 测试版",
    description="基于browser-viewer的智能浏览代理平台后端服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


@app.get("/")
async def root():
    """根路径 - 服务状态检查"""
    return {
        "service": "数懒平台后端服务",
        "version": "1.0.0",
        "status": "running",
        "description": "基于browser-viewer的智能浏览代理平台",
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "database": "not_connected",
        "redis": "not_connected",
        "timestamp": "2025-07-14T00:00:00Z",
    }


@app.get("/api/v1/test")
async def test_api():
    """测试API接口"""
    return {
        "message": "API测试成功",
        "data": {
            "frontend_url": "http://localhost:3000",
            "backend_url": "http://localhost:8000",
            "extension_support": True
        }
    }


@app.post("/api/v1/extension/connect")
async def extension_connect(data: dict):
    """浏览器插件连接接口"""
    return {
        "status": "connected",
        "message": "浏览器插件连接成功",
        "session_id": "test_session_123",
        "data": data
    }


if __name__ == "__main__":
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
