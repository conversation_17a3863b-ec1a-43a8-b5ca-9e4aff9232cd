/**
 * 数懒平台前端主应用
 * 基于React 18 + TypeScript + Ant Design
 */

import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntApp, theme } from 'antd';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 设置dayjs中文
dayjs.locale('zh-cn');

// 导入页面组件
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/Common/LoadingSpinner';
import ErrorBoundary from './components/Common/ErrorBoundary';

// 懒加载页面组件
const Dashboard = React.lazy(() => import('./pages/Dashboard/Dashboard'));
const TaskManagement = React.lazy(() => import('./pages/Tasks/TaskManagement'));
const TaskDetail = React.lazy(() => import('./pages/Tasks/TaskDetail'));
const DataVisualization = React.lazy(() => import('./pages/Data/DataVisualization'));
const UserProfile = React.lazy(() => import('./pages/User/UserProfile'));
const Settings = React.lazy(() => import('./pages/Settings/Settings'));
const Login = React.lazy(() => import('./pages/Auth/Login'));

// 导入状态管理
import { useAuthStore } from './stores/authStore';
import { useThemeStore } from './stores/themeStore';

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    },
    mutations: {
      retry: 1,
    },
  },
});

// 路由保护组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// 主应用组件
const App: React.FC = () => {
  const { isDarkMode } = useThemeStore();
  
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
              fontSize: 14,
            },
            components: {
              Layout: {
                headerBg: isDarkMode ? '#001529' : '#ffffff',
                siderBg: isDarkMode ? '#001529' : '#ffffff',
              },
              Menu: {
                darkItemBg: '#001529',
                darkSubMenuItemBg: '#000c17',
              },
            },
          }}
        >
          <AntApp>
            <Router>
              <div className="app">
                <Routes>
                  {/* 登录页面 */}
                  <Route
                    path="/login"
                    element={
                      <Suspense fallback={<LoadingSpinner />}>
                        <Login />
                      </Suspense>
                    }
                  />
                  
                  {/* 主应用路由 */}
                  <Route
                    path="/*"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<LoadingSpinner />}>
                            <Routes>
                              {/* 仪表板 */}
                              <Route path="/" element={<Dashboard />} />
                              <Route path="/dashboard" element={<Dashboard />} />
                              
                              {/* 任务管理 */}
                              <Route path="/tasks" element={<TaskManagement />} />
                              <Route path="/tasks/:taskId" element={<TaskDetail />} />
                              
                              {/* 数据可视化 */}
                              <Route path="/data" element={<DataVisualization />} />
                              
                              {/* 用户中心 */}
                              <Route path="/profile" element={<UserProfile />} />
                              
                              {/* 系统设置 */}
                              <Route path="/settings" element={<Settings />} />
                              
                              {/* 404重定向 */}
                              <Route path="*" element={<Navigate to="/dashboard" replace />} />
                            </Routes>
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />
                </Routes>
              </div>
            </Router>
          </AntApp>
        </ConfigProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
