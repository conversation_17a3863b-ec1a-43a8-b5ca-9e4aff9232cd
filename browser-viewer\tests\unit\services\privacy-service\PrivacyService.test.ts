/**
 * PrivacyService 单元测试
 * 测试隐私服务的设置管理和审计追踪功能
 */

import { STORAGE_KEYS } from '@/shared/constants';
import type { DataAccessEvent, PrivacySettings } from '@/shared/types';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock utils
vi.mock('@/shared/utils', () => ({
  createLogger: () => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
}));

describe('PrivacyService', () => {
  let PrivacyService: typeof import('@/services/privacy-service/PrivacyService').PrivacyService;
  let privacyService: import('@/services/privacy-service/PrivacyService').PrivacyService;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // 重置单例
    const module = await import('@/services/privacy-service/PrivacyService');
    PrivacyService = module.PrivacyService;
    
    // 重置单例实例
    (PrivacyService as unknown as { instance: unknown }).instance = undefined;
    privacyService = PrivacyService.getInstance();
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = PrivacyService.getInstance();
      const instance2 = PrivacyService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('隐私设置管理', () => {
    it('应该返回默认设置当存储为空时', async () => {
      chrome.storage.local.get = vi.fn().mockResolvedValue({});
      
      const settings = await privacyService.getPrivacySettings();
      
      expect(settings).toEqual({
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      });
    });

    it('应该合并存储的设置和默认设置', async () => {
      const storedSettings: Partial<PrivacySettings> = {
        cookieDeniedDomains: ['example.com'],
        enableAuditLog: false
      };
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_SETTINGS]: storedSettings
      });
      
      const settings = await privacyService.getPrivacySettings();
      
      expect(settings).toEqual({
        cookieDeniedDomains: ['example.com'],
        enableAuditLog: false,
        proxyUrlBlacklist: []
      });
    });

    it('应该缓存设置避免重复读取', async () => {
      const storedSettings: PrivacySettings = {
        cookieDeniedDomains: ['test.com'],
        enableAuditLog: true,
        proxyUrlBlacklist: ['blocked.com']
      };
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_SETTINGS]: storedSettings
      });
      
      // 第一次调用
      const settings1 = await privacyService.getPrivacySettings();
      // 第二次调用
      const settings2 = await privacyService.getPrivacySettings();
      
      expect(chrome.storage.local.get).toHaveBeenCalledTimes(1);
      expect(settings1).toEqual(settings2);
    });

    it('应该处理存储读取失败', async () => {
      chrome.storage.local.get = vi.fn().mockRejectedValue(new Error('Storage error'));
      
      const settings = await privacyService.getPrivacySettings();
      
      // 应该返回默认设置
      expect(settings).toEqual({
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      });
    });

    it('应该更新隐私设置', async () => {
      const newSettings: PrivacySettings = {
        cookieDeniedDomains: ['new.com'],
        enableAuditLog: false,
        proxyUrlBlacklist: ['blocked.net']
      };
      
      chrome.storage.local.set = vi.fn().mockResolvedValue(undefined);
      
      await privacyService.updatePrivacySettings(newSettings);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        [STORAGE_KEYS.PRIVACY_SETTINGS]: newSettings
      });
    });

    it('应该处理设置更新失败', async () => {
      const newSettings: PrivacySettings = {
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      };
      
      chrome.storage.local.set = vi.fn().mockRejectedValue(new Error('Storage error'));
      
      await expect(privacyService.updatePrivacySettings(newSettings))
        .rejects.toThrow('Storage error');
    });
  });

  describe('数据访问审计', () => {
    it('应该记录数据访问事件', async () => {
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_LOG]: []
      });
      chrome.storage.local.set = vi.fn().mockResolvedValue(undefined);
      
      const event: DataAccessEvent = {
        type: 'read',
        category: 'cookie',
        accessedBy: 'test-service',
        userApproved: true,
        context: { domain: 'example.com', purpose: 'authentication' }
      };
      
      await privacyService.logDataAccess(event);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        [STORAGE_KEYS.PRIVACY_LOG]: [expect.objectContaining(event)]
      });
    });

    it('应该限制审计日志大小', async () => {
      // 创建满的日志数组（500项）
      const existingLog = Array.from({ length: 500 }, (_, i) => ({
        type: 'read' as const,
        category: 'cookie' as const,
        accessedBy: 'old-service',
        userApproved: true,
        context: { domain: 'old.com', index: i }
      }));
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_LOG]: existingLog
      });
      chrome.storage.local.set = vi.fn().mockResolvedValue(undefined);
      
      const newEvent: DataAccessEvent = {
        type: 'write',
        category: 'storage',
        accessedBy: 'new-service',
        userApproved: true,
        context: { domain: 'new.com' }
      };
      
      await privacyService.logDataAccess(newEvent);
      
      const setCall = (chrome.storage.local.set as vi.MockedFunction<typeof chrome.storage.local.set>).mock.calls[0][0];
      const savedLog = setCall[STORAGE_KEYS.PRIVACY_LOG];
      
      // 应该限制在500项
      expect(savedLog).toHaveLength(500);
      // 新事件应该在开头（包含timestamp）
      expect(savedLog[0]).toEqual(expect.objectContaining(newEvent));
      // 最老的事件应该被移除
      expect(savedLog[499]).not.toEqual(existingLog[499]);
    });

    it('应该获取审计日志', async () => {
      const mockLog: DataAccessEvent[] = [
        {
          type: 'read',
          category: 'cookie',
          accessedBy: 'test-service',
          userApproved: true,
          context: { domain: 'test.com' }
        }
      ];
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_LOG]: mockLog
      });
      
      const log = await privacyService.getPrivacyLog();
      
      expect(log).toEqual(mockLog);
    });

    it('应该处理空的审计日志', async () => {
      chrome.storage.local.get = vi.fn().mockResolvedValue({});
      
      const log = await privacyService.getPrivacyLog();
      
      expect(log).toEqual([]);
    });

    it('应该清除审计日志', async () => {
      chrome.storage.local.remove = vi.fn().mockResolvedValue(undefined);
      
      await privacyService.clearPrivacyLog();
      
      expect(chrome.storage.local.remove).toHaveBeenCalledWith(STORAGE_KEYS.PRIVACY_LOG);
    });
  });

  describe('隐私检查', () => {
    it('应该检查域名是否被拒绝cookie', async () => {
      const settings: PrivacySettings = {
        cookieDeniedDomains: ['blocked.com', 'denied.net'],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      };
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_SETTINGS]: settings
      });
      
      const isBlocked1 = await privacyService.isDomainCookieDenied('blocked.com');
      const isBlocked2 = await privacyService.isDomainCookieDenied('allowed.com');
      
      expect(isBlocked1).toBe(true);
      expect(isBlocked2).toBe(false);
    });

    it('应该检查URL是否在代理黑名单中', async () => {
      const settings: PrivacySettings = {
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: ['https://blocked.com', 'http://denied.net']
      };
      
      chrome.storage.local.get = vi.fn().mockResolvedValue({
        [STORAGE_KEYS.PRIVACY_SETTINGS]: settings
      });
      
      const isBlocked1 = await privacyService.isUrlBlacklisted('https://blocked.com/path');
      const isBlocked2 = await privacyService.isUrlBlacklisted('https://allowed.com');
      
      expect(isBlocked1).toBe(true);
      expect(isBlocked2).toBe(false);
    });
  });

  describe('错误处理', () => {
    it('应该处理所有存储操作的错误', async () => {
      chrome.storage.local.get = vi.fn().mockRejectedValue(new Error('Storage unavailable'));
      chrome.storage.local.set = vi.fn().mockRejectedValue(new Error('Storage unavailable'));
      
      // 这些操作应该处理错误而不抛出异常
      const settings = await privacyService.getPrivacySettings();
      const log = await privacyService.getPrivacyLog();
      
      expect(settings).toBeDefined();
      expect(log).toEqual([]);
    });
  });
});