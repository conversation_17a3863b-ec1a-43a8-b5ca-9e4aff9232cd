/**
 * BehaviorAnalyzer 单元测试
 * 测试行为分析器的核心功能
 */

import { BehaviorAnalyzer } from '@/core/behavior-engine/BehaviorAnalyzer';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { TestUtils } from '../../../utils/test-helpers';

interface ActionRecord {
  type: string;
  timestamp: number;
  position?: { x: number; y: number };
  duration?: number;
}

describe('BehaviorAnalyzer', () => {
  let behaviorAnalyzer: BehaviorAnalyzer;

  beforeEach(() => {
    behaviorAnalyzer = new BehaviorAnalyzer();
  });

  afterEach(() => {
    TestUtils.cleanupTestEnvironment();
  });

  describe('启动和停止', () => {
    it('应该成功启动分析器', () => {
      expect(behaviorAnalyzer.isRunning()).toBe(false);
      
      behaviorAnalyzer.start();
      
      expect(behaviorAnalyzer.isRunning()).toBe(true);
    });

    it('应该成功停止分析器', () => {
      behaviorAnalyzer.start();
      expect(behaviorAnalyzer.isRunning()).toBe(true);
      
      behaviorAnalyzer.stop();
      
      expect(behaviorAnalyzer.isRunning()).toBe(false);
    });

    it('应该允许重复启动', () => {
      behaviorAnalyzer.start();
      behaviorAnalyzer.start();
      
      expect(behaviorAnalyzer.isRunning()).toBe(true);
    });

    it('应该允许重复停止', () => {
      behaviorAnalyzer.start();
      behaviorAnalyzer.stop();
      behaviorAnalyzer.stop();
      
      expect(behaviorAnalyzer.isRunning()).toBe(false);
    });
  });

  describe('行为分析', () => {
    beforeEach(() => {
      behaviorAnalyzer.start();
    });

    it('应该在数据不足时返回非异常结果', () => {
      const shortHistory: ActionRecord[] = [
        { type: 'click', timestamp: 1000, position: { x: 100, y: 200 } },
        { type: 'scroll', timestamp: 1500 },
        { type: 'click', timestamp: 2000, position: { x: 300, y: 400 } },
      ];

      const result = behaviorAnalyzer.analyze(shortHistory);

      expect(result.isAnomalous).toBe(false);
      expect(result.reason).toBeUndefined();
    });

    it('应该分析正常的行为模式', () => {
      const normalHistory: ActionRecord[] = [
        { type: 'scroll', timestamp: 1000 },
        { type: 'pause', timestamp: 1300 },
        { type: 'click', timestamp: 1800, position: { x: 100, y: 200 } },
        { type: 'scroll', timestamp: 2200 },
        { type: 'pause', timestamp: 2600 },
        { type: 'click', timestamp: 3100, position: { x: 300, y: 400 } },
        { type: 'scroll', timestamp: 3500 },
        { type: 'pause', timestamp: 3900 },
        { type: 'click', timestamp: 4400, position: { x: 500, y: 600 } },
        { type: 'scroll', timestamp: 4800 },
        { type: 'pause', timestamp: 5200 },
        { type: 'click', timestamp: 5700, position: { x: 700, y: 800 } },
      ];

      const result = behaviorAnalyzer.analyze(normalHistory);

      expect(result).toBeDefined();
      expect(typeof result.isAnomalous).toBe('boolean');
    });

    it('应该检测过于一致的时间间隔', () => {
      const roboticHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'click',
        timestamp: 1000 + i * 1000, // 完全一致的1秒间隔
        position: { x: 100 + i * 50, y: 200 + i * 50 },
      }));

      const result = behaviorAnalyzer.analyze(roboticHistory);

      expect(result.isAnomalous).toBe(true);
      expect(result.reason).toContain('consistent');
      expect(result.recommendation).toBe('ADJUST_TIMING');
    });

    it('应该检测重复的动作类型模式', () => {
      const repetitiveHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'scroll', // 只有一种动作类型
        timestamp: 1000 + i * (500 + Math.random() * 200), // 随机间隔避免时间检测
        position: { x: 100 + Math.random() * 400, y: 200 + Math.random() * 400 },
      }));

      const result = behaviorAnalyzer.analyze(repetitiveHistory);

      expect(result.isAnomalous).toBe(true);
      expect(result.reason).toContain('sequence');
      expect(result.recommendation).toBe('VARY_ACTION_TYPES');
    });

    it('应该检测空间聚集模式', () => {
      // 创建足够多种类的动作，确保有足够的click用于空间分析
      const clusteredHistory: ActionRecord[] = [];
      // 添加6个click动作用于空间分析
      for (let i = 0; i < 6; i++) {
        clusteredHistory.push({
          type: 'click',
          timestamp: 1000 + i * (500 + Math.random() * 300),
          position: { x: 150 + Math.random() * 10, y: 250 + Math.random() * 10 }, // 都在同一个100x100网格内
        });
      }
      // 添加其他类型的动作以提高动作类型熵
      const otherActions = ['scroll', 'hover', 'input', 'keypress', 'mousemove', 'focus'];
      for (let i = 0; i < 6; i++) {
        clusteredHistory.push({
          type: otherActions[i % otherActions.length],
          timestamp: 1000 + (i + 6) * (500 + Math.random() * 300),
          position: { x: 150 + Math.random() * 10, y: 250 + Math.random() * 10 },
        });
      }

      const result = behaviorAnalyzer.analyze(clusteredHistory);

      expect(result.isAnomalous).toBe(true);
      expect(result.reason).toContain('clustered');
      expect(result.recommendation).toBe('VARY_PATHS');
    });

    it('应该在分析器停止时返回非异常结果', () => {
      const history: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'click',
        timestamp: 1000 + i * 1000,
        position: { x: 100, y: 200 },
      }));

      behaviorAnalyzer.stop();
      const result = behaviorAnalyzer.analyze(history);

      expect(result.isAnomalous).toBe(false);
    });
  });

  describe('异常检测算法', () => {
    beforeEach(() => {
      behaviorAnalyzer.start();
    });

    it('应该计算时间间隔的方差', () => {
      // 创建方差很小的历史（机器人行为）
      const lowVarianceHistory: ActionRecord[] = Array.from({ length: 10 }, (_, i) => ({
        type: 'click',
        timestamp: 1000 + i * 1000, // 完全一致的间隔
        position: { x: 100 + i * 10, y: 200 + i * 10 },
      }));

      const result = behaviorAnalyzer.analyze(lowVarianceHistory);
      expect(result.isAnomalous).toBe(true);
    });

    it('应该计算动作类型的熵', () => {
      // 创建熵很低的历史（重复性高）
      const lowEntropyHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'scroll', // 只有一种类型
        timestamp: 1000 + i * (800 + Math.random() * 400),
        position: { x: 100 + Math.random() * 400, y: 200 + Math.random() * 400 },
      }));

      const result = behaviorAnalyzer.analyze(lowEntropyHistory);
      expect(result.isAnomalous).toBe(true);
    });

    it('应该分析空间分布', () => {
      // 创建空间分布熵很低的历史 - 只有点击才有位置信息
      const clusteredHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'click', // 只测试点击
        timestamp: 1000 + i * (600 + Math.random() * 400),
        position: { x: 150 + Math.random() * 10, y: 250 + Math.random() * 10 }, // 聚集在同一个100x100网格内
      }));

      const result = behaviorAnalyzer.analyze(clusteredHistory);
      expect(result.isAnomalous).toBe(true);
    });

    it('应该处理没有位置信息的动作', () => {
      const mixedHistory: ActionRecord[] = [
        { type: 'scroll', timestamp: 1000 },
        { type: 'pause', timestamp: 1500 },
        { type: 'click', timestamp: 2000, position: { x: 100, y: 200 } },
        { type: 'scroll', timestamp: 2500 },
        { type: 'pause', timestamp: 3000 },
        { type: 'click', timestamp: 3500, position: { x: 300, y: 400 } },
        { type: 'scroll', timestamp: 4000 },
        { type: 'pause', timestamp: 4500 },
        { type: 'click', timestamp: 5000, position: { x: 500, y: 600 } },
        { type: 'scroll', timestamp: 5500 },
        { type: 'pause', timestamp: 6000 },
        { type: 'click', timestamp: 6500, position: { x: 700, y: 800 } },
      ];

      const result = behaviorAnalyzer.analyze(mixedHistory);
      expect(result).toBeDefined();
      expect(typeof result.isAnomalous).toBe('boolean');
    });
  });

  describe('建议生成', () => {
    beforeEach(() => {
      behaviorAnalyzer.start();
    });

    it('应该为时间异常提供正确建议', () => {
      const timeBasedHistory: ActionRecord[] = Array.from({ length: 10 }, (_, i) => ({
        type: i % 2 === 0 ? 'click' : 'scroll',
        timestamp: 1000 + i * 1000, // 完全一致的间隔
        position: { x: 100 + Math.random() * 400, y: 200 + Math.random() * 400 },
      }));

      const result = behaviorAnalyzer.analyze(timeBasedHistory);
      expect(result.recommendation).toBe('ADJUST_TIMING');
    });

    it('应该为动作类型异常提供正确建议', () => {
      const actionBasedHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: 'click', // 单一动作类型
        timestamp: 1000 + i * (600 + Math.random() * 400),
        position: { x: 100 + Math.random() * 400, y: 200 + Math.random() * 400 },
      }));

      const result = behaviorAnalyzer.analyze(actionBasedHistory);
      expect(result.recommendation).toBe('VARY_ACTION_TYPES');
    });

    it('应该为空间异常提供正确建议', () => {
      // 创建足够多种类的动作，确保有足够的click用于空间分析
      const spatialBasedHistory: ActionRecord[] = [];
      // 添加6个click动作用于空间分析
      for (let i = 0; i < 6; i++) {
        spatialBasedHistory.push({
          type: 'click',
          timestamp: 1000 + i * (500 + Math.random() * 300),
          position: { x: 150 + Math.random() * 10, y: 250 + Math.random() * 10 }, // 都在同一个100x100网格内
        });
      }
      // 添加其他类型的动作以提高动作类型熵
      const otherActions = ['scroll', 'hover', 'input', 'keypress', 'mousemove', 'focus'];
      for (let i = 0; i < 6; i++) {
        spatialBasedHistory.push({
          type: otherActions[i % otherActions.length],
          timestamp: 1000 + (i + 6) * (500 + Math.random() * 300),
          position: { x: 150 + Math.random() * 10, y: 250 + Math.random() * 10 },
        });
      }

      const result = behaviorAnalyzer.analyze(spatialBasedHistory);
      expect(result.recommendation).toBe('VARY_PATHS');
    });
  });

  describe('性能测试', () => {
    beforeEach(() => {
      behaviorAnalyzer.start();
    });

    it('应该快速分析大量数据', () => {
      const largeHistory: ActionRecord[] = Array.from({ length: 1000 }, (_, i) => ({
        type: i % 4 === 0 ? 'click' : i % 4 === 1 ? 'scroll' : i % 4 === 2 ? 'pause' : 'move',
        timestamp: 1000 + i * (100 + Math.random() * 50),
        position: { x: Math.random() * 1000, y: Math.random() * 800 },
      }));

      const measure = new TestUtils.PerformanceMeasure();
      
      const result = behaviorAnalyzer.analyze(largeHistory);
      
      measure.mark('analysis_complete');
      expect(measure.getMeasurement('analysis_complete')).toBeLessThan(100); // 应在100ms内完成
      expect(result).toBeDefined();
    });

    it('应该处理空的历史记录', () => {
      const emptyHistory: ActionRecord[] = [];

      const result = behaviorAnalyzer.analyze(emptyHistory);

      expect(result.isAnomalous).toBe(false);
    });

    it('应该处理只有一个记录的历史', () => {
      const singleHistory: ActionRecord[] = [
        { type: 'click', timestamp: 1000, position: { x: 100, y: 200 } },
      ];

      const result = behaviorAnalyzer.analyze(singleHistory);

      expect(result.isAnomalous).toBe(false);
    });
  });

  describe('边界情况', () => {
    beforeEach(() => {
      behaviorAnalyzer.start();
    });

    it('应该处理相同时间戳的动作', () => {
      const sameTimestampHistory: ActionRecord[] = Array.from({ length: 10 }, (_, i) => ({
        type: i % 2 === 0 ? 'click' : 'scroll',
        timestamp: 1000, // 所有动作都有相同时间戳
        position: { x: 100 + i * 50, y: 200 + i * 50 },
      }));

      const result = behaviorAnalyzer.analyze(sameTimestampHistory);
      expect(result).toBeDefined();
    });

    it('应该处理负时间间隔', () => {
      const negativeIntervalHistory: ActionRecord[] = [
        { type: 'click', timestamp: 2000, position: { x: 100, y: 200 } },
        { type: 'scroll', timestamp: 1500 }, // 时间倒退
        { type: 'click', timestamp: 1000, position: { x: 300, y: 400 } },
        { type: 'scroll', timestamp: 1200 },
        { type: 'click', timestamp: 1100, position: { x: 500, y: 600 } },
        { type: 'scroll', timestamp: 1300 },
        { type: 'click', timestamp: 1250, position: { x: 700, y: 800 } },
        { type: 'scroll', timestamp: 1400 },
        { type: 'click', timestamp: 1350, position: { x: 900, y: 1000 } },
        { type: 'scroll', timestamp: 1450 },
        { type: 'click', timestamp: 1500, position: { x: 200, y: 300 } },
        { type: 'scroll', timestamp: 1550 },
      ];

      const result = behaviorAnalyzer.analyze(negativeIntervalHistory);
      expect(result).toBeDefined();
    });

    it('应该处理极大的坐标值', () => {
      const extremeCoordHistory: ActionRecord[] = Array.from({ length: 12 }, (_, i) => ({
        type: i % 2 === 0 ? 'click' : 'scroll',
        timestamp: 1000 + i * (500 + Math.random() * 300),
        position: { x: 999999, y: 999999 }, // 极大坐标值
      }));

      const result = behaviorAnalyzer.analyze(extremeCoordHistory);
      expect(result).toBeDefined();
    });
  });
});