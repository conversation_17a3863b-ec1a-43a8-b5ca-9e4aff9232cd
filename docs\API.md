# 数懒平台 API 文档

## 概述

数懒平台提供完整的RESTful API，支持用户管理、任务调度、数据处理等核心功能。

## 基础信息

- **Base URL**: `http://localhost:8000/api`
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

## 认证

### 登录

```http
POST /v1/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "full_name": "string",
    "is_admin": false
  }
}
```

### 刷新Token

```http
POST /v1/auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

{
  "refresh_token": "string"
}
```

### 获取当前用户信息

```http
GET /v1/auth/me
Authorization: Bearer <access_token>
```

## 任务管理

### 创建任务

```http
POST /v1/tasks
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "name": "string",
  "description": "string",
  "config": {
    "urls": ["string"],
    "max_pages": 10,
    "delay": 1.0,
    "timeout": 30,
    "screenshot": true,
    "extract_text": true,
    "extract_links": false,
    "extract_images": false
  },
  "priority": "NORMAL"
}
```

### 获取任务列表

```http
GET /v1/tasks?page=1&size=20&status=RUNNING&priority=HIGH
Authorization: Bearer <access_token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `status`: 任务状态 (PENDING|RUNNING|COMPLETED|FAILED|CANCELLED)
- `priority`: 优先级 (LOW|NORMAL|HIGH|URGENT)
- `batch_id`: 批次ID

### 获取任务详情

```http
GET /v1/tasks/{task_id}
Authorization: Bearer <access_token>
```

### 启动任务

```http
POST /v1/tasks/{task_id}/start
Authorization: Bearer <access_token>
```

### 暂停任务

```http
POST /v1/tasks/{task_id}/pause
Authorization: Bearer <access_token>
```

### 取消任务

```http
POST /v1/tasks/{task_id}/cancel
Authorization: Bearer <access_token>
```

### 删除任务

```http
DELETE /v1/tasks/{task_id}
Authorization: Bearer <access_token>
```

## 数据管理

### 上传数据

```http
POST /v1/data/upload
Content-Type: multipart/form-data
Authorization: Bearer <access_token>

file: <binary>
task_id: string (optional)
```

### 获取数据列表

```http
GET /v1/data?page=1&size=20&data_type=TEXT&status=PROCESSED
Authorization: Bearer <access_token>
```

### 查询数据

```http
POST /v1/data/query
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "data_type": "TEXT",
  "status": "PROCESSED",
  "date_from": "2024-01-01T00:00:00Z",
  "date_to": "2024-12-31T23:59:59Z",
  "search_text": "关键词",
  "page": 1,
  "size": 20
}
```

## WebSocket 实时通信

### 连接

```
ws://localhost:8000/api/v1/ws/connect?token=<access_token>
```

### 消息格式

**任务更新消息**:
```json
{
  "type": "task_update",
  "data": {
    "task_id": "string",
    "status": "RUNNING",
    "progress": 50.0
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**通知消息**:
```json
{
  "type": "notification",
  "data": {
    "title": "string",
    "content": "string",
    "level": "info"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 客户端消息

**心跳检测**:
```json
{
  "type": "ping",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**订阅任务**:
```json
{
  "type": "subscribe_task",
  "task_id": "string",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 错误处理

### 错误响应格式

```json
{
  "detail": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或token过期
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `429 Too Many Requests`: 请求频率限制
- `500 Internal Server Error`: 服务器内部错误

## 限流规则

- **标准接口**: 100 requests/minute
- **上传接口**: 10 requests/minute
- **认证接口**: 5 requests/minute

## 示例代码

### JavaScript/TypeScript

```typescript
// 登录
const login = async (username: string, password: string) => {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  })
  
  if (!response.ok) {
    throw new Error('Login failed')
  }
  
  return await response.json()
}

// 创建任务
const createTask = async (taskData: any, token: string) => {
  const response = await fetch('/api/v1/tasks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(taskData),
  })
  
  return await response.json()
}
```

### Python

```python
import requests

# 登录
def login(username: str, password: str):
    response = requests.post(
        'http://localhost:8000/api/v1/auth/login',
        json={'username': username, 'password': password}
    )
    response.raise_for_status()
    return response.json()

# 创建任务
def create_task(task_data: dict, token: str):
    response = requests.post(
        'http://localhost:8000/api/v1/tasks',
        json=task_data,
        headers={'Authorization': f'Bearer {token}'}
    )
    response.raise_for_status()
    return response.json()
```

## 更多信息

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json
