import { STORAGE_KEYS } from '../../shared/constants';
import { DataAccessEvent, PrivacySettings } from '../../shared/types';
import { createLogger, Logger } from '../../shared/utils';

const PRIVACY_LOG_KEY = STORAGE_KEYS.PRIVACY_LOG;
const PRIVACY_SETTINGS_KEY = STORAGE_KEYS.PRIVACY_SETTINGS;

/**
 * 负责隐私审计追踪，记录对敏感数据的访问，并管理隐私设置。
 * 这是一个单例服务。
 */
export class PrivacyService {
  private static instance: PrivacyService;
  private readonly _logger: Logger;
  private _cachedSettings: PrivacySettings | null = null;

  constructor() {
    this._logger = createLogger('PrivacyService');
  }

  public static getInstance(): PrivacyService {
    if (!PrivacyService.instance) {
      PrivacyService.instance = new PrivacyService();
    }
    return PrivacyService.instance;
  }

  /**
   * 获取隐私设置
   */
  public async getPrivacySettings(): Promise<PrivacySettings> {
    if (this._cachedSettings) {
      return this._cachedSettings;
    }

    try {
      const result = await chrome.storage.local.get([PRIVACY_SETTINGS_KEY]);
      const settings = result[PRIVACY_SETTINGS_KEY] as PrivacySettings;
      
      // 提供默认设置
      const defaultSettings: PrivacySettings = {
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      };

      this._cachedSettings = { ...defaultSettings, ...settings };
      return this._cachedSettings;
    } catch (error) {
      this._logger.error('Failed to load privacy settings:', error);
      // 返回默认安全设置
      return {
        cookieDeniedDomains: [],
        enableAuditLog: true,
        proxyUrlBlacklist: []
      };
    }
  }

  /**
   * 更新隐私设置
   */
  public async updatePrivacySettings(settings: Partial<PrivacySettings>): Promise<void> {
    try {
      const currentSettings = await this.getPrivacySettings();
      const newSettings = { ...currentSettings, ...settings };
      
      await chrome.storage.local.set({ [PRIVACY_SETTINGS_KEY]: newSettings });
      this._cachedSettings = newSettings;
      
      this._logger.info('Privacy settings updated successfully');
    } catch (error) {
      this._logger.error('Failed to update privacy settings:', error);
      throw error;
    }
  }

  /**
   * 检查URL是否在代理黑名单中
   */
  public async isUrlBlacklisted(url: string): Promise<boolean> {
    try {
      const settings = await this.getPrivacySettings();
      const urlObj = new URL(url);
      
      return settings.proxyUrlBlacklist.some(pattern => {
        // 支持通配符匹配
        if (pattern.includes('*')) {
          const regex = new RegExp(pattern.replace(/\*/g, '.*'));
          return regex.test(url);
        }
        // 精确匹配或域名匹配
        return url === pattern || urlObj.hostname === pattern || url.includes(pattern);
      });
    } catch (error) {
      this._logger.warn('Error checking URL blacklist:', error);
      return false; // 如果检查失败，允许访问以避免阻塞
    }
  }

  /**
   * 检查域名是否在Cookie黑名单中
   */
  public async isDomainCookieDenied(domain: string): Promise<boolean> {
    try {
      const settings = await this.getPrivacySettings();
      
      return settings.cookieDeniedDomains.some(deniedDomain => {
        // 支持通配符匹配
        if (deniedDomain.includes('*')) {
          const regex = new RegExp(deniedDomain.replace(/\*/g, '.*'));
          return regex.test(domain);
        }
        // 精确匹配或子域匹配
        return domain === deniedDomain || domain.endsWith('.' + deniedDomain);
      });
    } catch (error) {
      this._logger.warn('Error checking domain cookie denial:', error);
      return false; // 如果检查失败，允许Cookie以避免阻塞
    }
  }

  /**
   * 记录一次数据访问事件。
   * @param event - 数据访问事件的详细信息。
   */
  public async logDataAccess(event: DataAccessEvent): Promise<void> {
    const settings = await this.getPrivacySettings();
    if (!settings.enableAuditLog) {
      return; // 如果审计日志被禁用，则跳过记录
    }

    const logEntry = {
      ...event,
      timestamp: Date.now(),
    };

    try {
      const result = await chrome.storage.local.get([PRIVACY_LOG_KEY]);
      const currentLog = (result[PRIVACY_LOG_KEY] as DataAccessEvent[]) || [];
      const newLog: DataAccessEvent[] = [logEntry, ...currentLog];
      
      // 为了防止日志无限增长，设置一个上限，只保留最新的500条记录
      if (newLog.length > 500) {
        newLog.splice(500);
      }
      
      await chrome.storage.local.set({ [PRIVACY_LOG_KEY]: newLog });
    } catch (error) {
      this._logger.error('Failed to log privacy event:', error);
    }
  }

  /**
   * 获取隐私审计日志。
   * @returns 存储的所有隐私审计日志条目。
   */
  public async getPrivacyLog(): Promise<DataAccessEvent[]> {
    try {
      const result = await chrome.storage.local.get([PRIVACY_LOG_KEY]);
      return (result[PRIVACY_LOG_KEY] as DataAccessEvent[]) || [];
    } catch (error) {
      this._logger.error('Failed to retrieve privacy log:', error);
      return [];
    }
  }

  /**
   * 清除所有隐私审计日志。
   */
  public async clearPrivacyLog(): Promise<void> {
    try {
      await chrome.storage.local.remove(PRIVACY_LOG_KEY);
      this._logger.info('Privacy log cleared successfully');
    } catch (error) {
      this._logger.error('Failed to clear privacy log:', error);
    }
  }

  /**
   * 清除缓存的设置，强制下次获取时重新从存储加载
   */
  public clearSettingsCache(): void {
    this._cachedSettings = null;
  }
} 