/**
 * API服务层
 * 统一的HTTP请求处理
 */

import { message } from 'antd'
import { useAuthStore } from '../stores/authStore'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 请求拦截器
class ApiClient {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    // 获取认证token
    const { token } = useAuthStore.getState()
    
    // 默认请求头
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    // 添加认证头
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      // 处理401未授权
      if (response.status === 401) {
        const { logout } = useAuthStore.getState()
        logout()
        message.error('登录已过期，请重新登录')
        throw new Error('Unauthorized')
      }

      // 处理其他HTTP错误
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}`
        throw new Error(errorMessage)
      }

      // 返回JSON数据
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(endpoint, this.baseURL)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }

    return this.request<T>(url.pathname + url.search)
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    })
  }

  // 文件上传
  async upload<T>(endpoint: string, file: File, data?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const { token } = useAuthStore.getState()
    const headers: HeadersInit = {}
    
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: formData,
    })
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient(API_BASE_URL)

// 认证相关API
export const authApi = {
  login: (credentials: { username: string; password: string }) =>
    apiClient.post('/v1/auth/login', credentials),
  
  logout: () =>
    apiClient.post('/v1/auth/logout'),
  
  refreshToken: (refreshToken: string) =>
    apiClient.post('/v1/auth/refresh', { refresh_token: refreshToken }),
  
  getCurrentUser: () =>
    apiClient.get('/v1/auth/me'),
}

// 任务相关API
export const taskApi = {
  getTasks: (params?: any) =>
    apiClient.get('/v1/tasks', params),
  
  getTask: (taskId: string) =>
    apiClient.get(`/v1/tasks/${taskId}`),
  
  createTask: (taskData: any) =>
    apiClient.post('/v1/tasks', taskData),
  
  updateTask: (taskId: string, updates: any) =>
    apiClient.put(`/v1/tasks/${taskId}`, updates),
  
  deleteTask: (taskId: string) =>
    apiClient.delete(`/v1/tasks/${taskId}`),
  
  startTask: (taskId: string) =>
    apiClient.post(`/v1/tasks/${taskId}/start`),
  
  pauseTask: (taskId: string) =>
    apiClient.post(`/v1/tasks/${taskId}/pause`),
  
  cancelTask: (taskId: string) =>
    apiClient.post(`/v1/tasks/${taskId}/cancel`),
  
  getTaskMetrics: (taskId: string) =>
    apiClient.get(`/v1/tasks/${taskId}/metrics`),
}

// 数据相关API
export const dataApi = {
  getData: (params?: any) =>
    apiClient.get('/v1/data', params),
  
  getDataDetail: (dataId: string) =>
    apiClient.get(`/v1/data/${dataId}`),
  
  uploadData: (file: File, taskId?: string) =>
    apiClient.upload('/v1/data/upload', file, taskId ? { task_id: taskId } : {}),
  
  deleteData: (dataId: string) =>
    apiClient.delete(`/v1/data/${dataId}`),
  
  queryData: (query: any) =>
    apiClient.post('/v1/data/query', query),
}

// 用户相关API
export const userApi = {
  getUsers: (params?: any) =>
    apiClient.get('/v1/users', params),
  
  getUser: (userId: string) =>
    apiClient.get(`/v1/users/${userId}`),
  
  updateUser: (userId: string, updates: any) =>
    apiClient.put(`/v1/users/${userId}`, updates),
  
  deleteUser: (userId: string) =>
    apiClient.delete(`/v1/users/${userId}`),
}

export default apiClient
