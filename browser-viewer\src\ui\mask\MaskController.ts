/**
 * @file MaskController.ts
 * @description 该控制器负责在可视化模式下，管理覆盖在目标页面上的UI蒙版。
 * 包括创建、显示/隐藏、更新高亮区域等。
 */

import { CSS_CLASSES } from "@/shared/constants";
import type { Point, Rectangle } from "@/shared/types";

export class MaskController {
  public element: HTMLElement;
  private readonly _highlightElement: HTMLElement;
  private readonly _mouseElement: HTMLElement;

  constructor() {
    this.element = this._createMaskElement();
    this._highlightElement = this._createHighlightElement();
    this._mouseElement = this._createMouseElement();

    this.element.appendChild(this._highlightElement);
    this.element.appendChild(this._mouseElement);

    // 阻止事件冒泡，防止蒙版事件影响页面
    this._setupEventHandlers();

    document.body.appendChild(this.element);
  }

  /**
   * 显示蒙版。
   */
  public show(): void {
    this.element.style.display = 'block';
  }

  /**
   * 隐藏蒙版。
   */
  public hide(): void {
    this.element.style.display = 'none';
  }

  /**
   * 更新高亮区域的位置和大小。
   * @param rect - 一个包含 x, y, width, height 的矩形对象。
   */
  public updateHighlight(rect: Rectangle): void {
    this._highlightElement.style.left = `${rect.x}px`;
    this._highlightElement.style.top = `${rect.y}px`;
    this._highlightElement.style.width = `${rect.width}px`;
    this._highlightElement.style.height = `${rect.height}px`;
  }

  /**
   * 更新鼠标指针的位置。
   * @param point - 一个包含 x, y 坐标的对象。
   */
  public updateMousePosition(point: Point): void {
    this._mouseElement.style.left = `${point.x}px`;
    this._mouseElement.style.top = `${point.y}px`;
  }

  /**
   * 销毁蒙版并从DOM中移除。
   */
  public destroy(): void {
    this.element.remove();
  }

  private _createMaskElement(): HTMLElement {
    const mask = document.createElement('div');
    mask.id = 'agent-mask';
    mask.className = CSS_CLASSES.AGENT_MASK;
    return mask;
  }

  private _createHighlightElement(): HTMLElement {
    const highlight = document.createElement('div');
    highlight.className = CSS_CLASSES.VIEWPORT_HIGHLIGHT;
    return highlight;
  }
  
  private _createMouseElement(): HTMLElement {
    const mouse = document.createElement('div');
    mouse.className = 'agent-mouse-pointer'; // Add a specific class for styling
    return mouse;
  }

  /**
   * 设置事件处理器，阻止事件冒泡。
   */
  private _setupEventHandlers(): void {
    // 阻止所有鼠标事件冒泡
    const events = ['click', 'mousedown', 'mouseup', 'mousemove', 'contextmenu'];
    events.forEach(eventName => {
      this.element.addEventListener(eventName, (event) => {
        event.stopPropagation();
      });
    });
  }
} 