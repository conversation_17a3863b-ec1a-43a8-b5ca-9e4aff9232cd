/**
 * 任务详情页面
 * 显示任务详细信息、执行状态、结果数据
 */

import React, { useEffect, useState } from 'react'
import {
  Card,
  Row,
  Col,
  Descriptions,
  Progress,
  Tag,
  Button,
  Space,
  Table,
  Typography,
  Statistic,
  Timeline,
  Tabs,
  message,
  Spin,
} from 'antd'
import {
  ArrowLeftOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'

import { useTaskStore, TaskStatus, TaskPriority } from '../../stores/taskStore'
import { wsService } from '../../services/websocket'

const { Title, Text } = Typography
const { TabPane } = Tabs

const TaskDetail: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>()
  const navigate = useNavigate()
  const { currentTask, fetchTask, startTask, pauseTask, cancelTask, loading } = useTaskStore()
  const [realTimeData, setRealTimeData] = useState<any>(null)

  useEffect(() => {
    if (taskId) {
      fetchTask(taskId)
      
      // 订阅实时更新
      const unsubscribe = wsService.subscribeToTaskUpdates((data) => {
        if (data.task_id === taskId) {
          setRealTimeData(data)
        }
      })

      // 订阅任务
      wsService.subscribeToTask(taskId)

      return unsubscribe
    }
  }, [taskId, fetchTask])

  if (loading || !currentTask) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  // 状态配置
  const statusConfig = {
    [TaskStatus.PENDING]: { color: 'default', text: '等待中' },
    [TaskStatus.RUNNING]: { color: 'processing', text: '运行中' },
    [TaskStatus.COMPLETED]: { color: 'success', text: '已完成' },
    [TaskStatus.FAILED]: { color: 'error', text: '失败' },
    [TaskStatus.CANCELLED]: { color: 'default', text: '已取消' },
    [TaskStatus.PAUSED]: { color: 'warning', text: '已暂停' },
  }

  // 优先级配置
  const priorityConfig = {
    [TaskPriority.LOW]: { color: 'default', text: '低' },
    [TaskPriority.NORMAL]: { color: 'blue', text: '普通' },
    [TaskPriority.HIGH]: { color: 'orange', text: '高' },
    [TaskPriority.URGENT]: { color: 'red', text: '紧急' },
  }

  // 处理任务操作
  const handleStartTask = async () => {
    try {
      await startTask(currentTask.id)
      message.success('任务已启动')
    } catch (error) {
      message.error('启动任务失败')
    }
  }

  const handlePauseTask = async () => {
    try {
      await pauseTask(currentTask.id)
      message.success('任务已暂停')
    } catch (error) {
      message.error('暂停任务失败')
    }
  }

  const handleCancelTask = async () => {
    try {
      await cancelTask(currentTask.id)
      message.success('任务已取消')
    } catch (error) {
      message.error('取消任务失败')
    }
  }

  // URL处理结果表格列
  const urlColumns = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          success: 'success',
          failed: 'error',
          processing: 'processing',
          pending: 'default',
        }
        return <Tag color={colorMap[status] || 'default'}>{status}</Tag>
      },
    },
    {
      title: '处理时间',
      dataIndex: 'processingTime',
      key: 'processingTime',
      render: (time: number) => time ? `${time.toFixed(2)}s` : '-',
    },
    {
      title: '数据量',
      dataIndex: 'dataCount',
      key: 'dataCount',
      render: (count: number) => count || 0,
    },
  ]

  // 模拟URL处理数据
  const urlData = currentTask.config?.urls?.map((url: string, index: number) => ({
    key: index,
    url,
    status: index < currentTask.processedUrls ? 'success' : 'pending',
    processingTime: index < currentTask.processedUrls ? Math.random() * 5 : null,
    dataCount: index < currentTask.processedUrls ? Math.floor(Math.random() * 100) : 0,
  })) || []

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/tasks')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {currentTask.name}
          </Title>
        </Space>
      </div>

      {/* 任务概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总URL数"
              value={currentTask.totalUrls}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已处理"
              value={currentTask.processedUrls}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="成功"
              value={currentTask.succeededUrls}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="失败"
              value={currentTask.failedUrls}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Row gutter={[16, 16]}>
        {/* 左侧：任务信息 */}
        <Col xs={24} lg={8}>
          <Card title="任务信息" style={{ marginBottom: 16 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="状态">
                <Tag color={statusConfig[currentTask.status].color}>
                  {statusConfig[currentTask.status].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="优先级">
                <Tag color={priorityConfig[currentTask.priority].color}>
                  {priorityConfig[currentTask.priority].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="进度">
                <Progress percent={currentTask.progressPercentage} size="small" />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(currentTask.createdAt).toLocaleString()}
              </Descriptions.Item>
              {currentTask.startedAt && (
                <Descriptions.Item label="开始时间">
                  {new Date(currentTask.startedAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {currentTask.completedAt && (
                <Descriptions.Item label="完成时间">
                  {new Date(currentTask.completedAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {currentTask.executionTime && (
                <Descriptions.Item label="执行时长">
                  {currentTask.executionTime.toFixed(2)}秒
                </Descriptions.Item>
              )}
              {currentTask.assignedWorker && (
                <Descriptions.Item label="执行节点">
                  {currentTask.assignedWorker}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {/* 操作按钮 */}
          <Card title="操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              {currentTask.status === TaskStatus.PENDING && (
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStartTask}
                  block
                >
                  启动任务
                </Button>
              )}
              
              {currentTask.status === TaskStatus.RUNNING && (
                <Button
                  icon={<PauseCircleOutlined />}
                  onClick={handlePauseTask}
                  block
                >
                  暂停任务
                </Button>
              )}
              
              {[TaskStatus.RUNNING, TaskStatus.PAUSED].includes(currentTask.status) && (
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={handleCancelTask}
                  block
                >
                  取消任务
                </Button>
              )}
              
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchTask(currentTask.id)}
                block
              >
                刷新状态
              </Button>
              
              {currentTask.status === TaskStatus.COMPLETED && (
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  block
                >
                  导出结果
                </Button>
              )}
            </Space>
          </Card>
        </Col>

        {/* 右侧：详细信息 */}
        <Col xs={24} lg={16}>
          <Card>
            <Tabs defaultActiveKey="urls">
              <TabPane tab="URL处理状态" key="urls">
                <Table
                  columns={urlColumns}
                  dataSource={urlData}
                  pagination={{ pageSize: 10 }}
                  size="small"
                />
              </TabPane>
              
              <TabPane tab="执行日志" key="logs">
                <Timeline>
                  <Timeline.Item color="blue">
                    任务创建 - {new Date(currentTask.createdAt).toLocaleString()}
                  </Timeline.Item>
                  {currentTask.startedAt && (
                    <Timeline.Item color="green">
                      任务开始 - {new Date(currentTask.startedAt).toLocaleString()}
                    </Timeline.Item>
                  )}
                  {realTimeData && (
                    <Timeline.Item color="blue">
                      进度更新 - {realTimeData.progress}%
                    </Timeline.Item>
                  )}
                  {currentTask.completedAt && (
                    <Timeline.Item color="green">
                      任务完成 - {new Date(currentTask.completedAt).toLocaleString()}
                    </Timeline.Item>
                  )}
                  {currentTask.errorMessage && (
                    <Timeline.Item color="red">
                      错误信息 - {currentTask.errorMessage}
                    </Timeline.Item>
                  )}
                </Timeline>
              </TabPane>
              
              <TabPane tab="配置信息" key="config">
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="最大页面数">
                    {currentTask.config?.maxPages || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="延迟时间">
                    {currentTask.config?.delay || '-'}秒
                  </Descriptions.Item>
                  <Descriptions.Item label="超时时间">
                    {currentTask.config?.timeout || '-'}秒
                  </Descriptions.Item>
                  <Descriptions.Item label="截图">
                    {currentTask.config?.screenshot ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="提取文本">
                    {currentTask.config?.extractText ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="提取链接">
                    {currentTask.config?.extractLinks ? '是' : '否'}
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default TaskDetail
