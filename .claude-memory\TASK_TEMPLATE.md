# 任务记录模板

## 文件命名规则

### 任务状态前缀

- `PENDING_` - 待开始的任务
- `ACTIVE_` - 进行中的任务  
- `DONE_` - 已完成的任务
- `FAILED_` - 失败的任务
- `CANCELLED_` - 已取消的任务

### 完整命名格式

`{状态前缀}TASK_{编号}_{任务简述}.md`

### 命名示例

- `PENDING_TASK_002_后端API优化.md` - 待开始任务
- `ACTIVE_TASK_003_数据库迁移.md` - 进行中任务
- `DONE_TASK_001_前端样式优化.md` - 已完成任务
- `FAILED_TASK_002_Docker服务启动测试.md` - 失败任务
- `CANCELLED_TASK_004_第三方集成.md` - 已取消任务

### 文件重命名操作

1. **任务开始时**: 将 `PENDING_` 改为 `ACTIVE_`
2. **任务完成时**: 将 `ACTIVE_` 改为 `DONE_`
3. **任务失败时**: 将 `ACTIVE_` 改为 `FAILED_`
4. **任务取消时**: 将当前前缀改为 `CANCELLED_`

## 任务基本信息

- **任务ID**: [自动生成的唯一标识，如TASK_001]
- **创建时间**: [YYYY-MM-DD HH:MM:SS]
- **任务标题**: [简短描述任务目标]
- **任务状态**: [PENDING/ACTIVE/DONE/FAILED/CANCELLED]
- **优先级**: [高/中/低]

## 任务描述

[详细描述用户的需求和期望结果]

## 任务分析

[对任务的技术分析和实现思路]

## 子任务清单

- [ ] 子任务1: [具体描述]
- [ ] 子任务2: [具体描述]
- [ ] 子任务3: [具体描述]

## 涉及文件

[列出需要修改或创建的文件路径]

- `文件路径1` - [修改内容简述]
- `文件路径2` - [修改内容简述]

## 实施记录

### [时间戳] - 开始任务

[记录开始时的状态和计划]

### [时间戳] - 子任务更新

[记录每个子任务的完成情况和遇到的问题]

### [时间戳] - 任务完成

[记录最终结果和总结]

## 遇到的问题和解决方案

[记录实施过程中的问题和解决方法]

## 测试验证

[记录如何验证任务完成情况]

## 相关文件变更

[记录所有文件的变更情况]

---
*此记录会随着任务进展实时更新*
