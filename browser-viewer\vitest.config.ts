import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/core': resolve(__dirname, 'src/core'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/shared': resolve(__dirname, 'src/shared'),
      '@/types': resolve(__dirname, 'src/types'),
    },
  },

  test: {
    globals: true,
    environment: 'jsdom',
    
    // 测试文件匹配模式
    include: [
      'tests/**/*.{test,spec}.{js,ts}',
      'src/**/*.{test,spec}.{js,ts}'
    ],
    
    // 测试设置文件
    setupFiles: [
      'tests/setup/global-setup.ts',
      'tests/setup/chrome-api-setup.ts'
    ],
    
    // 代码覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: 'tests/coverage',
      include: ['src/**/*.{js,ts}'],
      exclude: [
        'src/**/*.{test,spec}.{js,ts}',
        'src/types/**',
        'src/**/*.d.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // 并行测试配置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false
      }
    },
    
    // 测试超时配置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 监听模式配置
    watch: {
      ignore: ['dist/**', 'node_modules/**']
    },
    
    // 测试报告配置
    reporter: ['verbose'],
    
    // Mock 配置
    mockReset: true,
    restoreMocks: true,
    clearMocks: true,
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITEST: 'true'
    }
  }
}); 