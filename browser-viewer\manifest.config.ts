export const manifest: Partial<chrome.runtime.ManifestV3> = {
  manifest_version: 3,
  name: '数懒智能',
  version: '1.0.0',
  description: '浏览代理，行为模拟 | 高效并发 | 无感采集您需要的任何页面。',

  permissions: [
    'storage',
    'activeTab',
    'scripting',
    'tabs',
    'tabGroups',
    'offscreen',
    'webNavigation',
    'contextMenus',
    'cookies',
    'declarativeNetRequest',
    'system.display',
    'downloads'
  ],

  host_permissions: [
    '<all_urls>'
  ],

  background: {
    service_worker: 'src/background/service-worker.ts',
    type: 'module'
  },

  action: {
    default_popup: 'popup.html',
    default_title: '数懒孪生代理'
  },

  options_page: 'options.html',
  
  icons: {
    '16': 'icons/icon16.png',
    '32': 'icons/icon32.png',
    '48': 'icons/icon48.png',
    '128': 'icons/icon128.png'
  },

  web_accessible_resources: [
    {
      resources: [
        'offscreen.html',
        'assets/*',
        'chunks/*',
        'src/content-script.js'
      ],
      matches: ['<all_urls>']
    }
  ],

  declarative_net_request: {
    rule_resources: [
      {
        id: 'agent_rules',
        enabled: true,
        path: 'rules.json'
      }
    ]
  },

  content_security_policy: {
    extension_pages: "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'; worker-src 'self';"
  },

  minimum_chrome_version: '88'
}; 