/**
 * 数懒孪生代理 - 共享常量
 */

import type { BehaviorProfile, CaptureOptions, ResourceThresholds } from '@/shared/types';

// 性能阈值常量
export const PERFORMANCE_TARGETS: ResourceThresholds = {
  CPU: 0.15,        // 单标签页最大CPU占用
  MEMORY: 150,      // 单标签页内存(MB)
  NETWORK: 2,       // 单标签页网络(Mbps)
  FPS: 30,          // 最低帧率
};

// 完整性能阈值
export const PERFORMANCE_THRESHOLDS = {
  // 单标签页限制（技术方案中的关键性能指标）
  CPU_PER_TAB: 0.15,        // 单标签页最大CPU占用
  MEMORY_PER_TAB: 150,      // 单标签页内存(MB)
  NETWORK_PER_TAB: 2,       // 单标签页网络(Mbps)
  TASK_THROUGHPUT: 8,       // 每分钟处理页面数
  RESPONSE_LATENCY: 200,    // 异常响应延迟(ms)
  
  // 节流级别阈值
  CPU_LOW: 0.2,             // CPU低负载阈值
  CPU_HIGH: 0.70,           // CPU高负载阈值
  CPU_CRITICAL: 0.85,       // CPU临界阈值
  MEMORY_LOW: 512,          // 内存低负载阈值 (MB)
  MEMORY_HIGH: 1024,        // 内存高负载阈值 (MB)
  MEMORY_CRITICAL: 1536,    // 内存临界阈值 (MB)
  NETWORK_LOW: 1,           // 网络低负载阈值 (Mbps)
  LATENCY_HIGH: 500,        // 延迟高阈值 (ms)
  LATENCY_CRITICAL: 1000,   // 延迟临界阈值 (ms)
};

// 任务调度常量
export const TASK_CONSTANTS = {
  MAX_CONCURRENT_TABS: Math.max(2, Math.floor(navigator.hardwareConcurrency / 2)),
  MIN_CONCURRENCY: 2, // 保证至少有2个并发任务，避免系统资源完全闲置
  DEFAULT_TIMEOUT: 30000, // 30秒
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000, // 1秒
  TASK_QUEUE_SIZE: 100,
  HEARTBEAT_INTERVAL: 5000, // 5秒
};

// 关键性能指标 (KPIs)，用于高级监控和报告
export const PERFORMANCE_KPIS = {
  TASK_THROUGHPUT: 8,       // 目标：每分钟处理页面数
  RESPONSE_LATENCY: 200,    // 目标：异常响应延迟(ms)
};

// 行为模拟常量
export const BEHAVIOR_PROFILES: Record<string, BehaviorProfile> = {
  stealth: {
    scrollSpeed: 0.5,
    preClickDelay: { min: 150, max: 500 },
    postActionDelay: { min: 200, max: 600 },
    scrollPause: { min: 800, max: 1500 },
    randomness: 0.8,
    distractionRate: 0.3,
  },
  normal: {
    scrollSpeed: 1.0,
    preClickDelay: { min: 80, max: 250 },
    postActionDelay: { min: 100, max: 300 },
    scrollPause: { min: 400, max: 800 },
    randomness: 0.5,
    distractionRate: 0.2,
  },
  aggressive: {
    scrollSpeed: 2.0,
    preClickDelay: { min: 30, max: 100 },
    postActionDelay: { min: 50, max: 150 },
    scrollPause: { min: 100, max: 300 },
    randomness: 0.2,
    distractionRate: 0.1,
  },
};

export const SCROLL_CONSTANTS = {
  MIN_SCROLL_DURATION: 500,
  MAX_SCROLL_DURATION: 3000,
  SCROLL_STEP_SIZE: 100,
  BEZIER_CONTROL_POINT_VARIANCE: 0.2,
  FRAME_RATE: 60,
  FRAME_DURATION: 1000 / 60, // 16.67ms
};

// 视觉处理常量
export const CAPTURE_DEFAULTS: CaptureOptions = {
  quality: 80,
  blockSize: 1024,
  overlap: 50,
  format: 'jpeg',
};

export const CAPTURE_CONSTANTS = {
  MIN_BLOCK_SIZE: 256,
  MAX_BLOCK_SIZE: 2048,
  MAX_SEGMENTS: 50,
  COMPRESSION_QUALITY: {
    LOW: 60,
    MEDIUM: 75,
    HIGH: 90,
  },
  FORMATS: ['png', 'jpeg', 'webp'] as const,
  CAPTURE_RATE_LIMIT: {
    MAX_CALLS_PER_SECOND: 2,
    MIN_INTERVAL_MS: 500,
    BURST_COOLDOWN_MS: 3000,
  },
};

// 异常检测常量
export const EXCEPTION_PATTERNS = {
  CAPTCHA_SELECTORS: [
    '[class*="captcha"]',
    '[id*="captcha"]',
    'iframe[src*="recaptcha"]',
    '.g-recaptcha',
    '[class*="verification"]',
  ],
  REDIRECT_LOOP_THRESHOLD: 3,
  BEHAVIOR_ENTROPY_THRESHOLD: 0.7,
  DOM_CHANGE_THRESHOLD: 0.3,
};

export const EXCEPTION_TIMEOUTS = {
  CAPTCHA_RESPONSE: 60000, // 1分钟
  USER_INTERVENTION: 300000, // 5分钟
  RECOVERY_ATTEMPT: 10000, // 10秒
  STATE_RESTORE: 5000, // 5秒
};

// 消息类型常量
export const MESSAGE_TYPES = {
  // 1. 通用 & 系统
  PING: 'ping',
  PONG: 'pong',
  ERROR: 'error',
  SYSTEM_READY: 'system:ready',
  SYSTEM_SHUTDOWN: 'system:shutdown',
  PERFORMANCE_UPDATE: 'performance:update',

  // 2. UI <-> Service Worker 通信
  // UI -> SW
  GET_TASKS_STATUS: 'ui:get_tasks_status',
  CREATE_DEV_TASK: 'ui:create_dev_task',
  START_ALL_TASKS: 'ui:start_all_tasks',
  PAUSE_ALL_TASKS: 'ui:pause_all_tasks',
  CLEAR_ALL_TASKS: 'ui:clear_all_tasks',
  START_SELECTED_TASKS: 'ui:start_selected_tasks',
  PAUSE_SELECTED_TASKS: 'ui:pause_selected_tasks',
  CLEAR_SELECTED_TASKS: 'ui:clear_selected_tasks',
  DOWNLOAD_TASK_ASSETS: 'ui:download_task_assets',
  GET_TASK_METRICS: 'ui:get_task_metrics',
  // SW -> UI
  TASKS_UPDATED: 'tasks:updated',
  SYSTEM_STATUS: 'system:status',

  // 3. 任务生命周期 (TaskScheduler)
  TASK_CREATE: 'task:create',
  TASK_START: 'task:start',
  TASK_PAUSE: 'task:pause',
  TASK_RESUME: 'task:resume',
  TASK_CANCEL: 'task:cancel',
  TASK_STATUS: 'task:status',
  TASK_COMPLETED: 'task:completed', // Final state
  TASK_UPDATED: 'task:updated', // Generic update
  TASK_ERROR: 'task:error',
  TASK_STATUS_CHANGE: 'task:status_change',
  TASK_HEARTBEAT: 'task:heartbeat',
  
  // 4. 内容脚本/代理控制 (SW -> CS)
  ACTIVATE_AGENT: 'agent:activate',
  DEACTIVATE_AGENT: 'agent:deactivate',
  INJECT_MASK: 'agent:inject_mask',
  REMOVE_MASK: 'agent:remove_mask',
  GET_PAGE_INFO: 'agent:get_page_info',
  SCROLL_TO: 'agent:scroll_to',
  CLICK_ELEMENT: 'agent:click_element',
  EXTRACT_CONTENT: 'agent:extract_content',
  CLEAN_UP_PAGE: 'agent:clean_up_page',
  SIMULATE_FULL_PAGE_SCAN: 'agent:simulate_full_page_scan',
  GET_PAGE_SNAPSHOT: 'agent:get_page_snapshot',
  RESTORE_PAGE_SNAPSHOT: 'agent:restore_page_snapshot',
  CONTENT_SCRIPT_READY: 'content:ready',
  
  // 5. 页面扫描与截图 (CS -> SW)
  CAPTURE_AND_PROCESS_VIEWPORT: 'capture:process_viewport',
  CAPTURE_COMPLETED: 'capture:completed',
  CAPTURE_FAILED: 'capture:failed',
  FULL_PAGE_SCAN_COMPLETED: 'content:full_page_scan_completed',
  STRUCTURED_DATA_CAPTURED: 'content:structured_data_captured',
  
  // 6. 行为模拟 (内部事件/遥测)
  BEHAVIOR_SCROLL: 'behavior:scroll',
  BEHAVIOR_CLICK: 'behavior:click',
  BEHAVIOR_NAVIGATE: 'behavior:navigate',
  BEHAVIOR_TYPE: 'behavior:type',
  BEHAVIOR_WAIT: 'behavior:wait',
  SIMULATE_SCROLL: 'simulate:scroll',
  SIMULATE_CLICK: 'simulate:click',
  SIMULATE_TYPING: 'simulate:typing',
  SIMULATE_DISTRACTION: 'simulate:distraction',

  // 7. 资源代理 (CS <-> SW)
  PROXY_RESOURCE: 'proxy:resource',
  PROXY_RESOURCE_RESPONSE: 'proxy:resource_response',
  FETCH_RESOURCE_AS_DATA_URL: 'proxy:fetch_resource_as_data_url',

  // 8. 异常处理
  EXCEPTION_DETECTED: 'exception:detected',
  EXCEPTION_RESOLVED: 'exception:resolved',
  
  // 9. 模式切换
  MODE_SWITCH: 'mode:switch',
  MODE_CHANGED: 'mode:changed',
  
  // 10. Offscreen & Worker
  WORKER_RESULT: 'offscreen:worker_result',
  EXECUTE_WORKER_TASK: 'offscreen:execute_worker_task',
  OFFSCREEN_TASK_START: 'offscreen:task_start',
  OFFSCREEN_TASK_COMPLETE: 'offscreen:task_complete',
  PLAY_AUDIO: 'offscreen:play_audio',
  PAUSE_AUDIO: 'offscreen:pause_audio',

  // 11. 监控
  VISUAL_TASK_START_MONITORING: 'visual:start_monitoring',
  VISUAL_TASK_STOP_MONITORING: 'visual:stop_monitoring',
  
  // 5. 内容脚本通知 (CS -> SW)
  PAGE_LOADED: 'agent:page_loaded',
  PAGE_ERROR: 'agent:page_error',
  DOM_CHANGED: 'agent:dom_changed',
  CONTENT_EXTRACTED: 'agent:content_extracted',

  // Offscreen & Compute Messages
  PROCESS_IMAGE_SEGMENTS: 'compute:process_image_segments',
  
  // Downloader
  DOWNLOAD_AGENT_TASK_ASSETS: 'agent:download_task_assets',

  // Agent -> SW
  PAGE_DATA_UPDATE: 'agent:page_data_update',

  // 状态与通知
  POPUP_INITIALIZED: 'POPUP_INITIALIZED',
} as const;

// 存储键常量
export const STORAGE_KEYS = {
  SETTINGS: 'agent:settings',
  TASKS: 'agent:tasks',
  COMPLETED_TASKS: 'agent:completed_tasks_v1',
  CONFIG: 'agent:config',
  PERFORMANCE: 'agent:performance',
  EXCEPTIONS: 'agent:exceptions',
  SNAPSHOTS: 'agent:snapshots',
  WORKFLOWS: 'agent:workflows',
  PERMISSIONS: 'agent:permissions',
  PRIVACY_LOG: 'agent:privacy_log',
  ENCRYPTION_KEY: 'agent:encryption_key', // 用于会话加密密钥
  PRIVACY_SETTINGS: 'agent:privacy_settings',
  SESSION_KEY: 'agent:session_key'
} as const;

// DOM选择器常量
export const DOM_SELECTORS = {
  AGENT_MASK: '#agent-mask',
  VIEWPORT_HIGHLIGHT: '.viewport-highlight',
  ACTION_INDICATOR: '.action-indicator',
  CONTENT_AREA: '[role="main"], main, #content, .content',
  NAVIGATION: 'nav, [role="navigation"]',
  FORMS: 'form, [role="form"]',
  BUTTONS: 'button, [role="button"], input[type="button"], input[type="submit"]',
} as const;

// CSS类名常量
export const CSS_CLASSES = {
  AGENT_ACTIVE: 'agent-active',
  AGENT_PAUSED: 'agent-paused',
  AGENT_MASK: 'agent-mask',
  VIEWPORT_HIGHLIGHT: 'viewport-highlight',
  ACTION_INDICATOR: 'action-indicator',
  NO_INTERACTION: 'no-interaction',
} as const;

// 网络常量
export const NETWORK_CONSTANTS = {
  REQUEST_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  RATE_LIMIT: {
    REQUESTS_PER_SECOND: 10,
    REQUESTS_PER_MINUTE: 300,
    REQUESTS_PER_HOUR: 10000,
  },
  USER_AGENTS: [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  ],
};

// AI常量
export const AI_CONSTANTS = {
  API_ENDPOINT: 'https://api.example.com/v1/vision/process', // 占位符
  MODEL: 'example-vision-model-v1',
  TIMEOUT: 30000, // 30秒
};

// 安全常量
export const SECURITY_CONSTANTS = {
  ALLOWED_PROTOCOLS: ['http:', 'https:'],
  BLOCKED_DOMAINS: [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    'file://',
  ],
  SENSITIVE_PATTERNS: [
    /password/i,
    /credit.?card/i,
    /social.?security/i,
    /ssn/i,
    /api.?key/i,
    /token/i,
  ],
  MAX_CONTENT_SIZE: 50 * 1024 * 1024, // 50MB
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
};

// 版本信息
export const VERSION_INFO = {
  VERSION: '1.0.0',
  BUILD_DATE: new Date().toISOString(),
  MIN_CHROME_VERSION: 88,
  MANIFEST_VERSION: 3,
} as const;