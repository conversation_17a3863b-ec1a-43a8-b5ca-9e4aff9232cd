{"name": "digital-twin-agent", "version": "1.0.0", "description": "数懒孪生代理 - 浏览器智能代理插件", "type": "module", "scripts": {"dev": "vite build --mode development --watch", "dev:serve": "vite serve --mode development", "build": "npm run type-check && vite build --mode production", "build:dev": "vite build --mode development", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "vitest run tests/e2e", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "type-check": "tsc --noEmit", "postbuild": "node scripts/post-build.js"}, "dependencies": {"jszip": "^3.10.1", "rxjs": "^7.8.0", "sanitize-html": "latest", "vue": "^3.3.0", "webextension-polyfill": "latest"}, "devDependencies": {"@samrum/vite-plugin-web-extension": "^5.1.1", "@types/chrome": "latest", "@types/dom-to-image": "latest", "@types/node": "^24.0.3", "@types/sanitize-html": "latest", "@types/tailwindcss": "^3.0.11", "@types/webextension-polyfill": "latest", "@vitejs/plugin-vue": "^5.0.5", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.5.0", "eslint-plugin-vue": "latest", "fs-extra": "^11.0.0", "jsdom": "^26.1.0", "modern-screenshot": "latest", "rimraf": "latest", "tailwindcss": "latest", "terser": "^5.43.1", "typescript": "^5.2.0", "typescript-eslint": "^8.0.0", "vite": "^6.3.5", "vite-plugin-static-copy": "latest", "vitest": "^3.2.4", "vue-eslint-parser": "latest"}, "overrides": {"esbuild": "^0.25.5", "vite": "^6.3.5"}, "engines": {"node": ">=18"}}