/**
 * MaskController 单元测试
 * 测试蒙版控制器的UI操作功能
 */

import { CSS_CLASSES } from '@/shared/constants';
import type { Point, Rectangle } from '@/shared/types';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';

describe('MaskController', () => {
  let MaskController: typeof import('@/ui/mask/MaskController').MaskController;
  let maskController: import('@/ui/mask/MaskController').MaskController;

  beforeEach(async () => {
    // 设置DOM环境
    document.body.innerHTML = '';
    
    const module = await import('@/ui/mask/MaskController');
    MaskController = module.MaskController;
    
    maskController = new MaskController();
  });

  afterEach(() => {
    maskController?.destroy();
    document.body.innerHTML = '';
  });

  describe('初始化', () => {
    it('应该成功创建蒙版元素', () => {
      expect(maskController.element).toBeDefined();
      expect(maskController.element.tagName).toBe('DIV');
      expect(maskController.element.classList.contains(CSS_CLASSES.AGENT_MASK)).toBe(true);
    });

    it('应该将蒙版添加到页面', () => {
      expect(document.body.contains(maskController.element)).toBe(true);
    });

    it('应该创建高亮元素', () => {
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      expect(highlight).toBeDefined();
      expect(highlight.tagName).toBe('DIV');
    });

    it('应该创建鼠标元素', () => {
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      expect(mouse).toBeDefined();
      expect(mouse.tagName).toBe('DIV');
    });

    it('应该默认隐藏蒙版', () => {
      expect(maskController.element.style.display).toBe('');
    });
  });

  describe('显示和隐藏', () => {
    it('应该显示蒙版', () => {
      maskController.show();
      expect(maskController.element.style.display).toBe('block');
    });

    it('应该隐藏蒙版', () => {
      maskController.show(); // 先显示
      maskController.hide();
      expect(maskController.element.style.display).toBe('none');
    });

    it('应该支持重复显示/隐藏操作', () => {
      maskController.show();
      maskController.show();
      expect(maskController.element.style.display).toBe('block');
      
      maskController.hide();
      maskController.hide();
      expect(maskController.element.style.display).toBe('none');
    });
  });

  describe('高亮区域控制', () => {
    it('应该更新高亮区域位置和大小', () => {
      const rect: Rectangle = {
        x: 100,
        y: 200,
        width: 300,
        height: 150
      };
      
      maskController.updateHighlight(rect);
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      expect(highlight.style.left).toBe('100px');
      expect(highlight.style.top).toBe('200px');
      expect(highlight.style.width).toBe('300px');
      expect(highlight.style.height).toBe('150px');
    });

    it('应该显示高亮区域', () => {
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      highlight.style.display = 'block';
      expect(highlight.style.display).toBe('block');
    });

    it('应该隐藏高亮区域', () => {
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      highlight.style.display = 'block'; // 先显示
      highlight.style.display = 'none'; // 再隐藏
      expect(highlight.style.display).toBe('none');
    });

    it('应该处理零尺寸的高亮区域', () => {
      const rect: Rectangle = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
      
      maskController.updateHighlight(rect);
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      expect(highlight.style.left).toBe('0px');
      expect(highlight.style.top).toBe('0px');
      expect(highlight.style.width).toBe('0px');
      expect(highlight.style.height).toBe('0px');
    });

    it('应该处理负坐标的高亮区域', () => {
      const rect: Rectangle = {
        x: -50,
        y: -100,
        width: 200,
        height: 300
      };
      
      maskController.updateHighlight(rect);
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      expect(highlight.style.left).toBe('-50px');
      expect(highlight.style.top).toBe('-100px');
    });
  });

  describe('鼠标位置控制', () => {
    it('应该更新鼠标位置', () => {
      const point: Point = {
        x: 150,
        y: 250
      };
      
      maskController.updateMousePosition(point);
      
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      expect(mouse.style.left).toBe('150px');
      expect(mouse.style.top).toBe('250px');
    });

    it('应该显示鼠标指示器', () => {
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      mouse.style.display = 'block';
      expect(mouse.style.display).toBe('block');
    });

    it('应该隐藏鼠标指示器', () => {
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      mouse.style.display = 'block'; // 先显示
      mouse.style.display = 'none'; // 再隐藏
      expect(mouse.style.display).toBe('none');
    });

    it('应该处理边界坐标', () => {
      const point: Point = {
        x: 0,
        y: 0
      };
      
      maskController.updateMousePosition(point);
      
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      expect(mouse.style.left).toBe('0px');
      expect(mouse.style.top).toBe('0px');
    });
  });

  describe('组合操作', () => {
    it('应该同时显示蒙版、高亮和鼠标', () => {
      const rect: Rectangle = { x: 10, y: 20, width: 100, height: 50 };
      const point: Point = { x: 60, y: 45 };
      
      maskController.show();
      maskController.updateHighlight(rect);
      maskController.updateMousePosition(point);
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      
      highlight.style.display = 'block';
      mouse.style.display = 'block';
      
      expect(maskController.element.style.display).toBe('block');
      expect(highlight.style.display).toBe('block');
      expect(mouse.style.display).toBe('block');
    });

    it('应该支持动态更新', () => {
      maskController.show();
      
      // 第一次更新
      maskController.updateHighlight({ x: 10, y: 20, width: 100, height: 50 });
      maskController.updateMousePosition({ x: 60, y: 45 });
      
      // 第二次更新
      maskController.updateHighlight({ x: 50, y: 60, width: 200, height: 100 });
      maskController.updateMousePosition({ x: 150, y: 110 });
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      
      expect(highlight.style.left).toBe('50px');
      expect(highlight.style.top).toBe('60px');
      expect(mouse.style.left).toBe('150px');
      expect(mouse.style.top).toBe('110px');
    });
  });

  describe('销毁和清理', () => {
    it('应该正确销毁蒙版', () => {
      const element = maskController.element;
      
      maskController.destroy();
      
      expect(document.body.contains(element)).toBe(false);
    });

    it('应该处理重复销毁调用', () => {
      maskController.destroy();
      
      // 第二次调用不应该抛出错误
      expect(() => maskController.destroy()).not.toThrow();
    });

    it('应该在销毁后停止响应方法调用', () => {
      maskController.destroy();
      
      // 这些调用不应该抛出错误
      expect(() => maskController.show()).not.toThrow();
      expect(() => maskController.hide()).not.toThrow();
      expect(() => maskController.updateHighlight({ x: 0, y: 0, width: 100, height: 100 })).not.toThrow();
    });
  });

  describe('CSS类和样式', () => {
    it('应该应用正确的CSS类', () => {
      expect(maskController.element.classList.contains(CSS_CLASSES.AGENT_MASK)).toBe(true);
      
      const highlight = maskController.element.querySelector(`.${CSS_CLASSES.VIEWPORT_HIGHLIGHT}`);
      const mouse = maskController.element.querySelector('.agent-mouse-pointer');
      
      expect(highlight.classList.contains(CSS_CLASSES.VIEWPORT_HIGHLIGHT)).toBe(true);
      expect(mouse.classList.contains('agent-mouse-pointer')).toBe(true);
    });

    it('应该设置正确的默认样式', () => {
      // 由于MaskController没有设置默认样式，我们只检查元素存在
      expect(maskController.element).toBeDefined();
      expect(maskController.element.id).toBe('agent-mask');
      expect(maskController.element.classList.contains(CSS_CLASSES.AGENT_MASK)).toBe(true);
    });
  });

  describe('事件处理', () => {
    it('应该防止鼠标事件冒泡', () => {
      const mockEvent = new MouseEvent('click', { bubbles: true });
      let eventPropagated = false;
      
      document.addEventListener('click', () => {
        eventPropagated = true;
      });
      
      maskController.element.dispatchEvent(mockEvent);
      
      // 事件应该被阻止冒泡
      expect(eventPropagated).toBe(false);
    });
  });
});