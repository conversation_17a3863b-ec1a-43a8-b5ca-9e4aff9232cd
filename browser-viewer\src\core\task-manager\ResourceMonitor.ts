/**
 * 资源监控器 - 监控系统资源使用情况
 */

import { PERFORMANCE_THRESHOLDS } from '@/shared/constants';
import type { PerformanceMetrics } from '@/shared/types';
import { createLogger, getCurrentTimestamp } from '@/shared/utils';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { PerformanceInterruptionController } from './PerformanceInterruptionController';

// 扩展内置类型以包含非标准API
interface PerformanceWithMemory extends Performance {
  memory: {
    totalJSHeapSize: number;
    usedJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}
interface NavigatorWithConnection extends Navigator {
  connection: {
    downlink: number;
    effectiveType: string;
    rtt: number;
    saveData: boolean;
  };
  deviceMemory?: number;
}

export class ResourceMonitor {
  private readonly _logger = createLogger('ResourceMonitor');
  private readonly _destroy$ = new Subject<void>();
  
  // 性能监控
  private readonly _metrics$ = new BehaviorSubject<PerformanceMetrics>({
    cpu: 0,
    memory: 0,
    network: 0,
    fps: 0,
    latency: 0,
    timestamp: getCurrentTimestamp()
  });
  
  // 监控状态
  private _isMonitoring = false;
  private _monitoringInterval?: NodeJS.Timeout;
  private _canMeasureFps = false; // 能力检测标志
  private _isServiceWorker = false; // Service Worker环境标志
  private _performanceObserverStarted = false; // 防止重复启动PerformanceObserver
  
  // 历史数据
  private readonly _metricsHistory: PerformanceMetrics[] = [];
  private readonly _MAX_HISTORY_SIZE = 100;
  private readonly LONG_TASK_WINDOW_MS = 5000; // 5 seconds
  private _longTasks: PerformanceEntry[] = [];
  
  start(): void {
    if (this._isMonitoring) {
      return;
    }
    
    this._logger.info('Starting resource monitoring');
    this._isMonitoring = true;
    
    // --- 一次性环境和能力检测 ---
    this._isServiceWorker = typeof importScripts === 'function' && typeof window === 'undefined';
    
    if (typeof self !== 'undefined' && typeof self.requestAnimationFrame !== 'undefined') {
      this._canMeasureFps = true;
    } else {
      if (this._isServiceWorker) {
        // 在Service Worker中这是预期的，只记录debug级别的日志
        this._logger.debug('requestAnimationFrame is not available in Service Worker context. FPS metrics will be disabled.');
      } else {
        this._logger.warn('requestAnimationFrame is not available in this context. FPS metrics will be disabled.');
      }
      this._canMeasureFps = false;
    }

    // 开始定期监控
    this._monitoringInterval = setInterval(() => {
      void this._collectMetrics();
    }, 1000); // 每秒收集一次
    
    // 开始性能观察（带防重复机制）
    if (!this._performanceObserverStarted) {
      this._startPerformanceObserver();
      this._performanceObserverStarted = true;
    }
  }
  
  stop(): void {
    if (!this._isMonitoring) {
      return;
    }
    
    this._logger.info('Stopping resource monitoring');
    this._isMonitoring = false;
    this._performanceObserverStarted = false; // 重置标志，允许重新启动
    
    if (this._monitoringInterval) {
      clearInterval(this._monitoringInterval);
      this._monitoringInterval = undefined;
    }
    
    this._destroy$.next();
    this._destroy$.complete();
  }
  
  public isMonitoring(): boolean {
    return this._isMonitoring;
  }
  
  getCurrentMetrics(): PerformanceMetrics {
    return this._metrics$.value;
  }
  
  getMetricsHistory(): PerformanceMetrics[] {
    return [...this._metricsHistory];
  }
  
  getAverageMetrics(windowSize: number = 10): PerformanceMetrics {
    const recentMetrics = this._metricsHistory.slice(-windowSize);
    if (recentMetrics.length === 0) {
      return this.getCurrentMetrics();
    }
    
    const sum = recentMetrics.reduce((acc, metrics) => ({
      cpu: acc.cpu + metrics.cpu,
      memory: acc.memory + metrics.memory,
      network: acc.network + metrics.network,
      fps: acc.fps + metrics.fps,
      latency: acc.latency + metrics.latency,
      timestamp: acc.timestamp
    }), {
      cpu: 0,
      memory: 0,
      network: 0,
      fps: 0,
      latency: 0,
      timestamp: getCurrentTimestamp()
    });
    
    const count = recentMetrics.length;
    return {
      cpu: sum.cpu / count,
      memory: sum.memory / count,
      network: sum.network / count,
      fps: sum.fps / count,
      latency: sum.latency / count,
      timestamp: getCurrentTimestamp()
    };
  }
  
  isResourceStressed(): boolean {
    const metrics = this.getCurrentMetrics();
    return metrics.cpu > PERFORMANCE_THRESHOLDS.CPU_HIGH ||
          metrics.memory > PERFORMANCE_THRESHOLDS.MEMORY_HIGH ||
          metrics.latency > PERFORMANCE_THRESHOLDS.LATENCY_HIGH;
  }
  
  isResourceCritical(): boolean {
    const metrics = this.getCurrentMetrics();
    return metrics.cpu > PERFORMANCE_THRESHOLDS.CPU_CRITICAL ||
          metrics.memory > PERFORMANCE_THRESHOLDS.MEMORY_CRITICAL ||
          metrics.latency > PERFORMANCE_THRESHOLDS.LATENCY_CRITICAL;
  }
  
  getResourceUtilization(): number {
    const metrics = this.getCurrentMetrics();
    
    // 计算综合资源利用率 (0-1)
    const cpuUtilization = Math.min(metrics.cpu / PERFORMANCE_THRESHOLDS.CPU_HIGH, 1);
    const memoryUtilization = Math.min(metrics.memory / PERFORMANCE_THRESHOLDS.MEMORY_HIGH, 1);
    const latencyPenalty = Math.min(metrics.latency / PERFORMANCE_THRESHOLDS.LATENCY_HIGH, 1);
    
    return (cpuUtilization + memoryUtilization + latencyPenalty) / 3;
  }
  
  // Observable接口
  get metrics$(): Observable<PerformanceMetrics> {
    return this._metrics$.asObservable();
  }
  
  // 获取资源变化事件
  get resourceChanges$(): Observable<{ type: string; metrics: PerformanceMetrics }> {
    return this._metrics$.pipe(
      distinctUntilChanged((prev, curr) => {
        // 只有当资源使用率变化超过阈值时才触发事件
        const cpuDiff = Math.abs(curr.cpu - prev.cpu);
        const memoryDiff = Math.abs(curr.memory - prev.memory);
        const latencyDiff = Math.abs(curr.latency - prev.latency);
        
        return cpuDiff < 0.05 && memoryDiff < 50 && latencyDiff < 50;
      }),
      map(metrics => ({
        type: this._getResourceStatus(metrics),
        metrics
      }))
    );
  }
  
  // 私有方法
  private async _collectMetrics(): Promise<void> {
    try {
      // --- 条件化指标收集 ---
      const fps = this._canMeasureFps ? await this._getFpsMetrics() : 0;

      const metrics: PerformanceMetrics = {
        cpu: this._getCpuUsage(),
        memory: this._getMemoryUsage(),
        network: this._getNetworkUsage(),
        fps: fps,
        latency: await this._getLatencyMetrics(),
        timestamp: getCurrentTimestamp()
      };
      
      // 更新当前指标
      this._metrics$.next(metrics);
      
      // 添加到历史记录
      this._metricsHistory.push(metrics);
      if (this._metricsHistory.length > this._MAX_HISTORY_SIZE) {
        this._metricsHistory.shift();
      }
      
    } catch (error) {
      this._logger.error('Failed to collect metrics:', error);
    }
  }
  
  private _getCpuUsage(): number {
    const now = performance.now();
    // 清理旧的长任务记录
    this._longTasks = this._longTasks.filter(
      (task) => now - (task.startTime + task.duration) < this.LONG_TASK_WINDOW_MS
    );

    if (this._longTasks.length === 0) {
      return 0;
    }

    // 计算在时间窗口内，长任务占用的总时长
    const totalDuration = this._longTasks.reduce((sum, task) => sum + task.duration, 0);

    // CPU 使用率估算为：长任务总时长 / 时间窗口总时长
    const usage = totalDuration / this.LONG_TASK_WINDOW_MS;

    return Math.min(usage, 1); // 结果限制在 0 和 1 之间
  }
  
  private _getMemoryUsage(): number {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      // 返回已使用的内存大小（字节）
      return (performance as PerformanceWithMemory).memory.usedJSHeapSize;
    }
    
    // 如果没有 performance.memory API，尝试其他方法
    const nav = navigator as NavigatorWithConnection;
    if (nav.deviceMemory) {
      // 估算内存使用情况
      return nav.deviceMemory * 1024 * 1024 * 1024 * 0.3; // 假设使用30%的设备内存
    }
    
    return 0;
  }
  
  private _getNetworkUsage(): number {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as NavigatorWithConnection).connection;
      if (connection?.downlink) {
        return connection.downlink; // Mbps
      }
    }
    
    return 0;
  }
  
  private async _getFpsMetrics(): Promise<number> {
    // 前置检查已移至 start() 方法，这里假设 API 可用
    return new Promise((resolve) => {
      const frameTimes: number[] = [];
      let frameCount = 0;
      
      const countFrames = (now: number) => {
        frameCount++;
        frameTimes.push(now);
        
        if (frameTimes.length > 2) {
          const firstTime = frameTimes[0];
          const lastTime = frameTimes[frameTimes.length - 1];
          const duration = (lastTime - firstTime) / 1000;
          
          if (duration >= 1) {
            const fps = Math.round(frameCount / duration);
            resolve(fps);
            return;
          }
        }
        
        requestAnimationFrame(countFrames);
      };
      
      requestAnimationFrame(countFrames);
    });
  }
  
  private async _getLatencyMetrics(): Promise<number> {
    // 测量网络延迟
    const startTime = performance.now();
    
    try {
      // 发送一个简单的请求来测量延迟
      await fetch('data:text/plain,ping', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      return performance.now() - startTime;
    } catch {
      return 0;
    }
  }
  
  private _startPerformanceObserver(): void {
    if (typeof PerformanceObserver === 'undefined') {
      this._logger.warn('PerformanceObserver is not supported in this context.');
      return;
    }
    
    if (this._isServiceWorker) {
      this._initializeServiceWorkerObserver();
      return;
    }
    
    this._initializeFullFeatureObserver();
  }

  private _initializeServiceWorkerObserver(): void {
    this._logger.debug('Service Worker environment detected. Using limited PerformanceObserver capabilities.');
    
    try {
      const observer = new PerformanceObserver((list) => {
        this._processServiceWorkerEntries(list.getEntries());
      });
      
      this._observeResourceEntries(observer);
    } catch (error) {
      this._logger.debug('PerformanceObserver initialization failed in Service Worker:', error);
    }
  }

  private _processServiceWorkerEntries(entries: PerformanceEntry[]): void {
    for (const entry of entries) {
      if (entry.entryType === 'resource' && entry.duration > 1000) {
        this._logger.debug(`Slow resource load detected: ${entry.name} (${entry.duration}ms)`);
      }
    }
  }

  private _observeResourceEntries(observer: PerformanceObserver): void {
    try {
      observer.observe({ entryTypes: ['resource'] });
      this._logger.debug('PerformanceObserver started in Service Worker mode with resource monitoring.');
    } catch {
      this._logger.debug('Resource entry type is also not supported in this Service Worker context.');
    }
  }

  private _initializeFullFeatureObserver(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        this._processPerformanceEntries(list.getEntries());
      });
      
      const supportedEntryTypes = this._getSupportedEntryTypes();
      this._startObserverWithSupportedTypes(observer, supportedEntryTypes);
    } catch (error) {
      this._logger.error('Failed to start PerformanceObserver:', error);
    }
  }

  private _getSupportedEntryTypes(): string[] {
    const typesToCheck = ['longtask', 'event', 'navigation', 'resource'];
    const supportedEntryTypes: string[] = [];
    
    for (const entryType of typesToCheck) {
      if (this._isEntryTypeSupported(entryType)) {
        supportedEntryTypes.push(entryType);
      }
    }
    
    // 'event' 是一个有效的类型，但需要显式检查 PerformanceEventTiming 支持
    if (supportedEntryTypes.includes('event')) {
      if (typeof PerformanceEventTiming === 'undefined') {
        this._logger.debug("Entry type 'event' is listed but PerformanceEventTiming is not supported. Removing.");
        supportedEntryTypes.splice(supportedEntryTypes.indexOf('event'), 1);
      }
    }

    return supportedEntryTypes;
  }

  private _isEntryTypeSupported(entryType: string): boolean {
    try {
      const testObserver = new PerformanceObserver(() => {});
      testObserver.observe({ entryTypes: [entryType] });
      testObserver.disconnect();
      return true;
    } catch {
      this._logger.debug(`Entry type '${entryType}' is not supported in this context.`);
      return false;
    }
  }

  private _startObserverWithSupportedTypes(observer: PerformanceObserver, supportedEntryTypes: string[]): void {
    if (supportedEntryTypes.length > 0) {
      observer.observe({ entryTypes: supportedEntryTypes });
      this._logger.debug(`PerformanceObserver started with supported types: ${supportedEntryTypes.join(', ')}`);
    } else {
      this._logger.info('No supported PerformanceObserver entry types available in this context.');
    }
  }
  
  private _getResourceStatus(_metrics: PerformanceMetrics): string {
    if (this.isResourceCritical()) {
      return 'critical';
    } else if (this.isResourceStressed()) {
      return 'stressed';
    } else if (this.getResourceUtilization() > 0.5) {
      return 'moderate';
    } else {
      return 'normal';
    }
  }

  private _processPerformanceEntries(entries: PerformanceEntry[]): void {
    for (const entry of entries) {
      this._processPerformanceEntry(entry);
    }
  }

  private _processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'longtask':
        this._handleLongTask(entry);
        break;
      case 'event':
        this._handleEventTiming(entry);
        break;
      case 'navigation':
        this._handleNavigationTiming(entry);
        break;
      case 'resource':
        this._handleResourceTiming(entry);
        break;
    }
  }

  private _handleLongTask(entry: PerformanceEntry): void {
    if (PerformanceInterruptionController.isPaused()) {
      this._logger.debug(
        `Ignoring long task during intentional pause: duration=${entry.duration.toFixed(2)}ms`,
      );
      return;
    }

    // 存储长任务条目，供 _getCpuUsage 方法使用
    this._longTasks.push(entry);

    // 对超过800ms的真正严重的长任务发出提醒
    if (entry.duration > 800) {
      this._logger.info(
        `Very long task detected: duration=${entry.duration.toFixed(2)}ms, name=${entry.name}`,
        entry
      );
    // 一般的长任务（150ms-800ms），debug级别记录，避免告警刷屏
    } else if (entry.duration > 150) {
      this._logger.debug(
        `Long task detected: duration=${entry.duration.toFixed(2)}ms, name=${entry.name}`,
        entry
      );
    }
  }

  private _handleEventTiming(entry: PerformanceEntry): void {
    if (entry.duration > 100) {
      const eventEntry = entry as PerformanceEntry & {
        name: string;
        duration: number;
        target?: { getBoundingClientRect?: () => DOMRect }
      };
      this._logger.warn('Slow event detected:', {
        name: eventEntry.name,
        duration: eventEntry.duration,
      });
      this._logEventTargetRect(eventEntry.target);
    }
  }

  private _logEventTargetRect(target: unknown): void {
    if (target && typeof target === 'object' && target !== null && 'getBoundingClientRect' in target) {
      const element = target as { getBoundingClientRect: () => DOMRect };
      if (typeof element.getBoundingClientRect === 'function') {
        const rect = element.getBoundingClientRect();
        if (rect) {
          this._logger.info('Slow event target rect:', rect.x, rect.y, rect.width, rect.height);
        }
      }
    }
  }

  private _handleNavigationTiming(entry: PerformanceEntry): void {
    this._logger.debug('Navigation timing:', {
      duration: entry.duration,
      name: entry.name,
      entryType: entry.entryType,
    });
  }

  private _handleResourceTiming(entry: PerformanceEntry): void {
    if (entry.duration > 1000) {
      this._logger.debug(`Slow resource load: ${entry.name} (${entry.duration}ms)`);
    }
  }
}