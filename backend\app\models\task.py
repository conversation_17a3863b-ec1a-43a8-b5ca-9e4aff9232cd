"""
任务数据模型
基于browser-viewer的任务设计
"""

from sqlalchemy import (
    Column,
    String,
    Text,
    DateTime,
    Integer,
    Float,
    Boolean,
    JSON,
    ForeignKey,
    Enum,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, Dict, Any, List

from app.core.database import Base


class TaskStatus(PyEnum):
    """任务状态枚举 - 继承browser-viewer的状态设计"""

    PENDING = "pending"  # 等待中
    RUNNING = "running"  # 执行中
    PAUSED = "paused"  # 已暂停
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskPriority(PyEnum):
    """任务优先级"""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Task(Base):
    """任务模型 - 核心任务实体"""

    __tablename__ = "tasks"

    # 基础字段
    id = Column(String(36), primary_key=True, index=True)
    batch_id = Column(String(36), index=True, nullable=True)  # 批次ID
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)

    # 任务配置 - 存储browser-viewer的TaskConfig
    config = Column(JSON, nullable=False)  # 任务配置JSON
    urls = Column(JSON, nullable=False)  # 目标URL列表

    # 状态管理
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, index=True)
    priority = Column(Enum(TaskPriority), default=TaskPriority.NORMAL)

    # 进度跟踪
    total_urls = Column(Integer, default=0)
    processed_urls = Column(Integer, default=0)
    succeeded_urls = Column(Integer, default=0)
    failed_urls = Column(Integer, default=0)
    progress_percentage = Column(Float, default=0.0)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 执行信息
    assigned_worker = Column(String(100), nullable=True)  # 分配的工作节点
    execution_time = Column(Float, nullable=True)  # 执行时间(秒)
    retry_count = Column(Integer, default=0)  # 重试次数
    error_message = Column(Text, nullable=True)  # 错误信息

    # 资源使用
    memory_usage = Column(Float, nullable=True)  # 内存使用(MB)
    cpu_usage = Column(Float, nullable=True)  # CPU使用率

    # 元数据
    metadata_info = Column(JSON, nullable=True)  # 额外元数据
    tags = Column(JSON, nullable=True)  # 标签

    # 关联关系
    user = relationship("User", back_populates="tasks")
    task_results = relationship(
        "TaskResult", back_populates="task", cascade="all, delete-orphan"
    )
    task_logs = relationship(
        "TaskLog", back_populates="task", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Task(id={self.id}, status={self.status}, user_id={self.user_id})>"

    @property
    def is_active(self) -> bool:
        """判断任务是否处于活跃状态"""
        return self.status in [TaskStatus.PENDING, TaskStatus.RUNNING]

    @property
    def is_completed(self) -> bool:
        """判断任务是否已完成"""
        return self.status in [
            TaskStatus.COMPLETED,
            TaskStatus.FAILED,
            TaskStatus.CANCELLED,
        ]

    def update_progress(self, processed: int, succeeded: int, failed: int):
        """更新任务进度"""
        self.processed_urls = processed
        self.succeeded_urls = succeeded
        self.failed_urls = failed

        if self.total_urls > 0:
            self.progress_percentage = (processed / self.total_urls) * 100

        self.updated_at = datetime.utcnow()

    def mark_as_started(self, worker_id: str = None):
        """标记任务开始执行"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.utcnow()
        if worker_id:
            self.assigned_worker = worker_id

    def mark_as_completed(self, execution_time: float = None):
        """标记任务完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        if execution_time:
            self.execution_time = execution_time

    def mark_as_failed(self, error_message: str = None):
        """标记任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.utcnow()
        if error_message:
            self.error_message = error_message

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "batch_id": self.batch_id,
            "user_id": self.user_id,
            "config": self.config,
            "urls": self.urls,
            "status": self.status.value,
            "priority": self.priority.value,
            "total_urls": self.total_urls,
            "processed_urls": self.processed_urls,
            "succeeded_urls": self.succeeded_urls,
            "failed_urls": self.failed_urls,
            "progress_percentage": self.progress_percentage,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": (
                self.completed_at.isoformat() if self.completed_at else None
            ),
            "execution_time": self.execution_time,
            "retry_count": self.retry_count,
            "error_message": self.error_message,
            "assigned_worker": self.assigned_worker,
            "metadata_info": self.metadata_info,
            "tags": self.tags,
        }


class TaskResult(Base):
    """任务结果模型 - 存储任务执行结果"""

    __tablename__ = "task_results"

    id = Column(String(36), primary_key=True, index=True)
    task_id = Column(String(36), ForeignKey("tasks.id"), nullable=False, index=True)
    url = Column(Text, nullable=False)  # 处理的URL

    # 结果数据
    extracted_data = Column(JSON, nullable=True)  # 提取的数据
    processed_data = Column(JSON, nullable=True)  # 处理后的数据
    screenshots = Column(JSON, nullable=True)  # 截图信息

    # 状态信息
    status = Column(String(20), nullable=False)  # 处理状态
    processing_time = Column(Float, nullable=True)  # 处理时间
    error_message = Column(Text, nullable=True)  # 错误信息

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    task = relationship("Task", back_populates="task_results")


class TaskLog(Base):
    """任务日志模型 - 记录任务执行日志"""

    __tablename__ = "task_logs"

    id = Column(String(36), primary_key=True, index=True)
    task_id = Column(String(36), ForeignKey("tasks.id"), nullable=False, index=True)

    # 日志信息
    level = Column(String(10), nullable=False)  # 日志级别
    message = Column(Text, nullable=False)  # 日志消息
    details = Column(JSON, nullable=True)  # 详细信息

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # 关联关系
    task = relationship("Task", back_populates="task_logs")
