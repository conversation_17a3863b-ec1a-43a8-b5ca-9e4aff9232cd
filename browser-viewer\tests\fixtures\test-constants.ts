/**
 * 测试常量和配置
 * 定义测试中使用的常量值和配置
 */

import type { BehaviorProfile, OutputFormat } from '@/shared/types';

// 测试用的 URL 常量
export const TEST_URLS = {
  VALID: 'https://example.com',
  HTTPS: 'https://secure.example.com',
  HTTP: 'http://insecure.example.com',
  LOCAL: 'http://localhost:3000',
  INVALID: 'not-a-url',
  LONG: 'https://very-long-domain-name-for-testing-purposes.example.com/very/long/path/with/many/segments',
  WITH_PARAMS: 'https://example.com?param1=value1&param2=value2',
  WITH_HASH: 'https://example.com#section',
  COMPLEX: 'https://example.com/path?param=value#section',
} as const;

// 测试用的行为配置常量
export const TEST_BEHAVIOR_PROFILES: Record<string, BehaviorProfile> = {
  STEALTH: {
    scrollSpeed: 0.2,
    preClickDelay: { min: 500, max: 1000 },
    randomness: 0.8,
    distractionRate: 0.1,
  },
  NORMAL: {
    scrollSpeed: 0.5,
    preClickDelay: { min: 100, max: 300 },
    randomness: 0.5,
    distractionRate: 0.3,
  },
  AGGRESSIVE: {
    scrollSpeed: 0.9,
    preClickDelay: { min: 20, max: 50 },
    randomness: 0.2,
    distractionRate: 0.5,
  },
} as const;

// 测试用的输出格式常量
export const TEST_OUTPUT_FORMATS: Record<string, OutputFormat> = {
  TEXT: 'text',
  MARKDOWN: 'markdown',
  JSON: 'json',
} as const;

// 测试用的任务状态常量
export const TEST_TASK_STATUSES = {
  PENDING: 'pending',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// 测试用的错误消息常量
export const TEST_ERROR_MESSAGES = {
  INVALID_URL: 'Invalid URL provided',
  NETWORK_ERROR: 'Network request failed',
  TIMEOUT_ERROR: 'Operation timed out',
  PERMISSION_DENIED: 'Permission denied',
  RESOURCE_NOT_FOUND: 'Resource not found',
  INVALID_CONFIG: 'Invalid configuration',
  TASK_NOT_FOUND: 'Task not found',
  ALREADY_RUNNING: 'Task is already running',
  NOT_STARTED: 'Task has not been started',
} as const;

// 测试用的延迟时间常量（毫秒）
export const TEST_TIMEOUTS = {
  VERY_SHORT: 10,
  SHORT: 100,
  MEDIUM: 1000,
  LONG: 5000,
  VERY_LONG: 10000,
  EXTREME: 30000,
} as const;

// 测试用的尺寸常量
export const TEST_DIMENSIONS = {
  VIEWPORT: {
    MOBILE: { width: 375, height: 667 },
    TABLET: { width: 768, height: 1024 },
    DESKTOP: { width: 1920, height: 1080 },
    LARGE: { width: 2560, height: 1440 },
  },
  IMAGE: {
    SMALL: { width: 100, height: 100 },
    MEDIUM: { width: 500, height: 500 },
    LARGE: { width: 1000, height: 1000 },
  },
} as const;

// 测试用的性能阈值常量
export const TEST_PERFORMANCE_THRESHOLDS = {
  CPU_USAGE: 0.8,
  MEMORY_USAGE: 0.9,
  NETWORK_LATENCY: 1000,
  PROCESSING_TIME: 5000,
  RESPONSE_TIME: 2000,
} as const;

// 测试用的数据大小常量（字节）
export const TEST_DATA_SIZES = {
  SMALL: 1024,         // 1KB
  MEDIUM: 1024 * 1024, // 1MB
  LARGE: 10 * 1024 * 1024, // 10MB
} as const;

// 测试用的重试配置常量
export const TEST_RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  BACKOFF_MULTIPLIER: 2,
} as const;

// 测试用的并发配置常量
export const TEST_CONCURRENCY_CONFIG = {
  LOW: 1,
  MEDIUM: 3,
  HIGH: 8,
  MAX: 16,
} as const;

// 测试用的用户代理字符串
export const TEST_USER_AGENTS = {
  CHROME: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  FIREFOX: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  SAFARI: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
  MOBILE: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  TEST: 'TestAgent/1.0.0',
} as const;

// 测试用的请求头
export const TEST_HEADERS = {
  CONTENT_TYPE_JSON: { 'Content-Type': 'application/json' },
  CONTENT_TYPE_TEXT: { 'Content-Type': 'text/plain' },
  ACCEPT_JSON: { 'Accept': 'application/json' },
  ACCEPT_ALL: { 'Accept': '*/*' },
  AUTHORIZATION: { 'Authorization': 'Bearer test-token' },
  USER_AGENT: { 'User-Agent': TEST_USER_AGENTS.TEST },
} as const;

// 测试用的存储键
export const TEST_STORAGE_KEYS = {
  TASK_STATE: 'test_task_state',
  USER_SETTINGS: 'test_user_settings',
  CACHE_DATA: 'test_cache_data',
  SESSION_DATA: 'test_session_data',
  PERFORMANCE_METRICS: 'test_performance_metrics',
} as const;

// 测试用的消息类型
export const TEST_MESSAGE_TYPES = {
  CREATE_TASK: 'CREATE_TASK',
  TASK_START: 'TASK_START',
  TASK_PAUSE: 'TASK_PAUSE',
  TASK_RESUME: 'TASK_RESUME',
  TASK_CANCEL: 'TASK_CANCEL',
  TASK_COMPLETE: 'TASK_COMPLETE',
  TASK_ERROR: 'TASK_ERROR',
  STATUS_UPDATE: 'STATUS_UPDATE',
  PROGRESS_UPDATE: 'PROGRESS_UPDATE',
  CONFIG_UPDATE: 'CONFIG_UPDATE',
} as const;

// 测试用的事件类型
export const TEST_EVENT_TYPES = {
  CLICK: 'click',
  SCROLL: 'scroll',
  LOAD: 'load',
  ERROR: 'error',
  RESIZE: 'resize',
  FOCUS: 'focus',
  BLUR: 'blur',
  KEYDOWN: 'keydown',
  KEYUP: 'keyup',
  MOUSEOVER: 'mouseover',
  MOUSEOUT: 'mouseout',
} as const;

// 测试用的选择器
export const TEST_SELECTORS = {
  BUTTON: 'button',
  LINK: 'a',
  INPUT: 'input',
  FORM: 'form',
  DIV: 'div',
  SPAN: 'span',
  P: 'p',
  H1: 'h1',
  IMG: 'img',
  BODY: 'body',
  CONTAINER: '.container',
  MODAL: '.modal',
  LOADING: '.loading',
  ERROR: '.error',
  SUCCESS: '.success',
} as const;

// 测试用的CSS类名
export const TEST_CSS_CLASSES = {
  CONTAINER: 'test-container',
  BUTTON: 'test-button',
  INPUT: 'test-input',
  MODAL: 'test-modal',
  LOADING: 'test-loading',
  ERROR: 'test-error',
  SUCCESS: 'test-success',
  HIDDEN: 'test-hidden',
  VISIBLE: 'test-visible',
  ACTIVE: 'test-active',
} as const;

// 测试用的正则表达式
export const TEST_REGEX = {
  URL: /^https?:\/\/.+/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  TIMESTAMP: /^\d{13}$/,
  BASE64: /^[A-Za-z0-9+/]*={0,2}$/,
  JSON: /^\{.*\}$/,
} as const;

// 导出默认配置对象
export const DEFAULT_TEST_CONFIG = {
  timeout: TEST_TIMEOUTS.MEDIUM,
  retries: TEST_RETRY_CONFIG.MAX_RETRIES,
  viewport: TEST_DIMENSIONS.VIEWPORT.DESKTOP,
  userAgent: TEST_USER_AGENTS.TEST,
  headers: TEST_HEADERS.ACCEPT_ALL,
  concurrency: TEST_CONCURRENCY_CONFIG.MEDIUM,
} as const;