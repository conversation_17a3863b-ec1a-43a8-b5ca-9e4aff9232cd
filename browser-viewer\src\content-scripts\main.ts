/**
 * Content Script - 统一内容脚本
 * 负责在目标页面执行任务，并能根据模式（无头/可视化）调整行为。
 */
import { ResourceMonitor } from '@/core/task-manager/ResourceMonitor';
import { MESSAGE_TYPES } from '@/shared/constants';
import type { CaptureOptions, ServiceMessage, ServiceResponse } from '@/shared/types';
import { createLogger, Logger } from '@/shared/utils';
import { MaskController } from '@/ui/mask/MaskController';
import { StrategySelector } from './strategies/StrategySelector';

// 扩展window对象自定义属性的类型定义
declare global {
  interface Window {
    contentScriptInitialized?: boolean;
    contentScriptInstance?: ContentScript;
  }
}

class ContentScript {
  private readonly _logger: Logger;
  private _messageListener: ((message: ServiceMessage<unknown>, sender: chrome.runtime.MessageSender, sendResponse: (response: ServiceResponse<unknown>) => boolean | void) => boolean | void) | null = null;
  private _visibilityChangeListener: (() => void) | null = null;
  
  // 可视化模式下的UI控制器
  private _maskController: MaskController | null = null;
  // 局部性能监控器
  private _localResourceMonitor: ResourceMonitor | null = null;
  // 策略选择器实例
  private readonly _strategySelector: StrategySelector;

  constructor() {
    this._logger = createLogger('ContentScript');
    this._strategySelector = new StrategySelector();
    this._init();
  }

  private _init(): void {
    if (this._messageListener) {
      this._logger.warn('Initialization attempted multiple times.');
      return;
    }
    
    this._messageListener = this._handleMessage.bind(this);
    chrome.runtime.onMessage.addListener(this._messageListener);
    this._logger.info('Content script initialized and message listener attached.');
    
    // 仅从顶层框架发送就绪信号，避免来自 iframe 的垃圾信息。
    if (window.self === window.top) {
      this._sendReadySignal();
    } else {
      this._logger.debug('Skipping READY signal from an iframe.');
    }
  }
  
  private _createServiceResponse<T>(success: boolean, data?: T, error?: string): ServiceResponse<T> {
    return {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      success,
      ...(success ? { data } : { error: error ?? 'An unknown error occurred' }),
    };
  }

  private _sendReadySignal(): void {
    chrome.runtime.sendMessage({ type: MESSAGE_TYPES.CONTENT_SCRIPT_READY })
      .catch((error: Error) => {
        const KnownErrors = [
          'Extension context invalidated',
          'Could not establish connection. Receiving end does not exist.',
          'The message port closed before a response was received.',
          'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received'
        ];
        if (!KnownErrors.some(e => error.message.includes(e))) {
          this._logger.error('Failed to send CONTENT_SCRIPT_READY signal with an unexpected error:', error);
        }
      });
  }

  private _handleMessage(
    message: ServiceMessage<unknown>,
    _sender: chrome.runtime.MessageSender,
    sendResponse: (response: ServiceResponse<unknown>) => void
  ): boolean {
    this._logger.debug('Received message:', message.type, message.payload);

    let isAsync = false;

    try {
      switch (message.type) {
        case MESSAGE_TYPES.ACTIVATE_AGENT:
          this._maskController ??= new MaskController();
          if (document.visibilityState === 'visible') this._maskController.show();
          if (!this._visibilityChangeListener) {
            this._visibilityChangeListener = () => {
              if (document.visibilityState === 'visible') this._maskController?.show();
              else this._maskController?.hide();
            };
            document.addEventListener('visibilitychange', this._visibilityChangeListener);
          }
          break;

        case MESSAGE_TYPES.DEACTIVATE_AGENT:
          this._maskController?.hide();
          if (this._visibilityChangeListener) {
            document.removeEventListener('visibilitychange', this._visibilityChangeListener);
            this._visibilityChangeListener = null;
          }
          this._maskController?.destroy();
          this._maskController = null;
          break;

        case MESSAGE_TYPES.VISUAL_TASK_START_MONITORING:
          if (!this._localResourceMonitor) {
            this._localResourceMonitor = new ResourceMonitor();
            this._localResourceMonitor.start();
          }
          sendResponse(this._createServiceResponse(true));
          break;

        case MESSAGE_TYPES.VISUAL_TASK_STOP_MONITORING:
          if (this._localResourceMonitor) {
            this._localResourceMonitor.stop();
            this._localResourceMonitor = null;
          }
          sendResponse(this._createServiceResponse(true));
          break;

        case MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN: {
          isAsync = true;
          const payload = message.payload as { taskId?: string; sessionId?: string; captureOptions?: CaptureOptions };
          if (typeof payload.taskId !== 'string' || typeof payload.sessionId !== 'string') {
            const error = 'Missing or invalid taskId/sessionId for SIMULATE_FULL_PAGE_SCAN';
            this._logger.error(error);
            sendResponse(this._createServiceResponse(false, { status: 'scan_aborted' }, error));
            break;
          }
          const { taskId, sessionId } = payload;
          
          sendResponse(this._createServiceResponse(true, { status: 'scan_initiated' }));
          
          void this._dispatchExtractionStrategy(taskId, sessionId);
          break;
        }
          
        default:
          this._logger.warn(`Unhandled message type in content script: ${message.type}`);
          break;
      }
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this._logger.error(`Critical unhandled error in handleMessage for type ${message.type}:`, errorMessage);
    }
    
    return isAsync;
  }

  private async _dispatchExtractionStrategy(taskId: string, sessionId: string): Promise<void> {
    try {
      // 使用实例化的选择器
      const { strategy, messageBuilder } = this._strategySelector.select(window.location.href);
      this._logger.info(`Selected extraction strategy: ${strategy.constructor.name}`);

      const result = await strategy.execute(window, taskId, sessionId);
      
      // 使用与策略关联的消息构建器来创建消息
      const message = messageBuilder({
        taskId,
        sessionId,
        result,
      });

      await chrome.runtime.sendMessage(message);

    } catch (error) {
      this._logger.error('Extraction strategy execution failed:', error);
      await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED, // 失败时总是发送默认的完成消息
        payload: {
          taskId,
          sessionId,
          result: { success: false, error: error instanceof Error ? error.message : String(error) }
        },
      });
    }
  }

  public destroy(): void {
    if (this._messageListener) {
      chrome.runtime.onMessage.removeListener(this._messageListener);
      this._messageListener = null;
      this._logger.info('Message listener detached.');
    }
    this._maskController?.destroy();
    this._maskController = null;
    if (this._visibilityChangeListener) {
      document.removeEventListener('visibilitychange', this._visibilityChangeListener);
      this._visibilityChangeListener = null;
    }
    if (this._localResourceMonitor) {
      this._localResourceMonitor.stop();
      this._localResourceMonitor = null;
      this._logger.info('Local resource monitor stopped.');
    }
  }
}

if (!window.contentScriptInitialized) {
  window.contentScriptInitialized = true;
  window.contentScriptInstance = new ContentScript();
}