# 数懒孪生代理 - 浏览器智能代理插件技术解决方案

<!--
文档元数据 (供AI助手快速查阅，完整目录中每个章节后的 `[L行号]` 标记可帮助快速定位文档内容位置)
- 文档类型: 技术架构方案
- 版本: v1.0
- 最后更新: 2025年7月2日
- 目标读者: 开发人员、架构师、AI助手
- 技术栈: TypeScript, Vite, Manifest V3, RxJS, Web Workers
- 核心功能: 智能浏览代理、统一可视化执行、异常处理、性能优化
-->

<div align="center">

**🤖 智能浏览代理 | 🚀 企业级架构 | 🔧 统一可视化执行**

[![文档版本](https://img.shields.io/badge/文档版本-v1.0-blue.svg)](#) [![技术栈](https://img.shields.io/badge/技术栈-TypeScript%2BVite-green.svg)](#12-技术栈) [![架构模式](https://img.shields.io/badge/架构模式-微服务-orange.svg)](#3-微服务模块设计)

[📖 完整文档](#完整目录) | [🏗️ 架构图](#21-系统架构图) | [🚀 快速开始](#阶段1-基础架构-2周) | [💡 核心特性](#12-技术栈)

</div>

---

## 📋 完整目录

### 🎯 [1. 项目概述](#1-项目概述) `[L111-L134]`

- [1.1 项目目标](#11-项目目标) `[L113-L123]`
- [1.2 技术栈](#12-技术栈) `[L125-L134]`

### 🏗️ [2. 完整项目架构](#2-完整项目架构) `[L138-L212]`

- [2.1 系统架构图](#21-系统架构图) `[L140-L176]`
- [2.2 目录结构](#22-目录结构) `[L178-L212]`

### ⚙️ [3. 微服务模块设计](#3-微服务模块设计) `[L216-L541]`

- [3.1 任务调度中心 (Task Manager)](#31-任务调度中心-task-manager) `[L218-L269]`
- [3.2 行为模拟引擎 (Behavior Engine)](#32-行为模拟引擎-behavior-engine) `[L271-L329]`
  - [3.2.1 页面完成度检测机制](#321-页面完成度检测机制) `[L331-L415]`
- [3.3 数据提取系统 (Data Extraction System)](#33-数据提取系统-data-extraction-system) `[L417-L486]`
- [3.4 视觉服务 (Vision Service)](#34-视觉服务-vision-service) `[L488-L523]`
- [3.5 异常服务 (Exception Service)](#35-异常服务-exception-service) `[L525-L541]`
- [3.6 内容脚本管理与安全注入](#36-内容脚本管理与安全注入) `[L543-L585]`

### 🖥️ [4. 统一可视化执行模型](#4-统一可视化执行模型) `[L587-L774]`

- [4.1 实现机制](#41-实现机制) `[L625-L633]`
- [4.2 智能遮罩系统](#42-智能遮罩系统) `[L634-L650]`
- [4.3 后台任务健壮性](#43-后台任务健壮性) `[L652-L685]`

### 🚀 [5. 非阻塞并发浏览代理设计](#5-非阻塞并发浏览代理设计) `[L736-L933]`

- [5.1 中心化并发控制架构](#51-中心化并发控制架构) `[L738-L765]`
- [5.2 动态并发与流水线机制](#52-动态并发与流水线机制) `[L767-L785]`
- [5.3 高性能数据处理](#53-高性能数据处理) `[L787-L864]`
- [5.4 全局计算资源池 (Shared Worker Pool)](#54-全局计算资源池-shared-worker-pool) `[L866-L933]`

### ⚠️ [6. 异常处理与恢复系统](#6-异常处理与恢复系统) `[L937-L1029]`

- [6.1 异常检测流程](#61-异常检测流程) `[L939-L963]`
- [6.2 状态快照与恢复](#62-状态快照与恢复) `[L965-L1014]`
- [6.3 任务持久化与恢复](#63-任务持久化与恢复) `[L1016-L1029]`

### ⚡ [7. 性能优化体系](#7-性能优化体系) `[L1033-L1151]`

- [7.1 三级优化策略](#71-三级优化策略) `[L1035-L1041]`
- [7.2 关键性能指标](#72-关键性能指标) `[L1043-L1053]`
- [7.3 自适应节流算法](#73-自适应节流算法) `[L1055-L1080]`
- [7.4 异步I/O解耦原则](#74-异步io解耦原则) `[L1082-L1096]`
- [7.5 分层性能监控架构](#75-分层性能监控架构) `[L1098-L1151]`

### 🔒 [8. 安全与隐私保护](#8-安全与隐私保护) `[L1155-L1212]`

- [8.1 数据安全策略](#81-数据安全策略) `[L1157-L1183]`
- [8.2 隐私保护机制](#82-隐私保护机制) `[L1185-L1212]`

### ✅ [9. 代码质量与测试](#9-代码质量与测试) `[L1216-L1268]`

- [9.1 TypeScript严格模式配置](#91-typescript严格模式配置) `[L1218-L1231]`
- [9.2 ESLint规则与代码质量](#92-eslint规则与代码质量) `[L1233-L1240]`
- [9.3 错误处理最佳实践](#93-错误处理最佳实践) `[L1242-L1260]`
- [9.4 测试策略](#94-测试策略) `[L1262-L1268]`

### 🚀 [10. 部署与监控](#10-部署与监控) `[L1272-L1339]`

- [10.1 CI/CD流程](#101-cicd流程) `[L1274-L1286]`
- [10.2 监控指标](#102-监控指标) `[L1288-L1297]`
- [10.3 开发环境与浏览器兼容性](#103-开发环境与浏览器兼容性) `[L1299-L1339]`

### 🔄 [11. 工作流模式 (Workflow Modes)](#11-工作流模式-workflow-modes) `[L1343-L1430]`

- [11.1 开发调试模式 (Development & Debugging Mode)](#111-开发调试模式-development--debugging-mode) `[L1349-L1409]`
- [11.2 生产运行模式 (Production Mode)](#112-生产运行模式-production-mode) `[L1411-L1430]`

### 📅 [12. 实施路线图](#12-实施路线图) `[L1434-L1459]`

### 📝 [技术方案总结](#📝-技术方案总结) `[L1463-L1527]`

---

### 🔍 **快速导航提示**

- 🎯 **核心概念**: [项目目标 `L113`](#11-项目目标) → [系统架构 `L140`](#21-系统架构图) → [技术栈 `L125`](#12-技术栈)
- 🏗️ **架构设计**: [微服务设计 `L216`](#3-微服务模块设计) → [并发控制 `L738`](#5-非阻塞并发浏览代理设计) → [性能优化 `L1033`](#7-性能优化体系)
- 🔧 **实现细节**: [任务调度 `L218`](#31-任务调度中心-task-manager) → [行为引擎 `L271`](#32-行为模拟引擎-behavior-engine) → [视觉处理 `L417`](#33-视觉处理系统-capture-system)
- 🚀 **部署运维**: [测试策略 `L1216`](#9-代码质量与测试) → [部署监控 `L1272`](#10-部署与监控) → [实施路线 `L1434`](#12-实施路线图)

---

## 1. 项目概述

### 1.1 项目目标

开发一个高性能浏览器插件，在用户授权下以真实浏览行为代理执行多页面任务，具备：

- 人类行为模拟（滚动、点击、分心）
- 对主流站点的索引页和详情页精准调优采集策略
- 统一的、带智能遮罩的可视化执行环境
- 异常实时检测与恢复
- 资源感知型任务调度

### 1.2 技术栈

- **核心框架**: TypeScript + Vite （区分开发和生产的sourcemap配置）
- **浏览器标准**: Manifest V3
- **并发模型**: Web Workers + Async Hooks
- **状态管理**: RxJS
- **数据存储**: IndexedDB + chrome.storage
- **UI框架**: Vue 3 + Tailwind CSS

---

## 2. 完整项目架构

### 2.1 系统架构图

```mermaid
graph TD
    subgraph A1["高层：批处理与策略"]
        UserUI[用户界面/任务配置] --> BatchProcessor[批处理器]
        BatchProcessor --> PriorityQueue[任务优先级队列]
    end
    
    subgraph B1["底层：调度与执行"]
        subgraph TS1["TaskScheduler - 中心调度器"]
            ResourceMonitor[资源监控器] --> DynamicControl[动态并发控制]
            ExceptionDetector[异常检测器] --> DynamicControl
            DynamicControl --> AvailableSlots[可用槽位信号量]
        end

        PriorityQueue --> RequestSlot{请求槽位}
        RequestSlot --> AvailableSlots
        AvailableSlots -->|槽位可用| CreateTask[创建Task实例]

        subgraph TaskExecution["并发任务执行"]
            CreateTask --> Task1[并发任务1: 行为模拟+内容捕获]
            CreateTask --> Task2[并发任务2: 行为模拟+内容捕获]
            CreateTask --> TaskN[更多并发任务...]
        end

        Task1 -->|完成| ReleaseSlot[释放槽位]
        Task2 -->|完成| ReleaseSlot
        TaskN -->|完成| ReleaseSlot
        ReleaseSlot --> AvailableSlots
    end
    
    Task1 --> AIService[多模态AI服务]
    Task2 --> AIService
```

### 2.2 目录结构

```tree
browser-viewer/
├── dist/                       # 构建输出
├── public/                     # 公共素材
├── scripts/                    # 构建脚本
├── src/                        # 源代码
│   ├── background/               # Service Worker
│   ├── content-scripts/          # 内容脚本
│   ├── core/                     # 核心引擎
│   │   ├── task-manager/           # 任务调度中心
│   │   │   ├── interfaces/                 # 服务接口定义
│   │   │   ├── services/                   # 微服务实现
│   │   │   │   ├── SlotManagerImpl.ts          # 槽位管理服务
│   │   │   │   ├── TaskStateManagerImpl.ts     # 任务状态管理
│   │   │   │   ├── UnifiedTaskStateManager.ts  # 统一状态管理
│   │   │   │   ├── TaskQueueServiceImpl.ts     # 任务队列服务
│   │   │   │   ├── ServiceContainer.ts         # 依赖注入容器
│   │   │   │   └── ServiceRegistration.ts      # 服务注册配置
│   │   │   ├── TaskSchedulerRefactored.ts  # 重构后的主调度器
│   │   │   └── TaskSchedulerFactory.ts     # 工厂模式入口
│   │   ├── state-management/       # 统一状态管理
│   │   │   └── UnifiedStateStore.ts        # 事务性状态存储
│   │   ├── messaging/              # 类型安全消息系统
│   │   │   └── TypedMessenger.ts           # 类型安全消息传递
│   │   ├── exception-handling/     # 智能异常处理
│   │   │   └── EnhancedExceptionHandler.ts # 智能异常处理器
│   │   ├── behavior-engine/        # 行为模拟
│   │   └── capture-system/         # 视觉处理
│   ├── services/                 # 微服务层
│   │   ├── vision-service/         # 视觉服务
│   │   ├── auth-service/           # 认证服务
│   │   └── exception-service/      # 异常服务
│   ├── shared/                   # 共享资源
│   │   ├── types/                  # 类型定义
│   │   ├── constants/              # 常量
│   │   └── utils/                  # 工具函数
│   ├── ui/                       # 用户界面
│   │   ├── popup/                  # 弹出页面
│   │   └── options/                # 设置页面
│   └── workers/                  # Web Workers
├── tests/                      # 测试套件
├── eslint.config.js            # 代码风格
├── manifest.json               # 插件清单
├── package.json                # 依赖版本
├── tailwind.config.ts          # 样式配置
├── tsconfig.json               # 开发配置
├── vite.config.ts              # 构建配置
└── vitest.config.ts            # 测试配置
```

---

## 3. 核心模块

### 3.1 任务调度中心

**核心架构**:

任务调度中心采用**基于并发槽位的中心化架构**。`TaskScheduler` 作为核心调度器，统一管理批处理、并发控制和任务调度。

- **`TaskScheduler` (统一调度器)**:
  - **批处理管理**: 用户提交的URL组被标记为批处理任务，生成唯一的`batchId`标识符。
  - **槽位管理**: 采用按需创建和统一销毁策略。插件启动时不创建标签页，任务提交时才创建执行槽位。任务完成后自动清理资源。
  - **任务队列**: 批处理请求被拆解为单个URL任务，统一推入待处理队列。
  - **智能调度**: 监控槽位状态和任务队列，空闲槽位立即分配新任务。

**功能与实现细节**:

1. **智能任务调度**:
    - **同源亲和性**: 优先分配相同域名的任务到同一槽位，充分利用浏览器缓存
    - **优先级调度**: 无同源任务时按优先级分配，确保重要任务优先执行
    - **性能优化**: 减少页面加载时间，提升整体执行效率

2. **流水线调度**:
    - **即时调度**: 任务完成立即释放槽位并分配新任务
    - **高吞吐量**: 确保所有槽位持续工作，最大化并发效率

3. **内存管理**:
    - **批次清理**: 按批处理单位管理任务历史，防止内存泄漏
    - **资源释放**: 完成任务自动清理截图数据和相关资源
    - **限制机制**: 设置最大保留批次数，确保长期运行稳定性

4. **状态持久化**:
    - **队列保存**: 任务配置队列加密存储到本地存储
    - **自动恢复**: 插件重启后自动恢复待处理任务
    - **健壮设计**: 只持久化配置信息，避免复杂运行时状态管理

5. **动态并发控制**:
    - **资源监控**: 按需启动性能监控，避免空闲时资源消耗
    - **自适应调整**: 根据CPU、内存负载动态调整并发槽位数量
    - **平滑扩缩容**: 扩容时立即创建槽位，缩容时等待任务完成后安全关闭
    - **协同节流**: 高负载时同步调整截图质量和模拟速度

**接口**:

```typescript
// 统一调度器接口
interface TaskScheduler {
  submitBatch(configs: TaskConfig[]): void;
  pauseTask(taskId: TaskId): Promise<void>;
  resumeTask(taskId: TaskId): Promise<void>;
  cancelTask(taskId: TaskId): void;
  getTaskMetrics(taskId: TaskId): TaskMetrics | null;
  getTask(taskId: TaskId): Task | undefined;
  getAllTasks(): TaskMetrics[];
  getRunningTasksCount(): number;
  shutdown(): Promise<void>;
  startAllPendingTasks(): Promise<void>;
  pauseAllTasks(): Promise<void>;
  clearAllTasks(): Promise<void>;
  startSelectedTasks(batchIds: string[]): Promise<void>;
  pauseSelectedTasks(batchIds: string[]): Promise<void>;
  clearSelectedTasks(batchIds: string[]): Promise<void>;
}

// 任务配置接口 (无变化)
interface TaskConfig {
  urls: string[];
  behaviorProfile: 'stealth' | 'normal' | 'aggressive';
  outputFormat: 'text' | 'markdown' | 'json';
  priority?: number; // 任务优先级
}

// 微服务架构实现
class TaskScheduler implements TaskScheduler {
  constructor(container: ServiceContainer) {
    // 从依赖注入容器获取所有服务
    this._slotManager = container.get<SlotManager>(SERVICE_TOKENS.SLOT_MANAGER);
    this._taskStateManager = container.get<TaskStateManager>(SERVICE_TOKENS.TASK_STATE_MANAGER);
    this._taskQueueService = container.get<TaskQueueService>(SERVICE_TOKENS.TASK_QUEUE_SERVICE);
    this._taskControlService = container.get<TaskControlService>(SERVICE_TOKENS.TASK_CONTROL_SERVICE);
    this._taskSubmissionService = container.get<TaskSubmissionService>(SERVICE_TOKENS.TASK_SUBMISSION_SERVICE);
    this._taskQueryService = container.get<TaskQueryService>(SERVICE_TOKENS.TASK_QUERY_SERVICE);
    this._batchOperationService = container.get<BatchOperationService>(SERVICE_TOKENS.BATCH_OPERATION_SERVICE);
    this._resourceMonitor = container.get<ResourceMonitor>(SERVICE_TOKENS.RESOURCE_MONITOR);
    this._adaptiveThrottler = container.get<AdaptiveThrottler>(SERVICE_TOKENS.ADAPTIVE_THROTTLER);
    this._typedMessenger = container.get<TypedMessenger>(SERVICE_TOKENS.TYPED_MESSENGER);
    this._enhancedExceptionHandler = container.get<EnhancedExceptionHandler>(SERVICE_TOKENS.ENHANCED_EXCEPTION_HANDLER);
  }
}

// 服务注册配置示例
export function registerTaskManagementServices(container: ServiceContainer): void {
  // 槽位管理器 - 独立的槽位生命周期管理
  container.registerSingleton(SERVICE_TOKENS.SLOT_MANAGER, (c) => 
    new SlotManagerImpl(c.get(SERVICE_TOKENS.WORKER_WINDOW_MANAGER))
  );
  
  // 任务状态管理器 - 基于统一状态存储
  container.registerSingleton(SERVICE_TOKENS.TASK_STATE_MANAGER, (c) => 
    new UnifiedTaskStateManager(c.get(SERVICE_TOKENS.UNIFIED_STATE_STORE))
  );
  
  // 任务队列服务 - 专业的任务排队和调度
  container.registerSingleton(SERVICE_TOKENS.TASK_QUEUE_SERVICE, () => 
    new TaskQueueServiceImpl()
  );
  
  // 任务控制服务 - 集中的任务操作接口
  container.registerSingleton(SERVICE_TOKENS.TASK_CONTROL_SERVICE, (c) => 
    new TaskControlServiceImpl(c.get(SERVICE_TOKENS.SLOT_MANAGER))
  );
}
```

#### 微服务架构设计

1. **核心职责分离** - 基于单一职责原则

   ```typescript
   // 槽位生命周期管理
   SlotManager: 独立管理槽位创建、分配、释放
   
   // 任务状态管理  
   TaskStateManager: 统一的任务指标存储和查询
   
   // 任务队列管理
   TaskQueueService: 专业的任务排队和调度算法
   
   // 任务控制接口
   TaskControlService: 集中的任务操作接口（暂停、恢复、取消）
   ```

2. **依赖注入架构** - 消除硬编码依赖

   ```typescript
   // 服务容器管理所有依赖关系
   class ServiceContainerImpl implements ServiceContainer {
     register<T>(token: ServiceToken, registration: ServiceRegistration<T>): void;
     get<T>(token: ServiceToken): T;
     createChild(): ServiceContainer;
   }
   
   // 工厂模式支持渐进式迁移
   export function createTaskScheduler(
     workerWindowManager: WorkerWindowManager,
     options: TaskSchedulerFactoryOptions = {}
   ): TaskScheduler {
     const { useRefactoredImplementation = true } = options;
     
     if (useRefactoredImplementation) {
       const container = createConfiguredContainer(workerWindowManager);
       return container.get<TaskScheduler>(SERVICE_TOKENS.TASK_SCHEDULER);
     } else {
       return new TaskSchedulerImpl(workerWindowManager); // 原有实现
     }
   }
   ```

3. **统一状态管理** - 企业级状态管理

   ```typescript
   // 事务性状态操作
   class UnifiedStateStore implements StateStore {
     async withTransaction<T>(operation: (ctx: TransactionContext) => Promise<T>): Promise<T> {
       const context = new TransactionContextImpl(this, this._config.transactionTimeout);
       try {
         const result = await operation(context);
         await context.commit();
         return result;
       } catch (error) {
         if (!context.isCompleted()) {
           await context.rollback();
         }
         throw error;
       }
     }
   }
   
   // 使用示例：事务性状态更新
   await stateStore.withTransaction(async (ctx) => {
     ctx.save('task_metrics', updatedMetrics);
     ctx.save('task_state', newState);
     // 自动提交或回滚
   });
   ```

4. **类型安全消息系统** - 编译时类型检查

   ```typescript
   // 预定义消息注册表
   interface MessageRegistry {
     'TASK_STATUS_CHANGED': {
       taskId: TaskId;
       oldStatus: string;
       newStatus: string;
       timestamp: number;
     };
     'USER_ACTION': {
       action: 'start' | 'pause' | 'stop' | 'clear';
       target?: 'all' | 'selected' | TaskId;
       batchIds?: string[];
     };
   }
   
   // 类型安全的消息传递
   class TypedMessenger {
     async send<K extends keyof MessageRegistry>(
       type: K,
       payload: MessageRegistry[K],
       target?: 'popup' | 'background' | 'content' | 'all'
     ): Promise<void> {
       // 编译时类型检查 + 运行时验证
       const validator = this._validators.get(type);
       if (validator && !validator.validate(payload).isValid) {
         throw new Error('消息验证失败');
       }
       // 发送消息...
     }
   }
   ```

5. **智能异常处理** - 企业级异常恢复

   ```typescript
   // 分级异常处理
   class EnhancedExceptionHandler {
     async handleException(
       error: Error | string,
       context: ExceptionContext,
       type?: ErrorType,
       severity?: ErrorSeverity
     ): Promise<RecoveryResult> {
       const exception = this._createException(error, context, type, severity);
       
       // 查找合适的处理器
       const handler = this._findHandler(exception);
       const result = await handler.handle(exception);
       
       // 根据恢复动作执行相应操作
       switch (result.action) {
         case RecoveryAction.RETRY:
           return { action: 'retry', success: true, retryAfter: 2000 };
         case RecoveryAction.SKIP:
           return { action: 'skip', success: true };
         case RecoveryAction.ABORT:
           return { action: 'abort', success: false };
       }
     }
   }
   
   // 专业化处理器
   class NetworkExceptionHandler implements ExceptionHandler {
     canHandle(exception: Exception): boolean {
       return exception.type === ErrorType.NETWORK || exception.type === ErrorType.TIMEOUT;
     }
   }
   ```

**架构优势**:

- **可维护性**: 职责分离和接口抽象显著提升代码质量
- **可测试性**: 依赖注入使单元测试变得简单可靠  
- **可扩展性**: 插件化架构支持功能模块化扩展
- **可靠性**: 事务性状态管理和智能异常处理
- **类型安全**: TypeScript严格模式提供编译时类型检查

### 3.2 行为模拟引擎

**功能**:

- 生成人类滚动轨迹（贝塞尔曲线模拟）
- 智能元素点击：作为分心行为的一部分，在页面非交互区域进行随机点击。通过执行脚本查找并点击"安全"元素（如`div`、`p`等），并智能规避会触发页面跳转或弹窗的交互式元素（如`a`、`button`）。
- 操作随机化和分心行为（间隔时间、轨迹偏移）
- 通过遮罩层提供实时视觉反馈

**与视觉反馈系统的集成**:

`HumanBehaviorSimulator` 被设计为可与一个可选的 `MaskController` 实例集成，以提供强大的视觉反馈能力。

- **依赖注入**: 它的构造函数可以**可选地接收一个 `MaskController` 实例**。
- **执行逻辑**:
  - `Task` 对象通过消息驱动内容脚本 `main.ts`。
  - 内容脚本会同时实例化 `MaskController` 和 `HumanBehaviorSimulator`，并将前者注入后者的构造函数中。
  - 这样，`HumanBehaviorSimulator` 在执行操作时（如滚动、点击），就能调用 `MaskController` 的方法来实时更新蒙版上的高亮区域和鼠标指针位置，为用户提供清晰、直观的操作反馈。

这种设计使得核心的模拟逻辑可以被复用，同时将与UI反馈的交互完全解耦。

```typescript
// browser-viewer/src/core/behavior-engine/HumanBehaviorSimulator.ts
export type ExecutionContext = Window;

class HumanBehaviorSimulator {
  private maskController?: MaskController;

  constructor(
    profile: keyof typeof BEHAVIOR_PROFILES = 'normal',
    maskController?: MaskController
  ) {
    this.maskController = maskController;
    // ...
  }
  
  async simulateScroll(context: ExecutionContext, options: ScrollOptions): Promise<void> {
    // ... 滚动逻辑 ...
    // 在每一帧滚动时，都更新蒙版的高亮区域以匹配视口
    this.maskController?.updateHighlight({
      x: 0, 
      y: context.scrollY,
      width: context.innerWidth,
      height: context.innerHeight
    });
    // ...
  }

  async simulateClick(context: ExecutionContext, target: Point): Promise<void> {
    // ...
    // 在点击前，将鼠标位置（视觉反馈）移动到目标点
    this.maskController?.updateMousePosition(target);
    const element = context.document.elementFromPoint(target.x, target.y);
    if (element) {
      (element as HTMLElement).click();
    }
  }
}
```

### 3.2.1 页面完成度检测机制

**目标**: 确保在任务结束前，页面所有动态内容（包括懒加载和无限滚动）均已加载完毕，标志着该URL的浏览任务已"消费"完全。此过程与视觉系统的持续捕捉紧密集成。

**触发**: 页面导航成功后，由 `Task` 的 `processUrl` 方法通过发送 `SIMULATE_FULL_PAGE_SCAN` 消息，驱动内容脚本 (`main.ts`) 执行页面扫描。

**具体流程**:

1. **启动扫描与捕捉会话**:
   - `Task.processUrl()` 为当前URL创建专属的捕捉会话：`sessionId = captureSystem.startSession(taskId, tabId)`
   - 建立URL与会话ID的映射关系：`urlSessionMap.set(url, sessionId)`

2. **消息驱动的页面扫描**:
   - 通过 `_sendMessageToTab()` 发送 `SIMULATE_FULL_PAGE_SCAN` 消息给内容脚本
   - 内容脚本使用 `StrategySelector` 根据URL智能选择提取策略：
     - `DefaultScreenshotStrategy`: 通用的完整页面截图策略
     - `JdProductStrategy`: 针对京东商品页的结构化数据提取策略
     - 可扩展支持更多网站特定策略
   - 默认策略执行 `performFullPageScan()` 进行完整页面扫描

3. **分段滚动与捕捉**:
   - **技术参数**: 每次滚动视口高度的85%（保留15%重叠，基于实际测试优化）
   - **滚动实现**: 使用 `window.scrollTo({ behavior: 'smooth' })` 进行平滑滚动
   - **等待时间**: 基准500ms，通过 `addRandomness(400, 0.5)` 添加随机性
   - **捕捉触发**: 每次滚动后调用 `requestViewportCapture(taskId)` 触发截图

4. **高度检测与稳定性判断**:
   - **监控对象**: `document.body.scrollHeight`
   - **稳定阈值**: 连续4次高度检测无变化（`STABILITY_THRESHOLD = 4`）
   - **重置机制**: 高度变化时重置计数器并额外触发一次捕捉

5. **结束条件**:
   - 到达页面底部：`currentScrollY + viewportHeight >= totalHeight - 5`（5px缓冲区）
   - 或达到稳定阈值：连续4次高度检测稳定

6. **会话终结**:
   - 调用 `captureSystem.endSession(sessionId)` 完成内容拼接和去重
   - 返回最终的完整页面文本内容

**统一实现**:
为确保行为一致性，页面扫描的核心逻辑被完全封装在 `HumanBehaviorSimulator` 的 `simulateFullPageScan` 方法中。内容脚本在收到指令后，会实例化一个 `HumanBehaviorSimulator` 并调用此方法。这种设计保证了使用完全相同的滚动策略和页面稳定性检测算法。

`simulateFullPageScan` 方法的内部实现包含：

- **分段滚动与捕捉**: 每次滚动视口高度的85%（保留15%重叠），滚动后调用一个由内容脚本传入的异步回调函数 (`requestViewportCapture`) 来触发截图。
- **高度检测与稳定性判断**: 持续监控 `document.body.scrollHeight`，当连续4次（`STABILITY_THRESHOLD`）高度检测无变化时，认为页面内容已加载稳定。
- **结束条件**: 到达页面底部或达到稳定阈值。

**关键代码实现 (封装于 HumanBehaviorSimulator.ts)**:

```typescript
// browser-viewer/src/core/behavior-engine/HumanBehaviorSimulator.ts

async simulateFullPageScan(
  context: ExecutionContext,
  // 由内容脚本注入的回调，负责触发截图
  captureAndProcessViewport: () => Promise<void> 
): Promise<void> {
  if (!this.isActive) {
    throw new Error('Behavior simulator is not active. Call start() first.');
  }
  
  let lastHeight = context.document.body.scrollHeight;
  let stableCount = 0;
  const STABILITY_THRESHOLD = 4;
  const SCROLL_DELAY = 500;
  
  // 初始捕获
  await captureAndProcessViewport();
  
  while (true) {
    const { scrollY: currentScrollY, innerHeight: viewportHeight, scrollHeight: totalHeight } = this.getPageDimensions(context);
    
    // 检查是否到达底部
    if (currentScrollY + viewportHeight >= totalHeight - 5) break;
    
    // 滚动并等待
    const scrollAmount = viewportHeight * 0.95;
    await this.simulateScroll(context, { startY: currentScrollY, endY: currentScrollY + scrollAmount, duration: SCROLL_DELAY });
    
    // 触发截图
    await captureAndProcessViewport();
    
    // 高度稳定性检测
    const newHeight = context.document.body.scrollHeight;
    if (newHeight === lastHeight) {
      stableCount++;
    } else {
      stableCount = 0;
      lastHeight = newHeight;
      await captureAndProcessViewport(); // 高度变化，额外触发一次捕捉
    }
    
    if (stableCount >= STABILITY_THRESHOLD) break;
  }
}
```

### 3.3 视觉处理系统

**核心思想演进**:

为了从根本上解决性能瓶颈、浏览器API速率限制以及复杂网站（如京东商品页）的截图挑战，我们采用一种智能、高效、可扩展性极强的**"基于策略的自适应数据提取"**架构。

这种架构的核心是**策略模式 (Strategy Pattern)**。系统不假设所有页面都需要被截图，而是基于不同网站结构和最优提取方式的差异性。系统能够根据页面URL，动态选择最合适的提取策略来执行任务。

**架构图：策略选择与执行流程**:

```mermaid
graph TD
    subgraph A["Content Script: main.ts"]
        Start[任务启动] --> GetURL[获取页面URL]
        GetURL --> StrategySelector[策略选择器<br/>StrategySelector.select(url)]
        StrategySelector -->|URL匹配 "item.jd.com"| JdStrategy("京东商品页策略<br/>JdProductStrategy")
        StrategySelector -->|默认/不匹配| DefaultStrategy("默认截图策略<br/>DefaultScreenshotStrategy")
    end
    
    subgraph B["Extraction Strategies"]
        JdStrategy -->|执行 .extract()| ParseDOM[解析DOM<br/>获取价格、标题、图片URL]
        DefaultStrategy -->|执行 .extract()| FullPageScan[滚动并调用 domToPng<br/>(原视觉处理逻辑)]
    end

    subgraph C["Data Transmission"]
        ParseDOM --> StructuredData{封装为结构化数据<br/>{ type: 'product', data: {...} }}
        FullPageScan --> ImageSlices[生成图片切片<br/>dataUrl 字符串]
        StructuredData --> |发送 STRUCTURED_DATA_CAPTURED 消息| ServiceWorker
        ImageSlices --> |发送 VIEWPORT_CAPTURED 消息| ServiceWorker
    end
```

**核心组件**:

1. **`ExtractionStrategy` (提取策略接口)**:
    - 定义了一个所有具体策略都必须实现的统一契约，即 `extract()` 方法。
    - 这确保了上层调用者（`main.ts`）可以用同样的方式与任何策略进行交互，无需关心其内部复杂的实现细节。

2. **`StrategySelector` (策略选择器)**:
    - 这是新架构的"交通枢纽"。它的职责是接收一个URL，并根据预定义的规则返回最适合该URL的策略实例。
    - **实际实现**: 采用**基于RegExp数组映射的模式**，比简单的Map模式更加灵活，支持正则表达式模式匹配，能够处理复杂的URL匹配规则。

    ```typescript
    // 实际实现：基于正则表达式的策略匹配
    private readonly strategies: Array<{
      pattern: RegExp;
      strategyFactory: () => ExtractionStrategy;
    }> = [
      {
        pattern: /item\.jd\.com/,
        strategyFactory: () => new JdProductStrategy()
      },
      // 未来可添加更多复杂匹配规则，如多域名、路径匹配等
    ];

    select(url: string): ExtractionStrategy {
      for (const { pattern, strategyFactory } of this.strategies) {
        if (pattern.test(url)) {
          return strategyFactory();
        }
      }
      return new DefaultScreenshotStrategy(); // 默认策略
    }
    ```

3. **`JdProductStrategy` (京东商品页策略)**:
    - **职责**: 专为京东商品详情页优化。它完全绕开了高成本的截图操作。
    - **实现**:
        1. 通过 `simulateFullPageScan` 精确模拟用户滚动，确保所有懒加载的"商品详情"图片都完全加载出来。
        2. 直接操作DOM，精准抓取页面顶部的商品标题、价格。
        3. 遍历详情区域，收集所有已加载图片的URL。
        4. 将这些信息组合成一个结构化的JSON对象。
        5. 通过 `STRUCTURED_DATA_CAPTURED` 消息类型，将这个JSON对象发送回后台Service Worker。

4. **`DefaultScreenshotStrategy` (默认截图策略)**:
    - **职责**: 作为保底方案，确保插件在遇到未知或非特定优化的网站时，依然能够工作。
    - **实现**: 它完整地封装了**旧的、基于 `modern-screenshot` 库进行全页滚动截图的全部逻辑**。其输出是图片切片的 `dataUrl` 字符串，通过 `VIEWPORT_CAPTURED` 消息发送回后台，与旧流程完全兼容。

**对系统其余部分的影响与协同**:

- **内容脚本 (`content-scripts/main.ts`)**: 是一个轻量级的引导程序：初始化策略选择器，根据当前URL选定策略，然后调用其 `extract()` 方法。它不关心提取的具体过程。
- **消息总线 (`shared/constants/index.ts`)**: 通信协议得到扩展，新增了 `STRUCTURED_DATA_CAPTURED` 消息类型，使得内容脚本可以直接向后台传递解析好的JSON数据，而不仅仅是图片。
- **后台服务 (`service-worker.ts`)**: `Task` 处理两种类型的返回数据。收到图片时，它将其送往视觉服务（Vision Service）进行AI分析；收到结构化数据时，它可以跳过AI分析，直接进行存储或后续处理，极大地提升了效率和处理速度，并节省了AI调用成本。
- **性能优化 (`7. 性能优化体系`)**: 这是对性能优化体系的重大贡献。我们将在文档的性能优化章节中明确补充这一条："**实施针对特定站点的提取策略，是避免不必要性能开销的最高优先级优化手段**"。

这种策略模式架构使插件具备了智能化和高效性。它为快速适配更多主流平台、提供精准数据提取服务奠定了坚实、可扩展的基础。

### 3.4 视觉处理服务

**功能**:

- 多模态AI路由
- 结果缓存与去重
- 内容后处理
- 流式内容拼接

**AI路由逻辑**:

```mermaid
graph LR
    ImageBlock[图像分块] --> ContentAnalysis{内容分析}
    ContentAnalysis -->|文本为主| OCRModel[OCR识别模型]
    ContentAnalysis -->|图形为主| ObjectDetection[目标检测模型]
    ContentAnalysis -->|混合内容| MultiModal[多模态AI模型]
    OCRModel --> ResultNormalization[结果归一化处理]
    ObjectDetection --> ResultNormalization
    MultiModal --> ResultNormalization
```

**实现细节**:

**实际架构实现说明**：

在实际开发中，视觉服务功能采用了分散式架构而非统一的`VisionService`类，以优化性能和维护性：

- **AI路由决策**:
  - **路由策略**：系统直接路由到配置的多模态AI模型，无需复杂的内容预分析。这种简化架构降低了系统复杂度，同时保持了高效的处理能力。
  - **实现位置**：AI调用逻辑分布在各个Image Processing Worker中，通过Worker池实现并行处理。

- **结果缓存机制**:
  - **实现方式**: 缓存分散在各个Worker实例中，每个Worker维护基于哈希的内存缓存。
  - **缓存键 (Cache Key)**: 使用轻量级哈希函数对图像内容生成数值哈希值作为缓存键。
  - **缓存逻辑**: Worker在处理图像分段前检查缓存，命中时直接返回，避免重复API调用。

- **内容后处理**:
  - **实现方式**: 服务内部实现了一个`_postProcessText`私有方法。
  - **处理逻辑**: AI模型返回的原始文本会经过此方法处理，执行例如去除首尾多余的空白字符、移除API响应中可能包含的Markdown代码块标记（```）等清理操作，确保最终拼接的内容干净、格式统一。

### 3.5 异常处理服务

**功能**:

- 实时异常检测
- 用户警报系统
- 状态快照与恢复
- 安全回滚机制

**检测矩阵**:

| 异常类型        | 检测方法                          | 响应策略（由Task实体执行）                     |
|-----------------|-----------------------------------|------------------------------------------|
| 验证码          | reCAPTCHA iframe特征检测          | **暂停任务**，并通过`NotificationService`**调用浏览器通知**，提示用户手动干预。 |
| 跳转循环        | **基于时间窗口的历史记录监控**：在指定时间窗口（如10秒）内，当导航次数超过阈值（如5次），且唯一URL数量少于阈值的一半时，判定为循环。 | **暂停并回滚状态**：暂停任务，并使用`StateSnapshot`服务将标签页恢复到上一个已知的良好状态。 |
| 授权失效        | HTTP 401/403响应分析              | **暂停并回滚状态**：此为通用且安全的回退策略。任务将回滚至授权失效前的最后一个稳定页面。 |
| 行为分析        | **双重熵值分析**：**行为序列熵**：计算操作类型（点击、滚动、等待等）序列的香农熵。低熵（高可预测性）表示可能存在机器人行为。**点击位置分布熵**：将视口划分为网格，计算点击在各网格中分布的熵。低熵（高聚集性）同样被视为异常。 | **记录并预警**：系统记录详细日志，支持扩展为动态调整行为模式，增加随机性。 |

### 3.6 内容脚本管理与安全注入

**核心原则**: 为实现最高级别的安全与隔离，插件放弃了传统的 `manifest.json` 静态注入方式，转而采用**按需、编程式注入（Programmatic Injection）**。

- **静态注入的风险**: 在 `manifest.json` 中配置 `content_scripts` 会将脚本注入到用户访问的所有页面（基于 `matches` 规则），即使这些页面与插件任务完全无关。这不仅带来了不必要的性能开销，更存在严重的安全隐患，可能导致插件行为泄露到非目标站点。

- **编程式注入的优势**: 脚本的注入时机和目标完全由代码控制。在我们的架构中，只有当 `Task` 实例被创建并分配到一个具体的标签页（Tab）后，`Task.ts` 才会调用 `chrome.scripting.executeScript`，将内容脚本精准注入到该任务标签页中。这确保了脚本的"最小化存在"，仅出现于需要它们的地方。

**技术实现细节：动态 `import()`**

在实现编程式注入时，需要解决现代前端模块化带来的挑战，系统采用动态 `import()` 方案：

1. **挑战一：ES模块加载**: `chrome.scripting.executeScript({ files: [...] })` 无法直接执行包含 `import/export` 语句的ES模块文件，会导致 `Cannot use import statement outside a module` 错误。

2. **挑战二：执行上下文（Execution Context）**:
    - 一个失败的尝试是注入一个"加载器"脚本，在页面DOM中创建 `<script type="module">` 标签。这种方法虽然能让浏览器以模块方式加载脚本，但脚本会被执行在页面的**主世界（Main World）**。
    - 主世界中的脚本无法访问 `chrome.*` 等扩展API，导致 `webextension-polyfill` 等库因检测不到扩展环境而抛出错误，使脚本无法正常工作。

3. **解决方案**:
    - **隔离世界（Isolated World）**: 内容脚本必须运行在由浏览器提供的、与页面DOM隔离但能访问扩展API的"隔离世界"中。`chrome.scripting.executeScript({ func: ... })` 是确保代码在此环境中启动的唯一可靠方法。
    - **动态 `import()`**: 在 `executeScript` 注入的函数内部，我们使用 `import()` 这一现代JavaScript特性来异步加载内容脚本模块。

    ```typescript
    // 位于 Task.ts 中的实现
    await chrome.scripting.executeScript({
      target: { tabId: tabId, allFrames: true },
      func: async (scriptPath: string) => {
        const url = chrome.runtime.getURL(scriptPath);
        await import(url);
      },
      args: [/* ... script path ... */],
    });
    ```

**总结**: 此方案完美结合了两种技术的优点：通过 `executeScript` 保证了正确的**执行上下文**，通过动态 `import()` 实现了正确的**模块加载**。这不仅解决了技术难题，也形成了与插件整体高安全、高性能架构相匹配的最佳实践。

---

## 4. 可视化执行

**架构决策**:

为了简化架构、提升稳定性并遵循现代浏览器的安全实践，系统采用**基于"标准标签组"的统一可视化执行模型**。此模型放弃了区分"无感"与"可视"模式的复杂设计，所有任务都在标准的、可见的浏览器标签页中执行，确保了行为的一致性和可预测性。

### 4.1 实现机制

系统的工作模式核心是在用户窗口创建一个专用的、始终可见的"工作标签组"。

1. **创建与生命周期管理**:
    - **按需创建**: 当首个任务请求创建标签页时，由 `WorkerWindowManager` 在用户聚焦的窗口内，创建一个有固定名称（如"🦥"）和颜色的标签组，并将其设置为**折叠状态**。
    - **自动销毁**: `WorkerWindowManager` 会监听组内标签页的关闭事件。当最后一个任务标签页关闭后，该标签组会自动解散并清理，不留痕迹。
    - **后台创建**: 所有任务标签页都通过 `chrome.tabs.create` 并设置 `active: false` 来创建，确保整个过程不会切换用户的活动标签页。

### 4.2 智能遮罩系统

为了在提供完整视觉反馈的同时，最大限度减少对用户的干扰，系统实现了一套智能遮罩机制。遮罩层仅在用户**主动查看**任务标签页时才会显示。

1. **激活与监听**:
    - 当一个任务开始时，后台服务会向对应的标签页内容脚本发送 `ACTIVATE_AGENT` 消息。
    - 内容脚本收到消息后，会创建 `MaskController` 实例，并注册一个 `visibilitychange` 事件监听器。

2. **显示与隐藏**:
    - **显示**: 如果页面在激活时就是可见的，或者当用户后续切换到该标签页使其从不可见变为可见时 (`document.visibilityState === 'visible'`)，遮罩层会自动显示 (`maskController.show()`)。
    - **隐藏**: 当用户切换到其他标签页，导致任务页变为不可见时 (`document.visibilityState === 'hidden'`)，遮罩层会自动隐藏 (`maskController.hide()`)。

3. **销毁**:
    - 当任务结束时，后台会发送 `DEACTIVATE_AGENT` 消息。
    - 内容脚本会彻底销毁 `MaskController` 实例，并移除 `visibilitychange` 事件监听器，确保没有资源残留。

该机制巧妙地平衡了视觉反馈和用户体验，用户既可以在需要时观察任务的一举一动，又可以在不关心时免受打扰。

### 4.3 后台任务健壮性

为确保在后台运行的任务能"满功率"执行，系统采用了一套**"组合拳"策略**，同时对抗浏览器的两种后台限制：为节省内存而**丢弃标签页 (Tab Discarding)**，以及为节省CPU而**冻结脚本 (Script Freezing)**。

- **防丢弃 (Anti-Discarding)**: `WorkerWindowManager` 在创建每一个任务标签页后，会立刻调用 `chrome.tabs.update(tab.id, { autoDiscardable: false })`。这是官方推荐的标准API，用于防止浏览器在内存压力大时**终止**标签页进程，确保了任务页面的存活。
- **防冻结 (Anti-Freezing)**: 与此同时，系统通过 `OffscreenManager` 启动并管理一个全局的、静音的音频播放。浏览器通常不会"冻结"（即暂停JavaScript执行）一个正在播放音频的后台页面。此策略是确保滚动、截图等脚本能在后台**持续获得CPU时间片**、不被节流或暂停的关键。

```mermaid
graph TD
    subgraph MainWindow["用户主窗口"]
        UserInterface[用户界面]
        subgraph WorkTabGroup["工作标签组 - 始终展开"]
            Tab1[任务标签页1]
            Tab2[任务标签页2]
            TabN[更多任务标签页...]
        end
    end

    subgraph BackgroundService["后台服务 - Service Worker"]
        WindowManager[WorkerWindowManager<br/>窗口管理器]
        OffscreenMgr[OffscreenManager<br/>离屏管理器] -->|播放静音音频| KeepAliveSignal[全局防冻结信号]
    end

    UserInterface -->|发起任务| WindowManager
    WindowManager -->|创建并设置autoDiscardable:false| Tab1
    WindowManager -->|创建并设置autoDiscardable:false| Tab2
    WindowManager -->|创建并设置autoDiscardable:false| TabN

    KeepAliveSignal -->|防止脚本被冻结| Tab1
    KeepAliveSignal -->|防止脚本被冻结| Tab2
    KeepAliveSignal -->|防止脚本被冻结| TabN
```

---

## 5. 非阻塞并发浏览代理设计

### 5.1 中心化并发控制架构

为实现高效的非阻塞浏览，系统采用以 `TaskScheduler` 为核心的中心化并发控制架构。此架构取代了分散、静态的并发管理模式，确保所有任务（无论来源）都在统一的资源调配下运行。

```mermaid
graph TD
    subgraph BatchProc["BatchProcessor - 批处理器"]
        TaskQueue[任务队列] --> RequestSlot[请求槽位]
    end

    subgraph TaskSched["TaskScheduler - 中心调度器"]
        AcquireSlot[acquireSlot方法] --> SlotAvailable{可用槽位?}
        SlotAvailable -->|是| CreateTask[创建Task实例]
        SlotAvailable -->|否| WaitForSlot[等待槽位]
        ReleaseSlot[releaseSlot方法] --> AcquireSlot
        ResMonitor[ResourceMonitor<br/>资源监控] --> AdjustSlots[动态调整总槽位数]
        AdjustSlots --> AcquireSlot
    end

    TaskQueue --> AcquireSlot
    RequestSlot --> AcquireSlot
    CreateTask --> ConcurrentTask1[并发任务1]
    CreateTask --> ConcurrentTask2[并发任务2]
    ConcurrentTask1 --> ReleaseSlot
    ConcurrentTask2 --> ReleaseSlot
```

### 5.2 并发执行流程

系统的并发执行基于第3章描述的TaskScheduler架构，实现了以下关键特性：

- **智能调度**: 采用同源亲和性优先的任务分配策略，最大化浏览器缓存利用率
- **动态扩缩容**: 根据系统资源状况和任务队列长度自动调整并发槽位数量  
- **流水线处理**: 任务完成即时释放槽位，确保系统持续以最大吞吐量运行
- **健壮性保障**: 槽位生命周期与任务生命周期严格绑定，防止资源泄漏

详细的调度机制和实现细节请参考第3.1节"任务调度中心"。

### 5.3 高性能数据处理

**视觉处理流水线优化**:

```typescript
/**
 * Image Processing Pipeline - 使用共享工作池并行处理图像分段。
 */
import { sharedWorkerPool } from '../../services/pool/SharedWorkerPool';
import type { ContentSegment, ImageSegment } from '../../shared/types';
import { createLogger, Logger } from '../../shared/utils';

// (辅助类型和函数)
type WorkerResult = ContentSegment | { error: string; segmentId: string };
function isErrorResult(result: WorkerResult): result is { error: string; segmentId: string } {
    return (result as { error: string }).error !== undefined;
}

export class ImageProcessingPipeline {
  private readonly logger: Logger;

  constructor() {}

  async processSegments(segments: ImageSegment[]): Promise<ContentSegment[]> {
    const processingPromises = segments.map(segment => {
      // 图像解码（如果尚未完成）
      const createBitmapPromise = segment.bitmap 
        ? Promise.resolve(segment.bitmap)
        : createImageBitmap(new Blob([segment.data], { type: segment.mimeType }));

      return createBitmapPromise.then(bitmap => {
        const transferableSegment = { ...segment, bitmap };
        // 将任务提交到共享池，并转移ImageBitmap的所有权以实现零拷贝。
        return sharedWorkerPool.run(transferableSegment, [bitmap]);
      });
    });

    const results = await Promise.all(processingPromises);
    const contentSegments: ContentSegment[] = [];

    results.forEach(result => {
      if (!isErrorResult(result)) {
        contentSegments.push(result);
      }
    });

    // 按垂直位置对结果进行排序，确保内容连贯
    contentSegments.sort((a, b) => a.position.y - b.position.y);
    
    return contentSegments;
  }
}
```

**内存优化策略**:

1. 使用`ArrayBuffer`替代base64编码
2. 及时释放未使用的ImageBitmap
3. 分块处理间隔加入GC机会

```javascript
// 显式内存释放
function releaseResources(segment: ImageSegment) {
  if (segment.bitmap) {
    segment.bitmap.close();
    delete segment.bitmap;
  }
  segment.data = null;
}
```

### 5.4 资源协调与性能优化

**资源协调原则**:

系统采用分层的资源管理策略，确保不同组件之间的资源使用得到有效协调：

- **TaskScheduler (任务级并发管理)**: 负责控制同时运行的任务标签页数量，基于系统硬件能力和资源状况动态调整
- **ResourceMonitor (系统级监控)**: 提供全局和局部的性能监控数据，为动态调整提供决策依据
- **AdaptiveThrottler (自适应节流)**: 根据资源压力自动调整各组件的行为参数，实现智能降级

**性能优化策略**:

1. **按需资源分配**: 所有重量级组件（如ResourceMonitor、标签页等）都采用按需创建和销毁的策略
2. **智能节流机制**: 在资源紧张时自动降低截图质量、减慢行为模拟速度等
3. **内存管理**: 定期清理已完成任务的数据，防止内存泄漏

---

## 6. 异常处理与恢复系统

### 6.1 异常检测流程

```mermaid
graph TD
    DOMMonitor[DOM监控器] --> ExceptionDetection{检测异常模式}
    NetworkMonitor[网络监控器] --> ExceptionDetection
    BehaviorAnalysis[行为分析器] --> ExceptionDetection
    
    ExceptionDetection -->|检测到验证码| PauseTask[暂停任务]
    ExceptionDetection -->|跳转循环/授权失效| RollbackState[回滚状态]
    
    PauseTask --> NotifyService[调用通知服务]
    RollbackState --> NotifyService
    NotifyService --> NotifyUser[通知用户]
    NotifyUser --> UserIntervention[用户手动干预]
    UserIntervention --> ContinueTask[继续执行任务]
```

**检测矩阵**:

| 异常类型        | 检测方法                          | 响应策略（由Task实体执行）                     |
|-----------------|-----------------------------------|------------------------------------------|
| 验证码          | reCAPTCHA iframe特征检测          | **暂停任务**，并通过`NotificationService`**调用浏览器通知**，提示用户手动干预。 |
| 跳转循环        | **基于时间窗口的历史记录监控**：在指定时间窗口（如10秒）内，当导航次数超过阈值（如5次），且唯一URL数量少于阈值的一半时，判定为循环。 | **暂停并回滚状态**：暂停任务，并使用`StateSnapshot`服务将标签页恢复到上一个已知的良好状态。 |
| 授权失效        | HTTP 401/403响应分析              | **暂停并回滚状态**：此为通用且安全的回退策略。任务将回滚至授权失效前的最后一个稳定页面。 |
| 行为分析        | **双重熵值分析**：**行为序列熵**：计算操作类型（点击、滚动、等待等）序列的香农熵。低熵（高可预测性）表示可能存在机器人行为。**点击位置分布熵**：将视口划分为网格，计算点击在各网格中分布的熵。低熵（高聚集性）同样被视为异常。 | **记录并预警**：系统记录详细日志，支持扩展为动态调整行为模式，增加随机性。 |

### 6.2 状态快照与恢复

**快照机制**:

`StateSnapshot`服务封装了对标签页状态的捕获与恢复能力。

- **捕获 (`capture`)**: 在任务成功处理完一个页面后，会调用此方法，捕获并存储一个包含DOM结构、滚动位置、Cookies和表单数据的"已知良好状态"快照。
- **恢复 (`restore`)**: 当`Task`实体接收到严重异常（如跳转循环）并决定回滚时，会调用此方法。恢复流程经过精心设计，以确保稳定性：
    1. **重新加载URL**: 首先导航到快照中记录的URL。
    2. **等待页面加载**: 监听并等待页面达到`complete`状态。
    3. **恢复Cookies、表单与滚动**: 页面加载完成后，通过`chrome.scripting.executeScript`注入脚本，分别恢复Cookies、表单(`restoreFormData`)和滚动条位置(`restoreScrollPosition`)。

**实现说明**:

> 我们选择**不直接注入（inject）整个DOM**的HTML字符串。因为现代网页高度依赖JavaScript进行初始化，直接替换DOM会破坏页面自身的生命周期，导致功能异常。当前采用的"重载页面后恢复子状态"的策略，是更为健壮和可靠的实现。

```typescript
// browser-viewer/src/services/exception-service/StateSnapshot.ts

class StateSnapshot {
  public async capture(tabId: number): Promise<TabState | null> { /*...*/ }
  
  public async restore(tabId: number, state: TabState): Promise<void> {
    // 1. 导航到目标URL并等待加载完成
    await chrome.tabs.update(tabId, { url: state.url });
    await this.waitForTabLoad(tabId); // 内部实现省略

    // 2. 恢复Cookies、表单、滚动位置
    await this.restoreCookies(state.url, state.cookies);
    await this.restoreFormData(tabId, state.formData);
    await this.restoreScrollPosition(tabId, state.scrollPosition);
  }
  
  private async restoreFormData(tabId: TabId, formData: Record<string, any>): Promise<void> {
    if (Object.keys(formData).length === 0) return;
    await chrome.scripting.executeScript({
      target: { tabId },
      func: (data: Record<string, any>) => {
        for (const name in data) {
          const el = document.querySelector(`[name="${name}"]`) as HTMLInputElement;
          if (el) el.value = data[name];
        }
      },
      args: [formData],
    });
  }

  // ... 其他私有捕获/恢复方法 ...
}
```

### 6.3 任务持久化与恢复

为了确保即使在浏览器关闭或插件更新后任务进度也不会丢失，系统实现了任务状态的持久化与恢复机制，其核心在于`TaskScheduler`和`Task`之间的协作。

1. **定时保存**: `TaskScheduler`通过心跳机制（`setInterval`），定期调用`saveAllTasksState`方法，该方法将所有任务的配置和状态通过`task.serialize()`序列化为JSON格式，并存储在`chrome.storage.local`中。

2. **依赖注入式恢复**:
    - `TaskScheduler`在初始化时，会创建所有核心服务（如`ExceptionService`, `StateSnapshot`, `NotificationService`）的单例。
    - 随后，它调用`restoreAllTasksState`方法从存储中读取之前保存的所有任务状态。
    - 对于每个读取到的状态，它会调用`Task.deserialize()`静态方法来重建`Task`实例。**关键在于，它会将自身持有的服务单例作为参数传递给`deserialize`方法**。
    - `Task.deserialize()`在创建新的`Task`实例时，会将这些接收到的服务注入到新实例中，从而确保恢复后的任务拥有与新创建任务完全相同的能力。
    - 恢复后的任务如果原状态为`running`，将被置为`paused`，等待用户指令，以防意外的自动执行。

这个"集中管理服务、反序列化时注入"的设计，优雅地解决了循环依赖和任务恢复时的能力缺失问题，是系统健壮性的基石。

---

## 7. 性能优化体系

### 7.1 三级优化策略

| 优化层级 | 策略 | 效果 |
|---------|------|------|
| **任务级** | 动态优先级调整 | 减少高负载时竞争 |
| **进程级** | 空闲调度(requestIdleCallback) | 利用浏览器空闲期 |
| **操作级** | 操作批处理 | 减少渲染次数 |

### 7.2 关键性能指标

```typescript
const PERFORMANCE_TARGETS = {
  CPU_PER_TAB: 0.15,        // 单标签页最大CPU占用
  MEMORY_PER_TAB: 150,      // 单标签页内存(MB)
  NETWORK_PER_TAB: 2,       // 单标签页网络(Mbps)
  TASK_THROUGHPUT: 8,       // 每分钟处理页面数
  RESPONSE_LATENCY: 200,    // 异常响应延迟(ms)
};
```

### 7.3 自适应节流算法

为实现真正的自适应，系统的节流算法放弃了简单的硬编码 `switch` 逻辑，转而采用一个更灵活、更强大的**事件驱动架构**。该架构的核心是 `AdaptiveThrottler` 服务，它不直接执行操作，而是根据性能指标确定一个"节流级别"，并**触发与该级别关联的事件**。

**中心化协调与指令下发**:

`TaskScheduler` 作为系统的中心调度器，负责编排整个节流响应流程：

1. **实例化服务**: `TaskScheduler` 在初始化时，会创建 `ResourceMonitor` 和 `AdaptiveThrottler` 的单例。
2. **订阅指标**: 它订阅 `ResourceMonitor` 的实时性能指标流 (`metrics$`)，并将数据持续输入 `AdaptiveThrottler` 的 `adjust` 方法。
3. **注册响应动作**: `TaskScheduler` 调用 `adaptiveThrottler.on(level, action)` 来为每个节流级别（0, 1, 2）注册具体的响应函数 (`action`)。
4. **执行与广播**: 当 `AdaptiveThrottler` 检测到级别变化并触发相应动作时，`TaskScheduler` 的注册函数会被执行。它会做两件事：
    - **调整自身**: 调用 `setDynamicMaxConcurrency()` 方法，动态调整全局的最大并发任务数。
    - **广播指令**: 调用 `broadcastThrottlingLevel()` 方法，遍历所有正在运行的 `Task` 实例，并调用每个任务的 `applyThrottling(level)` 方法，将节流指令下发到每个工作单元。

**多层级节流策略实现**:

`Task` 实例的 `applyThrottling` 方法根据接收到的级别，执行具体的多层级优化：

| 节流级别 | 并发控制 (TaskScheduler) | 任务行为控制 (Task -> BehaviorSimulator & CaptureSystem) |
| :--- | :--- | :--- |
| **0 (正常)** | 恢复初始并发数 | 行为速度: `normal`, 动画帧率: `60FPS`, 截图质量: `90`, 恢复所有行为 |
| **1 (轻度)** | 并发数减少 `25%` | 行为速度: `slow`, 动画帧率: `30FPS`, 截图质量: `75`, **不**暂停非关键行为 |
| **2 (深度)** | 并发数减少 `50%` | **暂停分心等非关键行为**, 截图质量: `50` (大幅降低) |

这种设计将"检测"和"响应"完全解耦，使得增加新的节流级别或修改响应策略变得简单，只需在 `TaskScheduler` 中修改注册的 `action` 即可，无需触及核心的节流逻辑。

### 7.4 异步I/O解耦原则

为了最大限度地提升插件的响应速度和任务吞吐量，系统遵循严格的异步I/O解耦原则。

- **核心思想**: 任何不影响操作直接逻辑、且耗时较长的I/O操作（尤其是网络请求），都必须从主行为循环中解耦，使其并行执行。应尽快将CPU控制权返还给事件循环，以允许其他任务或UI更新及时执行。

- **实现模式**: "即发即忘，带承诺追踪" (Fire and Forget with Promise Tracking)
  1. 对于耗时的异步操作（如 `visionService.processImage`），不使用 `await` 阻塞当前函数。
  2. 调用异步函数，并将其返回的 `Promise` 存入一个追踪数组中（如 `CaptureSession` 中的 `pendingOperations`）。
  3. 主逻辑可以立即继续执行后续操作（如 `BehaviorEngine` 的下一次滚动）。
  4. 在需要确保所有并行任务都已完成的检查点（如 `endSession` 时），使用 `await Promise.all()` 来等待追踪数组中的所有 `Promise` 完成。

- **应用场景**:
  - **站点优化策略**: 新增的"基于策略的自适应数据提取"是此原则的最佳体现。对于支持结构化数据提取的网站（如京东），我们完全绕过了高延迟的截图和AI分析IO，直接返回数据，这是最高级别的IO优化。
  - **视觉处理系统 (`CaptureSystem`)**: 将AI分析请求并行化，极大地提升了页面滚动捕捉的流畅度和效率。
  - **未来应用**: 任何未来新增的、需要与外部服务通信的功能（如数据上报、远程配置获取等），都应遵循此模式，确保其不会阻塞核心的任务执行流程。

### 7.5 分层性能监控架构

**设计背景**:

插件的核心模块，如`TaskScheduler`和`ResourceMonitor`，运行在Service Worker的后台环境中。Service Worker无法访问任何与浏览器UI相关的API，例如用于测量页面帧率（FPS）的`requestAnimationFrame`。直接在Service Worker中测量FPS会导致程序错误。然而，FPS是评估任务页面是否流畅、行为模拟是否自然的关键指标。

为了建立一个全面的监控体系，系统采用了**分层性能监控架构**，将**全局监控**与**局部监控**分离开来。

**架构图**:

```mermaid
graph TD
    subgraph ServiceWorkerLayer["Service Worker - 后台层"]
        GlobalMonitor["全局ResourceMonitor<br/>资源监控器(无FPS)"] --> TaskSchedulerSys[TaskScheduler<br/>任务调度系统]
        StateAggregator["中心状态聚合器"] --> TaskSchedulerSys
        TaskSchedulerSys --> ConcurrencyControl[动态并发控制]
    end

    subgraph VisualTab1["标签页1 - 任务执行"]
        ContentScript1["内容脚本1"] --> LocalMonitor1["局部ResourceMonitor<br/>本地监控器(含FPS)"]
        LocalMonitor1 -->|性能消息含FPS| StateAggregator
    end

    subgraph VisualTab2["标签页2 - 任务执行"]
        ContentScript2["内容脚本2"] --> LocalMonitor2["局部ResourceMonitor<br/>本地监控器(含FPS)"]
        LocalMonitor2 -->|性能消息含FPS| StateAggregator
    end
    
    classDef globalStyle fill:#f9f,stroke:#333,stroke-width:2px
    classDef localStyle fill:#ccf,stroke:#333,stroke-width:2px
    
    class GlobalMonitor globalStyle
    class LocalMonitor1,LocalMonitor2 localStyle
```

**实现机制**:

1. **全局监控器 (在Service Worker中)**:
    - **职责**: 负责监控插件的**整体**CPU和内存使用情况。这些宏观指标是`TaskScheduler`进行动态并发任务数调整的核心依据。
    - **生命周期管理**: 全局监控器的生命周期由 `TaskScheduler` **按需管理**。它并不会随插件启动而一直运行。相反，仅当有任务开始执行时，`TaskScheduler` 才会启动它；当所有任务都完成后，`TaskScheduler` 会将其停止，以确保零资源占用。
    - **实现**: `ResourceMonitor`在初始化时会进行**一次性能力检测**。当发现自身处于Service Worker环境（即`requestAnimationFrame`不可用）时，它会自动禁用FPS测量功能，并只在控制台打印一次警告，从而避免了在后台环境中反复尝试调用不可用API导致的错误和日志刷屏。

2. **局部监控器 (在任务标签页的Content Script中)**:
    - **职责**: 当任务在特定标签页运行时，在该页面的内容脚本中动态创建一个局部的`ResourceMonitor`实例。这个实例的唯一使命就是监控**这个特定页面**的性能，尤其是**FPS**。
    - **生命周期管理**: 局部监控器的生命周期与任务的生命周期严格绑定。
        - 当`Task`对象启动或恢复任务时，它会向对应标签页的内容脚本发送任务监控启动消息。
        - 内容脚本收到消息后，创建并启动局部的`ResourceMonitor`。
        - 当`Task`对象暂停、取消或完成任务时，它会发送监控停止消息，内容脚本随即销毁局部监控器，以节省性能。

3. **中心化数据聚合 (在Service Worker中)**:
    - **职责**: 局部监控器通过`PERFORMANCE_UPDATE`消息，将采集到的包含FPS的性能指标周期性地汇报给Service Worker。
    - **实现**: `TaskScheduler`接收到这些消息后，会将这些来自特定标签页的性能指标，更新到对应`Task`的`TaskMetrics`对象的`tabPerformance`字段中。这使得调度器在做决策时，不仅能看到全局的资源状况，还能洞察到每个任务的具体健康度，为未来实现更精细的节流策略（如"当某个标签页FPS过低时，自动降低其行为模拟速度"）打下了基础。

这种分层架构不仅解决了在Service Worker中无法测量FPS的问题，还通过事件驱动和严格的生命周期管理，实现了对系统资源的健壮、高效的监控。

---

## 8. 安全与隐私保护

### 8.1 数据安全策略

1. **敏感数据处理**:
   - 使用`chrome.storage.session`临时存储
   - 自动清除无头环境存储分区
   - 持久化数据加密(WebCrypto API)

2. **内容过滤**:

```typescript
const DOM_SANITIZER_CONFIG = {
  ALLOWED_TAGS: ['p', 'h1', 'h2', 'h3', 'ul', 'ol', 'li', 'img'],
  FORBID_TAGS: ['input', 'textarea', 'button'],
  FORBID_ATTR: ['autocomplete', 'name', 'id', 'data-sensitive']
};

function sanitizeContent(html: string): string {
  return DOMPurify.sanitize(html, DOM_SANITIZER_CONFIG);
}
```

**实现细节**:

- **持久化数据加密**: 为了保护用户隐私，`TaskScheduler`在将任务列表（可能包含敏感URL）持久化到`chrome.storage.local`之前，会使用`SecurityService`进行AES-GCM加密。当插件重启恢复任务时，会先进行解密。为了在 Manifest V3 环境下兼容 Service Worker 的临时生命周期，**加密密钥被设计为在浏览器会话期间持久化**。具体实现如下：
  - **密钥的生成与存储**：`SecurityService`初始化时，会尝试从`chrome.storage.session`（会话级存储）中加载密钥。如果不存在，它会生成一个新的、可导出的`CryptoKey`，并将其原始数据存入`chrome.storage.session`。
  - **密钥的生命周期**：此密钥在整个浏览器会话（从浏览器启动到关闭）中保持不变，确保即使 Service Worker 多次重启，也能使用同一密钥进行加解密。浏览器完全关闭后，`chrome.storage.session`会自动清除，密钥随之销毁，从而保证了每次大的浏览器会话都使用全新的密钥，提升了安全性。
- **任务完成后的存储清理**: 为防止隐私泄露，`Task`在完成对一个URL的处理后，会清理任务过程中产生的临时数据和缓存，确保不会留下任何浏览痕迹。系统通过PrivacyService统一管理隐私数据的清理策略。

### 8.2 隐私保护机制

1. **用户控制 (黑名单模式)**:
   - **选择性Cookie同步**: 用户可在设置页提供一个"Cookie同步域名**黑名单**"，代理将不会对匹配该名单的域名进行任何Cookie的读取和写入操作。
   - **代理配置黑名单**: 用户可配置一个"代理URL**黑名单**"，代理将自动跳过所有匹配该名单的URL，不予执行。
   - **默认行为**: 在用户未配置任何黑名单的情况下，插件默认允许处理所有网站并同步其Cookie，以符合"用户浏览代理"的完整功能定位。
   - **数据处理透明度面板**: 设置页提供一个可刷新的面板，展示所有由审计追踪系统记录的数据访问日志，让用户完全了解插件的行为。

2. **审计追踪**:

```typescript
// browser-viewer/src/services/privacy-service/PrivacyService.ts
class PrivacyService {
  public async logDataAccess(event: DataAccessEvent): Promise<void> {
    const logEntry = {
      timestamp: Date.now(),
      ...event,
    };
    
    // 从chrome.storage.local获取日志，追加新条目，并写回
    const result = await chrome.storage.local.get('agent:privacy_log');
    const newLog = [logEntry, ...(result['agent:privacy_log'] || [])];
    if (newLog.length > 500) newLog.splice(500); // 日志上限
    
    await chrome.storage.local.set({ 'agent:privacy_log': newLog });
  }
}
```

---

## 9. 代码质量与测试

### 9.1 TypeScript严格模式配置

项目启用TypeScript严格模式以确保类型安全：

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true
  }
}
```

### 9.2 ESLint规则与代码质量

使用ESLint和SonarQube规则确保代码质量：

- **类型安全**: 禁止使用`any`类型，要求明确的错误类型处理
- **异常处理**: 所有catch块必须提供具体的错误处理逻辑
- **空安全**: 使用可选链操作符(`?.`)和nullish合并操作符(`??`)
- **代码复用**: 避免重复的函数实现，确保函数返回值的多样性

### 9.3 错误处理最佳实践

```typescript
// ✅ 正确的错误处理
try {
  await riskyOperation();
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logger.error('Operation failed:', errorMessage);
  // 具体的恢复逻辑
}

// ❌ 避免的做法
try {
  await riskyOperation();
} catch (error) { // 缺少类型声明
  console.log(error); // 不安全的错误访问
}
```

### 9.4 测试策略

| 测试层级 | 工具 | 覆盖范围 | 目标 |
|---------|------|----------|------|
| **单元测试** | Vitest | 核心逻辑函数 | >80%覆盖率 |
| **集成测试** | Vitest + Mock | 服务间交互 | 关键路径100% |
| **端到端测试** | Playwright | 浏览器环境 | 核心用户流程 |

---

## 10. 部署与监控

### 10.1 CI/CD流程

```mermaid
graph LR
    CodeCommit[代码提交] --> UnitTest[单元测试]
    UnitTest --> IntegrationTest[集成测试]
    IntegrationTest --> PerformanceTest[性能基准测试]
    PerformanceTest --> BuildPackage[构建插件包]
    BuildPackage --> AutoSign[自动化签名]
    AutoSign --> TestEnv[发布到测试环境]
    TestEnv --> ManualQA[人工验收测试]
    ManualQA --> ProductionDeploy[生产环境发布]
```

### 10.2 监控指标

| 指标类别 | 监控项 | 报警阈值 |
|----------|--------|----------|
| **性能** | CPU使用率 | >85%持续30s |
| | 内存使用 | >1.2GB |
| **任务** | 吞吐量下降 | <5页/分钟 |
| | 任务失败率 | >10% |
| **异常** | 验证码频率 | >2次/小时 |
| | 恢复失败率 | >20% |

### 10.3 开发环境与浏览器兼容性

**1. Vite项目静态资源管理**:

为了确保浏览器扩展的关键文件（如`manifest.json`）能被正确打包，所有需要原样复制到输出目录 `dist` 根路径下的静态资源，都必须放置在 `public` 文件夹中。Vite的构建流程会自动处理该目录下的所有文件。

**2. 跨平台开发（尤其WSL）兼容性**:

当在Windows上使用WSL2进行开发时，从Windows中的浏览器（如Edge/Chrome）直接加载位于WSL文件系统（路径形如 `\\wsl.localhost\Ubuntu\...\dist`）的解压缩扩展，有时可能会因网络路径权限或浏览器安全策略导致失败，并出现"清单文件不可读"的错误。

**推荐的开发工作流**:

为规避此问题并确保最佳兼容性，建议在启动`watch`模式开发后，将生成的`dist`目录从WSL复制到Windows文件系统下的一个路径（例如 `C:\dev\extensions`），然后从该Windows路径加载扩展。这可以避免所有潜在的跨文件系统访问问题。

**3. 多浏览器兼容策略**:

- **API使用**: 使用 `webextension-polyfill` 库，它提供了基于Promise的、跨浏览器兼容的API，可以抹平Chrome和Firefox等浏览器之间的差异。在代码中，应通过 `import browser from 'webextension-polyfill'` 导入该库，从而在获得 TypeScript 类型支持的同时，直接使用 `browser` 全局对象（如 `browser.storage.local`），避免了对 `chrome.*` API 的直接依赖和手动进行浏览器兼容性判断。

- **功能测试**: 在CI/CD流程中应包含针对不同目标浏览器（如Chrome, Edge, Firefox）的自动化测试，确保核心功能在所有支持的平台上表现一致。

- **特性降级与核心优势**:
  - **根本性解决**: 本方案采用的**基于内容脚本和 `modern-screenshot` 的新一代视觉处理系统**，从根本上解决了截图功能对特定浏览器API的依赖。因为它完全基于所有现代浏览器都支持的Web标准（DOM、Canvas、Web Worker），所以核心的数据捕捉功能天然具备了**高度的跨浏览器兼容性**。
  - **关注点分离**: 这使得我们的兼容性关注点可以从"核心功能能否运行"，转移到"UI和次要API的细微差异"上，大大降低了多浏览器适配的难度和成本。过去需要`isChromiumBrowser()`这类函数来处理的复杂逻辑分支（如Offscreen API的使用），其重要性也随之下降，因为核心瓶颈已被消除。

---

## 11. 工作流模式 (Workflow Modes)

本插件包含两种主要的工作流模式：**开发调试模式**和**生产运行模式**。这两种模式在用户交互、任务创建和执行流程上有所区别，旨在优化开发效率和保障生产环境的稳定与安全。

**重要提示**: 在进行生产环境打包前，开发者**必须**确保将插件的逻辑从开发调试模式切换至生产运行模式。相关切换配置应在构建脚本 (`vite.config.ts` 或 `scripts/post-build.js`) 中进行管理，例如通过环境变量来移除或禁用开发模式专属的UI和逻辑。

### 11.1 开发调试模式 (Development & Debugging Mode)

此模式专为开发和内部测试设计，旨在简化调试流程，允许开发者快速验证核心功能。

**核心特性**:

1. **直达设置**: 在弹出面板的页眉右上角提供齿轮图标 (`<i class="fas fa-cog"></i>`)，点击可直接打开 `options.html` 设置页面，方便开发者快速调整插件的底层行为配置。

2. **跳过身份验证**: 无需与平台网站集成，插件可独立运行。在插件的弹出面板中提供开发模式专用的URL输入框。

3. **手动创建任务**:
    - **UI实现**: 通过 `#devModeSection` 提供URL输入区域，包含 `<textarea>` 用于输入多个URL
    - **输入格式**: 支持换行符和逗号分隔的多个URL格式
    - **任务创建**: 点击"创建批处理任务"按钮后调用 `createBatchTask()` 方法
    - **状态管理**: 新创建的任务以"pending"状态显示，不会自动执行
    - **UI反馈**: 创建后清空输入框，任务立即出现在下方列表中

4. **任务执行控制**:
    - **三按钮设计**: 提供"开始所有/开始任务"、"暂停所有/暂停任务"、"清除所有/清除任务"按钮
    - **智能切换**: 按钮文本根据是否选中任务动态变化（如"开始所有" ↔ "开始任务"）
    - **选择机制**: 通过checkbox实现任务多选，支持批量操作
    - **并发控制**: 执行遵循 `TaskScheduler` 定义的动态并发规则

5. **任务监控与详情**:
    - **折叠式展示**: 使用 `<details>` 元素实现任务列表的展开/折叠
    - **进度可视化**: 实时显示进度条（`${task.progress.processed}/${task.progress.total}`）
    - **状态指示**: 彩色状态标签区分 running/completed/failed/paused/pending
    - **URL级别状态**: 展开任务后显示每个URL的处理状态和结果

6. **下载功能实现**:
    - **触发机制**: 成功处理的URL旁边显示下载按钮 (`fas fa-download` 图标)
    - **消息流程**:

      ```typescript
      // popup.ts 发送下载请求
      this.sendMessage({
        type: MESSAGE_TYPES.DOWNLOAD_TASK_ASSETS,
        payload: { taskId, url }
      });
      
      // service-worker.ts 处理下载
      const task = this.taskScheduler.getTaskFromAnywhere(payload.taskId);
      const images = task.getCapturedImages(payload.url);
      
      // 使用 JSZip 打包并触发下载
      const zip = new JSZip();
      images.forEach((image, index) => {
        zip.file(`capture-${index + 1}.jpg`, new Blob([image.data]));
      });
      chrome.downloads.download({ url: downloadUrl, filename, saveAs: true });
      ```

    - **文件格式**: 将所有截图打包成ZIP文件，文件名格式为 `agent-captures-{domain}-{timestamp}.zip`
    - **错误处理**: 对找不到任务或图像为空的情况提供适当的日志记录

**实现架构**:

- **模式检测**: 通过 `declare const __DEV_MODE__: boolean` 在构建时控制开发模式功能
- **UI条件渲染**: 根据 `DEV_MODE` 标志显示或隐藏开发模式相关UI元素
- **服务工作者**: 开发模式消息通过 `handleDevUIMessage()` 方法专门处理
- **状态同步**: 通过 `taskMetrics$` Observable 实现任务状态的实时更新

### 11.2 生产运行模式 (Production Mode)

此模式是面向用户的标准运行模式，与外部平台深度集成，提供稳定、自动化的服务。

**核心流程**:

1. **用户身份验证**: 插件未登录前被激活（通过平台网站的API调用），会首先要求登录验证当前用户的身份和授权状态。
2. **任务自动创建**: 身份验证通过后，插件会从用户请求中自动获取一个或多个目标URL，并将这一次请求的所有URL组合成一个批处理任务。
3. **任务管理界面 (UI)**:
    - 插件弹出面板以列表形式展示用户的所有批处理任务，按创建时间倒序排列（最新的在前）。
    - 面板提供批量操作功能。用户可以通过点击或长按划选来选定一个或多个任务。
        - **选中任务时**: 出现"开始任务"、"暂停任务"、"清除任务"按钮，针对所选任务进行操作。
        - **未选中任何任务时**: 按钮变为"开始所有"、"暂停所有"、"清除所有"，对所有未完成的任务生效（操作前会有二次确认）。
4. **任务执行与调度**:
    - **批任务队列**: 任务严格按照列表顺序（即时间顺序）依次执行。一个批任务必须完全结束后（所有URL处理完毕），下一个批任务才会开始。即使在执行过程中有新任务被创建，也不会中断当前批任务。
    - **批任务内并发**: 在一个批任务内部，多个URL会根据 `TaskScheduler` 和 `ResourceMonitor` 动态管理的并发槽位进行并行处理，以最大化资源利用率。
    - **完整的处理流水线**: 每个URL都会经过完整的处理流程：模拟人类行为（滚动、分心）、分块视觉捕捉、将截图发送至多模态AI模型进行分析、结果归一化、内容拼接去重等。
5. **进度与状态**:
    - 插件面板主要显示批任务的**总体进度**（例如，"已完成 3/10 个URL"），而非单个URL的详细进度。
    - 当一个批任务内的所有URL都处理完成（无论成功或失败），该批任务的状态会被标记为"已完成"，期间如果有任意URL弹出验证码需要用户手动处理，该批任务继续运行，但最后结果会标记为失败。

---

## 12. 实施路线图

### 阶段1: 基础架构 (2周)

- 实现任务调度中心
- 完成无头浏览环境
- 建立基础通信总线

### 阶段2: 核心功能 (3周)

- 行为模拟引擎开发
- 视觉处理流水线
- 异常检测系统

### 阶段3: 高级特性 (2周)

- 统一可视化执行模型
- 资源感知调度器
- 可视化监控面板

### 阶段4: 优化迭代 (持续)

- 性能调优
- 异常处理增强
- AI模型适配
- 多浏览器兼容

---

## 📝 技术方案总结

本技术方案提供了一套完整的浏览器智能代理插件解决方案，具备以下核心优势：

### 🏗️ **企业级架构特性**

- **微服务设计**：符合微服务设计原则，模块高度解耦
- **可扩展性**：支持水平扩展和功能模块化扩展
- **兼容性**：跨浏览器兼容，为未来支持Firefox等浏览器奠定基础

### ⚡ **生产级性能保障**

- **三级优化策略**：任务级、进程级、操作级全方位性能优化
- **自适应节流**：基于资源监控的动态性能调整
- **异步I/O解耦**：确保高吞吐量和响应速度

### 👁️ **统一的可视化执行**

- **单一执行模型**：简化架构，所有任务均在标准标签页中执行。
- **智能遮罩系统**：仅在用户主动查看时显示遮罩，兼顾反馈与体验。
- **后台任务健壮性**：结合防丢弃与防冻结策略，确保后台任务稳定运行。

### ⚠️ **完备的异常处理机制**

- **实时检测**：验证码、跳转循环、授权失效等异常智能识别
- **自动恢复**：状态快照与回滚机制
- **用户协同**：关键异常的用户干预机制

### 🔒 **严格的隐私保护**

- **数据最小化**：只收集必要的浏览数据
- **用户控制**：黑名单机制，用户完全控制代理行为
- **透明度**：完整的审计追踪和日志记录

---

### 🎯 **适用场景**

- **企业自动化**：批量网页数据采集和处理
- **测试验证**：Web应用的自动化测试和验证
- **内容聚合**：多源信息的智能收集和整理
- **监控巡检**：网站状态和内容变化监控

---

### 📞 **技术支持**

如需了解更多实施细节或有技术问题，请参考：

- [📖 返回完整目录 `L25`](#📋-完整目录)
- [🏗️ 查看系统架构 `L140`](#21-系统架构图)
- [🚀 开始实施 `L1434`](#12-实施路线图)

---

<div align="center">

**🎉 感谢阅读**

*本方案为数懒孪生代理项目提供了完整的技术实现路径，助力企业构建高效、安全、可靠的智能浏览代理系统。*

[⬆️ 返回顶部 `L1`](#数懒孪生代理---浏览器智能代理插件技术解决方案) | [📋 查看目录 `L25`](#📋-完整目录)

</div>
