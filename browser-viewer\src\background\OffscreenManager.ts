/**
 * Offscreen Manager - 统一管理 Offscreen Document 的生命周期。
 *
 * 设计目标：
 * 1.  **单一实例**: 确保整个扩展只有一个 Offscreen Document 实例。
 * 2.  **生命周期管理**: 使用引用计数来决定何时创建和销毁 Offscreen Document。
 * 3.  **冲突解决**: 避免多个服务因各自管理生命周期而产生冲突。
 */
import { createLogger, Logger } from "@/shared/utils";

const OFFSCREEN_DOCUMENT_PATH = '/offscreen.html';

class OffscreenManager {
  private readonly _logger: Logger;
  private _refCount = 0;
  private _creationPromise: Promise<void> | null = null;

  constructor() {
    this._logger = createLogger("[OffscreenManager]");
  }

  /**
   * 启动静音音频播放以防止Service Worker被终止。
   * 此方法通过增加引用计数来工作。
   */
  public async startAudio(): Promise<void> {
    this._refCount++;
    this._logger.info(`Audio requested, refCount is now ${this._refCount}`);

    // 如果这是第一个请求，则创建文档并播放音频
    if (this._refCount === 1) {
      this._logger.info('First audio request, ensuring document exists and playing audio.');
      try {
        await this._getOrCreateDocument();
        await chrome.runtime.sendMessage({
          target: 'offscreen-audio',
          type: 'PLAY_AUDIO',
        });
        this._logger.info('PLAY_AUDIO message sent successfully.');
      } catch (error) {
        this._logger.error('Failed to start audio playback:', error);
        // 如果启动失败，回滚引用计数
        this._refCount--;
        throw error;
      }
    }
  }

  /**
   * 停止静音音频播放。
   * 此方法通过减少引用计数来工作。当没有更多的请求时，文档将被关闭。
   */
  public async stopAudio(): Promise<void> {
    if (this._refCount <= 0) {
      this._logger.warn("stopAudio called but refCount is already zero or less.");
      return;
    }

    this._refCount--;
    this._logger.info(`Audio released, refCount is now ${this._refCount}`);

    // 如果这是最后一个释放，则关闭文档
    if (this._refCount === 0) {
      this._logger.info("Last audio request released, closing document.");
      await this._closeDocument();
    }
  }

  /**
   * 获取或创建Offscreen Document的内部方法。
   * 使用一个Promise来处理并发的创建请求。
   */
  private async _getOrCreateDocument(): Promise<void> {
    if (await this._hasDocument()) {
      return;
    }

    this._creationPromise ??= this._createDocument().finally(() => {
      this._creationPromise = null;
    });
    return this._creationPromise;
  }

  private async _createDocument(): Promise<void> {
    this._logger.info("Creating offscreen document...");
    try {
      await chrome.offscreen.createDocument({
        url: OFFSCREEN_DOCUMENT_PATH,
        reasons: [
          chrome.offscreen.Reason.AUDIO_PLAYBACK,
          chrome.offscreen.Reason.DOM_PARSER,
          chrome.offscreen.Reason.WORKERS,
        ],
        justification: "To play silent audio for preventing throttling and to support background tasks.",
      });
      this._logger.info("Offscreen document created successfully.");
    } catch (error) {
      this._logger.error("Failed to create offscreen document:", error);
      throw error;
    }
  }

  private async _closeDocument(): Promise<void> {
    if (await this._hasDocument()) {
      // 在关闭文档之前先暂停音频，以避免错误
      try {
        await chrome.runtime.sendMessage({
          target: 'offscreen-audio',
          type: 'PAUSE_AUDIO',
        });
        this._logger.info('PAUSE_AUDIO message sent successfully.');
      } catch (e) {
        this._logger.warn('Failed to send PAUSE_AUDIO message. Document might already be closing.', e);
      }
      await chrome.offscreen.closeDocument();
      this._logger.info("Offscreen document closed.");
    }
  }
  
  private async _hasDocument(): Promise<boolean> {
    try {
      return await chrome.offscreen.hasDocument();
    } catch (e) {
      this._logger.warn('Failed to check for offscreen document, assuming none exists:', e);
      return false;
    }
  }
}

// 创建并导出一个单例
export const offscreenManager = new OffscreenManager(); 