/**
 * 定义了数据提取策略的通用接口。
 * 每个策略负责针对特定类型的页面执行最优的数据采集方案。
 */

/**
 * 提取策略的执行结果。
 * 根据策略的不同，它可能包含截图数据，也可能包含结构化数据。
 */
export type ExtractionResult = {
  success: boolean;
  data?: unknown; // 具体数据结构由策略定义
  error?: string;
};

export interface ExtractionStrategy {
  /**
   * 执行提取策略。
   * @param context - 页面执行上下文 (Window)。
   * @param taskId - 当前任务的ID。
   * @param sessionId - 当前捕获会话的ID。
   * @returns 返回一个包含提取结果的 Promise。
   */
  execute(context: Window, taskId: string, sessionId: string): Promise<ExtractionResult>;
} 