"""
数懒平台后端服务 - FastAPI应用入口
基于browser-viewer的微服务架构设计
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager
import uvicorn

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1 import auth, tasks, users, data, websocket, extension
from app.utils.logger import get_logger

logger = get_logger(__name__)

# 创建数据库表
Base.metadata.create_all(bind=engine)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("数懒平台后端服务启动中...")

    # 启动时的初始化工作
    try:
        # 初始化Redis连接
        from app.utils.redis_client import get_redis_client

        redis_client = await get_redis_client()
        await redis_client.ping()
        logger.info("Redis连接成功")

        # 初始化消息队列
        from app.utils.message_queue import get_task_message_queue

        task_mq = await get_task_message_queue()
        logger.info("消息队列初始化成功")

        logger.info("数懒平台后端服务启动完成")
        yield

    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

    finally:
        # 关闭时的清理工作
        logger.info("数懒平台后端服务关闭中...")
        try:
            from app.utils.redis_client import close_redis_client

            await close_redis_client()
            logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis失败: {e}")
        logger.info("数懒平台后端服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="数懒平台API",
    description="基于browser-viewer的智能浏览代理平台后端服务",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 注册API路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/v1/users", tags=["用户管理"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["任务管理"])
app.include_router(data.router, prefix="/api/v1/data", tags=["数据处理"])
app.include_router(websocket.router, prefix="/api/v1/ws", tags=["实时通信"])
app.include_router(extension.router, prefix="/api/v1", tags=["浏览器插件"])


@app.get("/")
async def root():
    """根路径 - 服务状态检查"""
    return {
        "service": "数懒平台后端服务",
        "version": "1.0.0",
        "status": "running",
        "description": "基于browser-viewer的智能浏览代理平台",
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        from app.core.database import get_db

        db = next(get_db())
        db.execute("SELECT 1")

        # 检查Redis连接
        from app.utils.redis_client import get_redis_client

        redis_client = await get_redis_client()
        await redis_client.ping()

        return {
            "status": "healthy",
            "database": "connected",
            "redis": "connected",
            "timestamp": "2025-07-14T00:00:00Z",
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info",
    )
