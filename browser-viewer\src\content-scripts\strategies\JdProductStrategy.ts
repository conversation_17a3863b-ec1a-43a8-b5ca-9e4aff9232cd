/**
 * 针对京东商品页面的特定提取策略。
 * 该策略通过直接操作DOM来提取结构化数据，而不是进行截图。
 */
import { HumanBehaviorSimulator } from '@/core/behavior-engine/HumanBehaviorSimulator';
import { Logger } from '@/shared/utils';
import { ExtractionResult, ExtractionStrategy } from './ExtractionStrategy';

export class JdProductStrategy implements ExtractionStrategy {
  private readonly simulator: HumanBehaviorSimulator;

  constructor(private readonly logger: Logger) {
    // 此处不注入MaskController，因为我们不需要在DOM提取时显示遮罩
    this.simulator = new HumanBehaviorSimulator('normal');
  }

  async execute(window: Window, taskId: string, sessionId: string): Promise<ExtractionResult> {
    this.logger.info(`Executing JdProductStrategy for session: ${sessionId}`);
    
    try {
      // 1. 使用HumanBehaviorSimulator滚动页面以确保所有内容（特别是图片）加载
      // 这个回调是空的，因为我们不需要在每次滚动时都截图
      await this.simulator.simulateFullPageScan(window, async () => {
        // We don't need to capture viewport for this strategy
        this.logger.debug('JdProductStrategy: Scrolled, but no capture needed.');
      });

      // 2. 页面滚动加载完毕后，开始提取结构化数据
      const doc = window.document;

      const title = (doc.querySelector('.sku-name')?.textContent ?? '').trim();
      const price = (doc.querySelector('.J-p-${priceId}')?.textContent ?? // 这是一个示例选择器，实际可能需要更新
                     doc.querySelector('.main-price .J-summary-price .p-price')?.textContent ??
                     '').trim();
      
      const imageUrls = Array.from(doc.querySelectorAll('#spec-list img, .ssd-module-wrap .J-detail-content-wrap img'))
        .map(img => (img as HTMLImageElement).src)
        .filter(src => src); // 过滤掉空的src

      const data = {
        platform: 'jd.com',
        title,
        price,
        imageUrls: [...new Set(imageUrls)], // 去重
      };
      
      this.logger.info(`Successfully extracted structured data for session ${sessionId}:`, data);
      return { success: true, data };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error executing JdProductStrategy for session ${sessionId}:`, errorMessage);
      return { success: false, error: errorMessage };
    }
  }
} 