/**
 * 全局测试设置文件
 * 配置 Vitest 全局测试环境
 */

import { vi } from 'vitest';

// 设置全局测试超时
vi.setConfig({
  testTimeout: 10000,
  hookTimeout: 10000,
});

// 模拟 performance API
Object.defineProperty(globalThis, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
  },
  writable: true,
});

// 模拟 ResizeObserver
Object.defineProperty(globalThis, 'ResizeObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
  writable: true,
});

// 模拟 IntersectionObserver
Object.defineProperty(globalThis, 'IntersectionObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
  writable: true,
});

// 模拟 MutationObserver
Object.defineProperty(globalThis, 'MutationObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(),
  })),
  writable: true,
});

// 模拟 crypto API
Object.defineProperty(globalThis, 'crypto', {
  value: {
    getRandomValues: vi.fn().mockImplementation((arr: any) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: vi.fn().mockImplementation(() => 
      'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      })
    ),
  },
  writable: true,
});

// 模拟 URL 构造函数
if (typeof URL === 'undefined') {
  Object.defineProperty(globalThis, 'URL', {
    value: class URL {
      constructor(public href: string, base?: string) {
        if (base) {
          this.href = new URL(href, base).href;
        }
      }
      toString() {
        return this.href;
      }
    },
    writable: true,
  });
}

// 模拟 structuredClone
if (typeof structuredClone === 'undefined') {
  Object.defineProperty(globalThis, 'structuredClone', {
    value: vi.fn().mockImplementation((obj: any) => JSON.parse(JSON.stringify(obj))),
    writable: true,
  });
}

// 设置全局变量
Object.defineProperty(globalThis, '__DEV__', {
  value: true,
  writable: true,
});

Object.defineProperty(globalThis, '__PROD__', {
  value: false,
  writable: true,
});

Object.defineProperty(globalThis, '__VERSION__', {
  value: '1.0.0-test',
  writable: true,
});

Object.defineProperty(globalThis, '__BUILD_TIME__', {
  value: new Date().toISOString(),
  writable: true,
});

// 测试环境标识
process.env.NODE_ENV = 'test';
process.env.VITEST = 'true';

// 清理函数
beforeEach(() => {
  vi.clearAllMocks();
  vi.clearAllTimers();
});

afterEach(() => {
  vi.restoreAllMocks();
});