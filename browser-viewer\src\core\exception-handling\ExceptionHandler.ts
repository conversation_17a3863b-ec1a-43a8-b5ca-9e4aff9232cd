/**
 * 异常处理系统
 * 
 * - 智能重试机制
 * - 异常分级处理
 * - 恢复策略优化
 */

import { TaskId } from '@/shared/types';
import { createLogger } from '@/shared/utils';
import { BehaviorSubject, Observable } from 'rxjs';

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  PARSING = 'parsing',
  SECURITY = 'security',
  TIMEOUT = 'timeout',
  RESOURCE = 'resource',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown'
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 重试策略配置
export interface RetryPolicy {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  baseDelay: number;
  maxDelay: number;
  retryableErrors: ErrorType[];
  jitter: boolean;
}

// 恢复动作类型
export enum RecoveryAction {
  RETRY = 'retry',
  SKIP = 'skip',
  ABORT = 'abort',
  FALLBACK = 'fallback',
  ESCALATE = 'escalate'
}

// 异常上下文信息
export interface ExceptionContext {
  taskId?: TaskId;
  url?: string;
  operation: string;
  timestamp: number;
  userAgent?: string;
  tabId?: number;
  stackTrace?: string;
  metadata?: Record<string, any>;
}

// 异常信息
export interface Exception {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context: ExceptionContext;
  retryCount: number;
  recoverable: boolean;
  timestamp: number;
}

// 恢复结果
export interface RecoveryResult {
  action: RecoveryAction;
  success: boolean;
  message?: string;
  newContext?: Partial<ExceptionContext>;
  retryAfter?: number;
}

// 异常统计信息
export interface ExceptionStats {
  totalExceptions: number;
  exceptionsByType: Record<ErrorType, number>;
  exceptionsBySeverity: Record<ErrorSeverity, number>;
  recoverySuccessRate: number;
  averageRecoveryTime: number;
  topFailureReasons: Array<{ reason: string; count: number }>;
}

// 异常处理器接口
export interface ExceptionHandler {
  /**
   * 检查是否可以处理此异常
   */
  canHandle(exception: Exception): boolean;
  
  /**
   * 处理异常并返回恢复动作
   */
  handle(exception: Exception): RecoveryResult;
  
  /**
   * 获取处理器的优先级（数字越小优先级越高）
   */
  getPriority(): number;
  
  /**
   * 获取处理器名称
   */
  getName(): string;
}

// 异常监听器接口
export interface ExceptionListener {
  onExceptionOccurred(exception: Exception): void;
  onRecoveryAttempted(exception: Exception, result: RecoveryResult): void;
  onRecoveryCompleted(exception: Exception, finalResult: RecoveryResult): void;
}

/**
 * 网络异常处理器
 */
export class NetworkExceptionHandler implements ExceptionHandler {
  private readonly _retryPolicy: RetryPolicy = {
    maxRetries: 3,
    backoffStrategy: 'exponential',
    baseDelay: 1000,
    maxDelay: 10000,
    retryableErrors: [ErrorType.NETWORK, ErrorType.TIMEOUT],
    jitter: true
  };

  canHandle(exception: Exception): boolean {
    return exception.type === ErrorType.NETWORK || exception.type === ErrorType.TIMEOUT;
  }

  handle(exception: Exception): RecoveryResult {
    // 检查是否应该重试
    if (exception.retryCount >= this._retryPolicy.maxRetries) {
      return {
        action: RecoveryAction.ABORT,
        success: false,
        message: '已达到最大重试次数'
      };
    }

    // 计算退避延迟
    const delay = this._calculateBackoffDelay(exception.retryCount);
    
    return {
      action: RecoveryAction.RETRY,
      success: true,
      message: `网络错误，${delay}ms后重试`,
      retryAfter: delay
    };
  }

  getPriority(): number {
    return 1;
  }

  getName(): string {
    return 'NetworkExceptionHandler';
  }

  private _calculateBackoffDelay(retryCount: number): number {
    let delay = this._retryPolicy.baseDelay;
    
    switch (this._retryPolicy.backoffStrategy) {
      case 'linear':
        delay = this._retryPolicy.baseDelay * (retryCount + 1);
        break;
      case 'exponential':
        delay = this._retryPolicy.baseDelay * Math.pow(2, retryCount);
        break;
      case 'fixed':
        delay = this._retryPolicy.baseDelay;
        break;
    }
    
    // 应用最大延迟限制
    delay = Math.min(delay, this._retryPolicy.maxDelay);
    
    // 应用抖动
    if (this._retryPolicy.jitter) {
      delay += Math.random() * 1000;
    }
    
    return Math.floor(delay);
  }
}

/**
 * 安全异常处理器
 */
export class SecurityExceptionHandler implements ExceptionHandler {
  canHandle(exception: Exception): boolean {
    return exception.type === ErrorType.SECURITY || exception.type === ErrorType.PERMISSION;
  }

  handle(exception: Exception): RecoveryResult {
    if (exception.severity === ErrorSeverity.CRITICAL) {
      return {
        action: RecoveryAction.ABORT,
        success: false,
        message: '严重安全错误，任务已终止'
      };
    }

    return {
      action: RecoveryAction.SKIP,
      success: true,
      message: '安全限制，跳过当前操作'
    };
  }

  getPriority(): number {
    return 0; // 最高优先级
  }

  getName(): string {
    return 'SecurityExceptionHandler';
  }
}

/**
 * 解析异常处理器
 */
export class ParsingExceptionHandler implements ExceptionHandler {
  canHandle(exception: Exception): boolean {
    return exception.type === ErrorType.PARSING;
  }

  handle(exception: Exception): RecoveryResult {
    // 尝试使用备用解析策略
    return {
      action: RecoveryAction.FALLBACK,
      success: true,
      message: '使用备用解析策略',
      newContext: {
        operation: `${exception.context.operation}_fallback`
      }
    };
  }

  getPriority(): number {
    return 2;
  }

  getName(): string {
    return 'ParsingExceptionHandler';
  }
}

/**
 * 默认异常处理器
 */
export class DefaultExceptionHandler implements ExceptionHandler {
  canHandle(_exception: Exception): boolean {
    return true; // 处理所有未被其他处理器处理的异常
  }

  handle(exception: Exception): RecoveryResult {
    if (exception.severity === ErrorSeverity.CRITICAL) {
      return {
        action: RecoveryAction.ABORT,
        success: false,
        message: '严重错误，任务终止'
      };
    }

    if (exception.retryCount < 1) {
      return {
        action: RecoveryAction.RETRY,
        success: true,
        message: '未知错误，尝试重试',
        retryAfter: 2000
      };
    }

    return {
      action: RecoveryAction.SKIP,
      success: true,
      message: '跳过当前操作'
    };
  }

  getPriority(): number {
    return 999; // 最低优先级
  }

  getName(): string {
    return 'DefaultExceptionHandler';
  }
}

/**
 * 增强的异常处理引擎
 */
export class EnhancedExceptionHandler {
  private readonly _logger = createLogger('EnhancedExceptionHandler');
  private readonly _handlers: ExceptionHandler[] = [];
  private readonly _listeners: ExceptionListener[] = [];
  private readonly _exceptions = new Map<string, Exception>();
  private readonly _exceptionSubject = new BehaviorSubject<Exception[]>([]);
  
  // 统计信息
  private _stats: ExceptionStats = {
    totalExceptions: 0,
    exceptionsByType: {} as Record<ErrorType, number>,
    exceptionsBySeverity: {} as Record<ErrorSeverity, number>,
    recoverySuccessRate: 0,
    averageRecoveryTime: 0,
    topFailureReasons: []
  };

  constructor() {
    // 注册默认处理器
    this.registerHandler(new SecurityExceptionHandler());
    this.registerHandler(new NetworkExceptionHandler());
    this.registerHandler(new ParsingExceptionHandler());
    this.registerHandler(new DefaultExceptionHandler());
  }

  /**
   * 注册异常处理器
   */
  registerHandler(handler: ExceptionHandler): void {
    this._handlers.push(handler);
    // 按优先级排序
    this._handlers.sort((a, b) => a.getPriority() - b.getPriority());
    this._logger.info(`异常处理器已注册: ${handler.getName()}`);
  }

  /**
   * 注册异常监听器
   */
  registerListener(listener: ExceptionListener): void {
    this._listeners.push(listener);
  }

  /**
   * 处理异常
   */
  handleException(
    error: Error | string,
    context: ExceptionContext,
    type?: ErrorType,
    severity?: ErrorSeverity
  ): RecoveryResult {
    const exception = this._createException(error, context, type, severity);
    
    // 通知监听器
    this._notifyListeners('occurred', exception);
    
    // 更新统计信息
    this._updateStats(exception);
    
    // 查找合适的处理器
    const handler = this._findHandler(exception);
    if (!handler) {
      this._logger.error('未找到合适的异常处理器', exception);
      return {
        action: RecoveryAction.ABORT,
        success: false,
        message: '无法处理的异常'
      };
    }

    try {
      const startTime = Date.now();
      const result = handler.handle(exception);
      const recoveryTime = Date.now() - startTime;
      
      // 更新异常状态
      exception.retryCount++;
      this._exceptions.set(exception.id, exception);
      this._updateExceptionsList();
      
      // 通知监听器
      this._notifyListeners('attempted', exception, result);
      
      // 更新统计信息
      this._updateRecoveryStats(result, recoveryTime);
      
      this._logger.info(`异常处理完成: ${exception.id}`, {
        handler: handler.getName(),
        action: result.action,
        success: result.success
      });
      
      return result;
    } catch (handlingError) {
      this._logger.error('异常处理器执行失败', handlingError);
      return {
        action: RecoveryAction.ABORT,
        success: false,
        message: '异常处理器执行失败'
      };
    }
  }

  /**
   * 批量处理异常
   */
  async handleBatchExceptions(
    exceptions: Array<{ error: Error | string; context: ExceptionContext }>
  ): Promise<RecoveryResult[]> {
    const results = await Promise.all(
      exceptions.map(({ error, context }) => this.handleException(error, context))
    );
    
    this._logger.info(`批量处理 ${exceptions.length} 个异常完成`);
    return results;
  }

  /**
   * 获取异常历史
   */
  getExceptionHistory(): Observable<Exception[]> {
    return this._exceptionSubject.asObservable();
  }

  /**
   * 获取特定任务的异常
   */
  getTaskExceptions(taskId: TaskId): Exception[] {
    return Array.from(this._exceptions.values())
      .filter(ex => ex.context.taskId === taskId);
  }

  /**
   * 获取统计信息
   */
  getStats(): ExceptionStats {
    return { ...this._stats };
  }

  /**
   * 清理旧异常记录
   */
  cleanupOldExceptions(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoffTime = Date.now() - maxAge;
    const toDelete: string[] = [];
    
    for (const [id, exception] of this._exceptions.entries()) {
      if (exception.timestamp < cutoffTime) {
        toDelete.push(id);
      }
    }
    
    toDelete.forEach(id => this._exceptions.delete(id));
    this._updateExceptionsList();
    
    this._logger.info(`清理了 ${toDelete.length} 个旧异常记录`);
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this._stats = {
      totalExceptions: 0,
      exceptionsByType: {} as Record<ErrorType, number>,
      exceptionsBySeverity: {} as Record<ErrorSeverity, number>,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0,
      topFailureReasons: []
    };
  }

  // 私有方法
  private _createException(
    error: Error | string,
    context: ExceptionContext,
    type?: ErrorType,
    severity?: ErrorSeverity
  ): Exception {
    const id = `ex_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const message = typeof error === 'string' ? error : error.message;
    
    const exception: Exception = {
      id,
      type: type ?? this._inferErrorType(error),
      severity: severity ?? this._inferSeverity(error, context),
      message,
      originalError: typeof error === 'string' ? undefined : error,
      context: {
        ...context,
        timestamp: Date.now(),
        stackTrace: typeof error === 'string' ? undefined : error.stack
      },
      retryCount: 0,
      recoverable: this._isRecoverable(error, context),
      timestamp: Date.now()
    };
    
    this._exceptions.set(id, exception);
    this._updateExceptionsList();
    
    return exception;
  }

  private _findHandler(exception: Exception): ExceptionHandler | null {
    return this._handlers.find(handler => handler.canHandle(exception)) || null;
  }

  private _inferErrorType(error: Error | string): ErrorType {
    const message = typeof error === 'string' ? error : error.message;
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (lowerMessage.includes('timeout')) {
      return ErrorType.TIMEOUT;
    }
    if (lowerMessage.includes('security') || lowerMessage.includes('permission')) {
      return ErrorType.SECURITY;
    }
    if (lowerMessage.includes('parse') || lowerMessage.includes('json')) {
      return ErrorType.PARSING;
    }
    
    return ErrorType.UNKNOWN;
  }

  private _inferSeverity(error: Error | string, _context: ExceptionContext): ErrorSeverity {
    const message = typeof error === 'string' ? error : error.message;
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('critical') || lowerMessage.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (lowerMessage.includes('security') || lowerMessage.includes('permission')) {
      return ErrorSeverity.HIGH;
    }
    if (lowerMessage.includes('timeout') || lowerMessage.includes('network')) {
      return ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.LOW;
  }

  private _isRecoverable(error: Error | string, _context: ExceptionContext): boolean {
    const message = typeof error === 'string' ? error : error.message;
    const lowerMessage = message.toLowerCase();
    
    // 不可恢复的错误类型
    if (lowerMessage.includes('security') || lowerMessage.includes('critical')) {
      return false;
    }
    
    return true;
  }

  private _updateStats(exception: Exception): void {
    this._stats.totalExceptions++;
    
    // 按类型统计
    this._stats.exceptionsByType[exception.type] = 
      (this._stats.exceptionsByType[exception.type] || 0) + 1;
    
    // 按严重级别统计
    this._stats.exceptionsBySeverity[exception.severity] = 
      (this._stats.exceptionsBySeverity[exception.severity] || 0) + 1;
  }

  private _updateRecoveryStats(result: RecoveryResult, recoveryTime: number): void {
    // 更新平均恢复时间
    this._stats.averageRecoveryTime = 
      (this._stats.averageRecoveryTime + recoveryTime) / 2;
    
    // 更新恢复成功率
    const totalRecoveries = this._stats.totalExceptions;
    const successfulRecoveries = result.success ? 1 : 0;
    this._stats.recoverySuccessRate = 
      (this._stats.recoverySuccessRate * (totalRecoveries - 1) + successfulRecoveries) / totalRecoveries;
  }

  private _updateExceptionsList(): void {
    const exceptions = Array.from(this._exceptions.values())
      .sort((a, b) => b.timestamp - a.timestamp);
    this._exceptionSubject.next(exceptions);
  }

  private _notifyListeners(
    event: 'occurred' | 'attempted' | 'completed',
    exception: Exception,
    result?: RecoveryResult
  ): void {
    this._listeners.forEach(listener => {
      try {
        switch (event) {
          case 'occurred':
            listener.onExceptionOccurred(exception);
            break;
          case 'attempted':
            if (result) listener.onRecoveryAttempted(exception, result);
            break;
          case 'completed':
            if (result) listener.onRecoveryCompleted(exception, result);
            break;
        }
      } catch (error) {
        this._logger.error('异常监听器执行失败', error);
      }
    });
  }
}