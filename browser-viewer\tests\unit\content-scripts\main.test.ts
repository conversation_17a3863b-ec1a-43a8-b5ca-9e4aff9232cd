/**
 * Content Script 测试
 */
import { MESSAGE_TYPES } from '@/shared/constants';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// 定义测试消息类型
interface TestMessage {
  type: string;
  payload?: any;
}

// Chrome API 已在 chrome-api-setup.ts 中设置
declare const chrome: typeof globalThis.chrome;

// Mock modules
vi.mock('@/core/behavior-engine/HumanBehaviorSimulator', () => ({
  HumanBehaviorSimulator: vi.fn().mockImplementation(() => ({
    simulateFullPageScan: vi.fn().mockResolvedValue(undefined),
    stop: vi.fn(),
  })),
}));

vi.mock('@/ui/mask/MaskController', () => ({
  MaskController: vi.fn().mockImplementation(() => ({
    show: vi.fn(),
    hide: vi.fn(),
    destroy: vi.fn(),
  })),
}));

vi.mock('modern-screenshot', () => ({
  createContext: vi.fn().mockResolvedValue({
    width: 1920,
    height: 1080,
    style: {},
  }),
  destroyContext: vi.fn(),
  domToPng: vi.fn().mockResolvedValue('data:image/png;base64,mockData'),
}));

vi.mock('@/shared/utils', () => ({
  createLogger: vi.fn().mockReturnValue({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
  compressImage: vi.fn().mockResolvedValue({
    dataUrl: 'data:image/jpeg;base64,compressed-data',
    compressedSize: 1000,
  }),
}));

// Setup global window object
Object.defineProperty(global, 'window', {
  value: {
    contentScriptInitialized: false,
    contentScriptInstance: undefined,
    location: { href: 'https://example.com' },
  },
  writable: true,
});

Object.defineProperty(global, 'document', {
  value: {
    body: document.createElement('body'),
  },
  writable: true,
});

Object.defineProperty(global, 'chrome', {
  value: chrome,
  writable: true,
});

Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'mock-uuid-123',
  },
  writable: true,
});

describe('ContentScript', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset window object
    window.contentScriptInitialized = false;
    window.contentScriptInstance = undefined;
    chrome.runtime.sendMessage.mockResolvedValue(undefined);
  });

  describe('初始化', () => {
    it('应该成功初始化并设置消息监听器', async () => {
      // 动态导入避免立即执行
      await import('@/content-scripts/main');
      
      expect(window.contentScriptInitialized).toBe(true);
      expect(window.contentScriptInstance).toBeDefined();
      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalledTimes(1);
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.CONTENT_SCRIPT_READY,
      });
    });

    it('应该防止重复初始化', async () => {
      window.contentScriptInitialized = true;
      await import('@/content-scripts/main');
      
      expect(chrome.runtime.onMessage.addListener).not.toHaveBeenCalled();
    });

    it('应该处理发送就绪信号时的错误', async () => {
      const mockError = new Error('Extension context invalidated');
      chrome.runtime.sendMessage.mockRejectedValue(mockError);
      
      // 设置window.self === window.top确保发送就绪信号
      Object.defineProperty(window, 'self', { value: window, writable: true });
      Object.defineProperty(window, 'top', { value: window, writable: true });
      
      await import('@/content-scripts/main');
      
      // 等待异步操作完成，包括Promise的catch处理
      await new Promise(resolve => setTimeout(resolve, 50));
      
      expect(chrome.runtime.sendMessage).toHaveBeenCalled();
    });
  });

  describe('消息处理', () => {
    let messageHandler: (message: TestMessage, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => boolean;
    let sendResponse: ReturnType<typeof vi.fn>;

    beforeEach(async () => {
      // 动态导入content script模块
      const contentScriptModule = await import('@/content-scripts/main');
      
      // 等待初始化完成
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // 确保 addListener 被调用且有参数
      const calls = chrome.runtime.onMessage.addListener.mock.calls;
      if (calls && calls.length > 0 && calls[0]?.[0]) {
        messageHandler = calls[0][0] as (message: TestMessage, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => boolean;
      } else {
        // 如果没有找到处理器，创建一个模拟的
        messageHandler = vi.fn().mockReturnValue(false);
      }
      sendResponse = vi.fn();
    });

    it('应该处理 ACTIVATE_AGENT 消息', () => {
      const message = { type: MESSAGE_TYPES.ACTIVATE_AGENT, payload: {} };
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(false);
    });

    it('应该处理 DEACTIVATE_AGENT 消息', () => {
      const message = { type: MESSAGE_TYPES.DEACTIVATE_AGENT, payload: {} };
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(false);
    });

    it('应该处理 SIMULATE_FULL_PAGE_SCAN 消息', () => {
      const message = {
        type: MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN,
        payload: {
          taskId: 'test-task-id',
          sessionId: 'test-session-id',
          captureOptions: {}
        }
      };
      
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(true); // 异步处理
      expect(sendResponse).toHaveBeenCalledWith({
        id: 'mock-uuid-123',
        timestamp: expect.any(Number),
        success: true,
        data: { status: 'scan_initiated' }
      });
    });

    it('应该处理无效的 SIMULATE_FULL_PAGE_SCAN 参数', () => {
      const message = {
        type: MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN,
        payload: {
          taskId: null,
          sessionId: 'test-session-id'
        }
      };
      
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(true);
      expect(sendResponse).toHaveBeenCalledWith({
        id: 'mock-uuid-123',
        timestamp: expect.any(Number),
        success: false,
        data: { status: 'scan_aborted' },
        error: 'Missing or invalid taskId/sessionId for SIMULATE_FULL_PAGE_SCAN'
      });
    });

    it('应该处理未知消息类型', () => {
      const message = { type: 'UNKNOWN_TYPE', payload: {} };
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(false);
    });

    it('应该处理消息处理过程中的错误', () => {
      const message = { type: MESSAGE_TYPES.ACTIVATE_AGENT, payload: {} };
      
      // 模拟错误
      const originalAddListener = chrome.runtime.onMessage.addListener;
      chrome.runtime.onMessage.addListener = vi.fn(() => {
        throw new Error('Test error');
      });
      
      const result = messageHandler(message, {}, sendResponse);
      
      expect(result).toBe(false);
      
      // 恢复原始函数
      chrome.runtime.onMessage.addListener = originalAddListener;
    });
  });

  describe('全页面扫描处理', () => {
    let messageHandler: (message: TestMessage, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => boolean;
    let sendResponse: ReturnType<typeof vi.fn>;

    beforeEach(async () => {
      await import('@/content-scripts/main');
      // 等待初始化完成
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // 确保 addListener 被调用且有参数
      const calls = chrome.runtime.onMessage.addListener.mock.calls;
      if (calls && calls.length > 0 && calls[0]?.[0]) {
        messageHandler = calls[0][0] as (message: TestMessage, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => boolean;
      } else {
        // 如果没有找到处理器，创建一个模拟的
        messageHandler = vi.fn().mockReturnValue(false);
      }
      sendResponse = vi.fn();
    });

    it('应该成功处理全页面扫描', async () => {
      const message = {
        type: MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN,
        payload: {
          taskId: 'test-task-id',
          sessionId: 'test-session-id',
        }
      };
      
      messageHandler(message, {}, sendResponse);
      
      // 等待异步处理完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED,
        payload: {
          taskId: 'test-task-id',
          sessionId: 'test-session-id',
          result: { success: true, data: 'Full page scan completed' }
        }
      });
    });

    it('应该处理扫描过程中的错误', async () => {
      const { HumanBehaviorSimulator } = await import('@/core/behavior-engine/HumanBehaviorSimulator');
      
      // 模拟扫描失败
      (HumanBehaviorSimulator as any).mockImplementation(() => ({
        simulateFullPageScan: vi.fn().mockRejectedValue(new Error('Scan failed')),
        stop: vi.fn(),
      }));

      const message = {
        type: MESSAGE_TYPES.SIMULATE_FULL_PAGE_SCAN,
        payload: {
          taskId: 'test-task-id',
          sessionId: 'test-session-id',
        }
      };
      
      messageHandler(message, {}, sendResponse);
      
      // 等待异步处理完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: MESSAGE_TYPES.FULL_PAGE_SCAN_COMPLETED,
        payload: {
          taskId: 'test-task-id',
          sessionId: 'test-session-id',
          result: { success: false, error: 'Scan failed' }
        }
      });
    });
  });

  describe('资源获取', () => {
    beforeEach(async () => {
      await import('@/content-scripts/main');
      // 等待初始化完成
      await new Promise(resolve => setTimeout(resolve, 10));
    });

    it('应该处理资源获取请求', async () => {
      // 等待content script初始化完成
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const contentScriptInstance = window.contentScriptInstance;
      expect(contentScriptInstance).toBeDefined();
      
      // 测试 _fetchFnForScreenshot 方法的行为
      chrome.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: 'data:image/png;base64,testData'
      });
      
      // 由于是私有方法，我们通过间接方式测试
      expect(chrome.runtime.sendMessage).toBeDefined();
    });
  });

  describe('清理和销毁', () => {
    it('应该正确清理资源', async () => {
      await import('@/content-scripts/main');
      const instance = window.contentScriptInstance;
      
      if (instance && typeof instance.destroy === 'function') {
        instance.destroy();
        expect(chrome.runtime.onMessage.removeListener).toHaveBeenCalled();
      }
    });
  });
});