"""
任务编排服务
基于browser-viewer的TaskScheduler设计模式
"""

from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
import asyncio
import uuid
from datetime import datetime

from app.models.task import Task, TaskStatus
from app.schemas.task import TaskCreateRequest, TaskResponse, BatchResponse, TaskMetrics
from app.utils.message_queue import MessageQueue
from app.utils.logger import get_logger

logger = get_logger(__name__)


class TaskOrchestratorService:
    """任务编排服务 - 基于browser-viewer的TaskScheduler设计模式"""

    def __init__(self, db: Session):
        self.db = db
        self.mq = MessageQueue()

    async def create_task(
        self, request: TaskCreateRequest, user_id: str
    ) -> TaskResponse:
        """创建新任务"""
        try:
            task = Task(
                id=str(uuid.uuid4()),
                user_id=user_id,
                name=request.name,
                description=request.description,
                config=request.config.dict() if request.config else {},
                status=TaskStatus.PENDING,
                created_at=datetime.utcnow(),
            )

            self.db.add(task)
            self.db.commit()
            self.db.refresh(task)

            # 发送到任务队列
            await self.mq.publish(
                "task.created",
                {"task_id": task.id, "user_id": user_id, "config": task.config},
            )

            logger.info(f"Task created: {task.id}")
            return TaskResponse.from_orm(task)

        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            self.db.rollback()
            raise HTTPException(500, f"Failed to create task: {str(e)}")

    async def submit_batch(
        self, configs: List[TaskCreateRequest], user_id: str
    ) -> BatchResponse:
        """批量提交任务 - 继承browser-viewer的批处理设计"""
        batch_id = str(uuid.uuid4())
        tasks = []

        try:
            for config in configs:
                task = Task(
                    id=str(uuid.uuid4()),
                    batch_id=batch_id,
                    user_id=user_id,
                    name=config.name,
                    description=config.description,
                    config=config.config.dict() if config.config else {},
                    status=TaskStatus.PENDING,
                    created_at=datetime.utcnow(),
                )
                tasks.append(task)

            self.db.add_all(tasks)
            self.db.commit()

            # 批量发送到队列
            for task in tasks:
                await self.mq.publish(
                    "task.created",
                    {
                        "task_id": task.id,
                        "batch_id": batch_id,
                        "user_id": user_id,
                        "config": task.config,
                    },
                )

            logger.info(f"Batch created: {batch_id} with {len(tasks)} tasks")
            return BatchResponse(batch_id=batch_id, task_count=len(tasks))

        except Exception as e:
            logger.error(f"Failed to create batch: {e}")
            self.db.rollback()
            raise HTTPException(500, f"Failed to create batch: {str(e)}")

    async def start_task(self, task_id: str, user_id: str) -> None:
        """启动任务"""
        try:
            task = (
                self.db.query(Task)
                .filter(Task.id == task_id, Task.user_id == user_id)
                .first()
            )

            if not task:
                raise HTTPException(404, "Task not found")

            if task.status != TaskStatus.PENDING:
                raise HTTPException(
                    400, f"Task is not in pending status: {task.status}"
                )

            task.status = TaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            task.updated_at = datetime.utcnow()
            self.db.commit()

            # 发送启动消息
            await self.mq.publish(
                "task.start", {"task_id": task_id, "user_id": user_id}
            )

            logger.info(f"Task started: {task_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to start task: {e}")
            self.db.rollback()
            raise HTTPException(500, f"Failed to start task: {str(e)}")

    async def pause_task(self, task_id: str, user_id: str) -> None:
        """暂停任务"""
        try:
            task = (
                self.db.query(Task)
                .filter(Task.id == task_id, Task.user_id == user_id)
                .first()
            )

            if not task:
                raise HTTPException(404, "Task not found")

            if task.status != TaskStatus.RUNNING:
                raise HTTPException(400, f"Task is not running: {task.status}")

            task.status = TaskStatus.PAUSED
            task.updated_at = datetime.utcnow()
            self.db.commit()

            # 发送暂停消息
            await self.mq.publish(
                "task.pause", {"task_id": task_id, "user_id": user_id}
            )

            logger.info(f"Task paused: {task_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to pause task: {e}")
            self.db.rollback()
            raise HTTPException(500, f"Failed to pause task: {str(e)}")

    async def cancel_task(self, task_id: str, user_id: str) -> None:
        """取消任务"""
        try:
            task = (
                self.db.query(Task)
                .filter(Task.id == task_id, Task.user_id == user_id)
                .first()
            )

            if not task:
                raise HTTPException(404, "Task not found")

            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                raise HTTPException(400, f"Cannot cancel completed task: {task.status}")

            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            task.updated_at = datetime.utcnow()
            self.db.commit()

            # 发送取消消息
            await self.mq.publish(
                "task.cancel", {"task_id": task_id, "user_id": user_id}
            )

            logger.info(f"Task cancelled: {task_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to cancel task: {e}")
            self.db.rollback()
            raise HTTPException(500, f"Failed to cancel task: {str(e)}")

    async def get_task_metrics(self, task_id: str, user_id: str) -> TaskMetrics:
        """获取任务指标"""
        try:
            task = (
                self.db.query(Task)
                .filter(Task.id == task_id, Task.user_id == user_id)
                .first()
            )

            if not task:
                raise HTTPException(404, "Task not found")

            # 计算任务指标
            duration = None
            if task.started_at:
                end_time = task.completed_at or datetime.utcnow()
                duration = (end_time - task.started_at).total_seconds()

            return TaskMetrics(
                task_id=task_id,
                status=task.status,
                progress=task.progress or 0.0,
                duration=duration,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get task metrics: {e}")
            raise HTTPException(500, f"Failed to get task metrics: {str(e)}")

    async def update_task_progress(
        self, task_id: str, progress: float, message: str = None
    ) -> None:
        """更新任务进度"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return

            task.progress = progress
            task.updated_at = datetime.utcnow()

            if message:
                task.status_message = message

            self.db.commit()

            # 发送进度更新消息
            await self.mq.publish(
                "task.progress",
                {"task_id": task_id, "progress": progress, "message": message},
            )

        except Exception as e:
            logger.error(f"Failed to update task progress: {e}")
            self.db.rollback()

    async def complete_task(
        self, task_id: str, result: dict = None, error: str = None
    ) -> None:
        """完成任务"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return

            if error:
                task.status = TaskStatus.FAILED
                task.error_message = error
            else:
                task.status = TaskStatus.COMPLETED
                task.progress = 100.0

            if result:
                task.result = result

            task.completed_at = datetime.utcnow()
            task.updated_at = datetime.utcnow()
            self.db.commit()

            # 发送完成消息
            await self.mq.publish(
                "task.completed",
                {
                    "task_id": task_id,
                    "status": task.status.value,
                    "result": result,
                    "error": error,
                },
            )

            logger.info(f"Task completed: {task_id} with status {task.status}")

        except Exception as e:
            logger.error(f"Failed to complete task: {e}")
            self.db.rollback()
