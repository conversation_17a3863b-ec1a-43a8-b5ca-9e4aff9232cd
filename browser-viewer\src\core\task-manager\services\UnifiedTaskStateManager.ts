/**
 * 基于统一状态存储的任务状态管理器
 * 
 * - 事务性状态操作
 * - 状态订阅机制
 * - 冲突解决策略
 */

import { TaskConfig, TaskId, TaskMetrics } from '@/shared/types';
import { createLogger, getCurrentTimestamp } from '@/shared/utils';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ConflictResolution, StateStore } from '../../state-management/interfaces/StateStore';
import { TaskStateManager } from '../interfaces';

// 状态键常量
const STATE_KEYS = {
  TASK_METRICS: 'task_metrics',
  PENDING_QUEUE: 'pending_queue',
  BATCH_METADATA: 'batch_metadata'
} as const;

// 批处理元数据
interface BatchMetadata {
  batchId: string;
  createdAt: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  totalTasks: number;
  completedTasks: number;
}

/**
 * 统一状态存储的任务状态管理器实现
 */
export class UnifiedTaskStateManager implements TaskStateManager {
  private readonly _logger = createLogger('UnifiedTaskStateManager');
  private readonly _taskMetrics$ = new BehaviorSubject<Map<TaskId, TaskMetrics>>(new Map());
  private readonly _stateStore: StateStore;

  constructor(stateStore: StateStore) {
    this._stateStore = stateStore;
    
    // 订阅状态变更
    this._stateStore.subscribe(STATE_KEYS.TASK_METRICS, (event) => {
      if (event.newValue) {
        const metricsMap = new Map(Object.entries(event.newValue));
        this._taskMetrics$.next(metricsMap);
      }
    });
    
    // 初始化时加载现有状态
    void this._initializeState();
  }

  getTaskMetrics$(): Observable<TaskMetrics[]> {
    return this._taskMetrics$.asObservable().pipe(
      map(metricsMap => Array.from(metricsMap.values()))
    );
  }

  async updateTaskMetrics(taskId: TaskId, metrics: Partial<TaskMetrics>): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      const existingMetrics = currentMetrics.get(taskId);
      
      if (existingMetrics) {
        const updatedMetrics: TaskMetrics = {
          ...existingMetrics,
          ...metrics,
        };
        
        currentMetrics.set(taskId, updatedMetrics);
        this._taskMetrics$.next(new Map(currentMetrics));
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        
        this._logger.debug(`任务 ${taskId} 的指标已更新`);
      } else {
        this._logger.warn(`尝试更新不存在任务的指标: ${taskId}`);
      }
    });
  }

  async addTaskMetrics(taskId: TaskId, metrics: TaskMetrics): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      
      if (currentMetrics.has(taskId)) {
        this._logger.warn(`任务 ${taskId} 的指标已存在，将被覆盖`);
      }
      
      currentMetrics.set(taskId, metrics);
      this._taskMetrics$.next(new Map(currentMetrics));
      ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
      
      this._logger.debug(`任务 ${taskId} 的指标已添加`);
    });
  }

  async removeTaskMetrics(taskId: TaskId): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      
      if (currentMetrics.has(taskId)) {
        currentMetrics.delete(taskId);
        this._taskMetrics$.next(new Map(currentMetrics));
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        
        this._logger.debug(`任务 ${taskId} 的指标已移除`);
      } else {
        this._logger.warn(`尝试移除不存在的任务指标: ${taskId}`);
      }
    });
  }

  getTaskMetrics(taskId: TaskId): TaskMetrics | null {
    return this._taskMetrics$.value.get(taskId) || null;
  }

  getAllTaskMetrics(): TaskMetrics[] {
    return Array.from(this._taskMetrics$.value.values());
  }

  async saveState(tasks: TaskMetrics[]): Promise<void> {
    try {
      // 只保存待处理的任务配置
      const pendingConfigs = tasks
        .filter(task => task.status === 'pending' && task.config)
        .map(task => task.config)
        .filter((config): config is TaskConfig => config !== undefined);

      await this._stateStore.save(STATE_KEYS.PENDING_QUEUE, pendingConfigs, ConflictResolution.OVERWRITE);
      
      this._logger.info(`已保存 ${pendingConfigs.length} 个待处理任务`);
    } catch (error) {
      this._logger.error('保存任务状态失败:', error);
    }
  }

  async restoreState(): Promise<TaskConfig[]> {
    try {
      const restoredConfigs = await this._stateStore.load<TaskConfig[]>(STATE_KEYS.PENDING_QUEUE);
      
      if (!restoredConfigs) {
        this._logger.info('未找到先前的任务状态');
        return [];
      }
      
      // 验证恢复的配置
      const validatedConfigs = restoredConfigs.filter(config => {
        if (!config || !Array.isArray(config.urls) || config.urls.length === 0) {
          this._logger.warn('发现无效的任务配置，已丢弃:', config);
          return false;
        }
        return true;
      });

      this._logger.info(`成功恢复 ${validatedConfigs.length} 个任务配置`);
      return validatedConfigs;
    } catch (error) {
      this._logger.error('恢复任务状态失败:', error);
      return [];
    }
  }

  async clearState(): Promise<void> {
    try {
      await this._stateStore.batch([
        { type: 'delete', key: STATE_KEYS.TASK_METRICS },
        { type: 'delete', key: STATE_KEYS.PENDING_QUEUE },
        { type: 'delete', key: STATE_KEYS.BATCH_METADATA }
      ]);
      
      this._taskMetrics$.next(new Map());
      this._logger.info('任务状态已清空');
    } catch (error) {
      this._logger.error('清空任务状态失败:', error);
    }
  }

  async createPendingTaskMetrics(configs: TaskConfig[]): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      
      for (const config of configs) {
        if (config.batchId && config.urls && config.urls.length > 0) {
          for (const url of config.urls) {
            // 检查是否已存在相同的待处理任务
            const existingMetric = Array.from(currentMetrics.values()).find(m =>
              m.status === 'pending' &&
              m.config &&
              m.config.batchId === config.batchId &&
              m.config.urls[0] === url
            );

            if (!existingMetric) {
              const tempId = `pending-${config.batchId}-${url}-${Math.random()}`;
              const singleUrlConfig: TaskConfig = {
                ...config,
                urls: [url],
              };

              const pendingMetrics: TaskMetrics = {
                id: tempId,
                batchId: config.batchId,
                status: 'pending',
                startTime: getCurrentTimestamp(),
                totalPages: 1,
                processedPages: 0,
                config: singleUrlConfig,
                endTime: undefined,
                cpuUsage: 0,
                memoryUsage: 0,
                networkUsage: 0,
                errors: []
              };

              currentMetrics.set(tempId, pendingMetrics);
              this._logger.debug(`创建待处理任务指标: ${url}`);
            }
          }
        }
      }

      ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
    });
  }

  async removePendingTaskMetrics(batchId: string, url: string): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      const entryToRemove = Array.from(currentMetrics.entries()).find(
        ([, metric]) =>
          metric.status === 'pending' &&
          metric.config &&
          metric.config.batchId === batchId &&
          metric.config.urls[0] === url
      );

      if (entryToRemove) {
        currentMetrics.delete(entryToRemove[0]);
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        
        this._logger.debug(`移除待处理任务指标: ${url}`);
      }
    });
  }

  async pruneBatches(maxCompletedBatches: number): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      if (currentMetrics.size === 0) return;

      // 按 batchId 分组
      const batches = new Map<string, TaskMetrics[]>();
      for (const metric of currentMetrics.values()) {
        if (!batches.has(metric.batchId)) {
          batches.set(metric.batchId, []);
        }
        const batch = batches.get(metric.batchId);
        if (batch) {
          batch.push(metric);
        }
      }

      // 识别完全完成的批处理任务
      const completedBatches: { batchId: string, completedAt: number }[] = [];
      for (const [batchId, tasks] of batches.entries()) {
        const isBatchComplete = tasks.every(t => ['completed', 'failed', 'cancelled'].includes(t.status));
        if (isBatchComplete) {
          const lastCompletedTime = Math.max(...tasks.map(t => t.endTime ?? 0));
          completedBatches.push({ batchId, completedAt: lastCompletedTime });
        }
      }

      // 如果完成的批处理数超过限制，进行清理
      if (completedBatches.length > maxCompletedBatches) {
        this._logger.info(`已完成批处理数 (${completedBatches.length}) 超过限制 (${maxCompletedBatches})，开始清理...`);
        
        // 按完成时间升序排序
        completedBatches.sort((a, b) => a.completedAt - b.completedAt);
        
        const batchesToRemoveCount = completedBatches.length - maxCompletedBatches;
        const batchesToRemove = completedBatches.slice(0, batchesToRemoveCount);
        
        let tasksRemovedCount = 0;
        for (const batchToRemove of batchesToRemove) {
          const tasksInBatch = batches.get(batchToRemove.batchId) || [];
          for (const taskMetric of tasksInBatch) {
            currentMetrics.delete(taskMetric.id);
            tasksRemovedCount++;
          }
          this._logger.debug(`已清理旧批处理任务: ${batchToRemove.batchId}`);
        }
        
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        this._logger.info(`清理完成，移除了 ${batchesToRemove.length} 个旧批处理 (共 ${tasksRemovedCount} 个任务)`);
      }
    });
  }

  async removeTaskMetricsByBatchIds(batchIds: string[]): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      let removedCount = 0;

      for (const [taskId, metric] of currentMetrics.entries()) {
        if (batchIds.includes(metric.batchId)) {
          currentMetrics.delete(taskId);
          removedCount++;
        }
      }

      if (removedCount > 0) {
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        this._logger.info(`移除了 ${removedCount} 个任务指标 (批处理: ${batchIds.join(', ')})`);
      }
    });
  }

  // 批处理元数据管理
  async updateBatchMetadata(batchId: string, updates: Partial<BatchMetadata>): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const batchData = await this._loadBatchMetadata(ctx);
      const existing = batchData.get(batchId);
      
      if (existing) {
        const updated = { ...existing, ...updates };
        batchData.set(batchId, updated);
      } else {
        const newBatch: BatchMetadata = {
          batchId,
          createdAt: getCurrentTimestamp(),
          status: 'pending',
          totalTasks: 0,
          completedTasks: 0,
          ...updates
        };
        batchData.set(batchId, newBatch);
      }
      
      ctx.save(STATE_KEYS.BATCH_METADATA, Object.fromEntries(batchData));
    });
  }

  async getBatchMetadata(batchId: string): Promise<BatchMetadata | null> {
    const batchData = await this._stateStore.load<Record<string, BatchMetadata>>(STATE_KEYS.BATCH_METADATA);
    return batchData?.[batchId] || null;
  }

  // 私有方法
  private async _initializeState(): Promise<void> {
    try {
      const metricsData = await this._stateStore.load<Record<string, TaskMetrics>>(STATE_KEYS.TASK_METRICS);
      if (metricsData) {
        const metricsMap = new Map(Object.entries(metricsData));
        this._taskMetrics$.next(metricsMap);
        this._logger.info(`初始化时加载了 ${metricsMap.size} 个任务指标`);
      }
    } catch (error) {
      this._logger.error('初始化状态失败:', error);
    }
  }

  private async _loadTaskMetricsMap(ctx?: any): Promise<Map<TaskId, TaskMetrics>> {
    const loadMethod = ctx ? ctx.load.bind(ctx) : this._stateStore.load.bind(this._stateStore);
    const metricsData = await loadMethod<Record<string, TaskMetrics>>(STATE_KEYS.TASK_METRICS);
    return new Map(Object.entries(metricsData || {}));
  }

  private async _loadBatchMetadata(ctx?: any): Promise<Map<string, BatchMetadata>> {
    const loadMethod = ctx ? ctx.load.bind(ctx) : this._stateStore.load.bind(this._stateStore);
    const batchData = await loadMethod<Record<string, BatchMetadata>>(STATE_KEYS.BATCH_METADATA);
    return new Map(Object.entries(batchData || {}));
  }

  /**
   * 获取状态存储的统计信息
   */
  async getStateStats(): Promise<any> {
    return this._stateStore.getStats();
  }

  /**
   * 清理旧的状态数据
   */
  async cleanupOldState(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    await this._stateStore.withTransaction(async (ctx) => {
      const currentMetrics = await this._loadTaskMetricsMap(ctx);
      const cutoffTime = getCurrentTimestamp() - maxAge;
      let cleanedCount = 0;

      for (const [taskId, metric] of currentMetrics.entries()) {
        if (metric.endTime && metric.endTime < cutoffTime) {
          currentMetrics.delete(taskId);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        ctx.save(STATE_KEYS.TASK_METRICS, Object.fromEntries(currentMetrics));
        this._logger.info(`清理了 ${cleanedCount} 个过期任务指标`);
      }
    });
  }
}