# 数懒平台部署指南

## 部署架构

数懒平台支持多种部署方式，从单机开发环境到分布式生产环境。

## 环境要求

### 最低配置
- **CPU**: 2核
- **内存**: 4GB
- **存储**: 20GB
- **网络**: 1Mbps

### 推荐配置
- **CPU**: 4核+
- **内存**: 8GB+
- **存储**: 100GB+ SSD
- **网络**: 10Mbps+

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- PostgreSQL 15+
- Redis 7+
- Node.js 16+ (开发环境)
- Python 3.11+ (开发环境)

## 快速部署

### 1. 使用Docker Compose (推荐)

```bash
# 克隆项目
git clone https://github.com/your-org/digital-lazy-platform.git
cd digital-lazy-platform

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 使用部署脚本

```bash
# 赋予执行权限
chmod +x scripts/deploy.sh

# 部署到生产环境
./scripts/deploy.sh v1.0.0 production deploy

# 检查部署状态
./scripts/deploy.sh health-check
```

## 环境配置

### 环境变量配置

创建 `.env` 文件：

```bash
# 应用配置
APP_NAME=数懒平台
VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=****************************************/digital_lazy_platform
REDIS_URL=redis://redis:6379/0

# 外部服务
OPENAI_API_KEY=your-openai-api-key
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 文件存储
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# 监控配置
ENABLE_METRICS=true
LOG_LEVEL=INFO
```

### 数据库配置

#### PostgreSQL 配置

```sql
-- 创建数据库
CREATE DATABASE digital_lazy_platform;
CREATE USER digital_lazy_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE digital_lazy_platform TO digital_lazy_user;

-- 配置连接池
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

#### Redis 配置

```bash
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 生产环境部署

### 1. Kubernetes 部署

创建 `k8s/` 目录下的配置文件：

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: digital-lazy

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: digital-lazy
data:
  DATABASE_URL: "****************************************/digital_lazy_platform"
  REDIS_URL: "redis://redis:6379/0"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: digital-lazy
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: digital-lazy-backend:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: app-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

部署到Kubernetes：

```bash
kubectl apply -f k8s/
kubectl get pods -n digital-lazy
kubectl logs -f deployment/backend -n digital-lazy
```

### 2. 云服务部署

#### AWS ECS 部署

```json
{
  "family": "digital-lazy-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/digital-lazy-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "********************************************/digital_lazy_platform"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/digital-lazy-backend",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## 负载均衡配置

### Nginx 配置

```nginx
upstream backend {
    server backend1:8000;
    server backend2:8000;
    server backend3:8000;
}

upstream frontend {
    server frontend1:3000;
    server frontend2:3000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # API请求
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 前端应用
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 监控和日志

### 1. 应用监控

使用Prometheus + Grafana：

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
```

### 2. 日志收集

使用ELK Stack：

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    
  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    
  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

## 备份和恢复

### 数据库备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份PostgreSQL
docker exec digital-lazy-postgres pg_dump -U user digital_lazy_platform > $BACKUP_DIR/database.sql

# 备份Redis
docker exec digital-lazy-redis redis-cli BGSAVE
docker cp digital-lazy-redis:/data/dump.rdb $BACKUP_DIR/

# 备份上传文件
tar -czf $BACKUP_DIR/uploads.tar.gz uploads/

echo "Backup completed: $BACKUP_DIR"
```

### 数据恢复

```bash
#!/bin/bash
# restore.sh

BACKUP_DIR=$1

if [ -z "$BACKUP_DIR" ]; then
    echo "Usage: $0 <backup_directory>"
    exit 1
fi

# 恢复PostgreSQL
docker exec -i digital-lazy-postgres psql -U user digital_lazy_platform < $BACKUP_DIR/database.sql

# 恢复Redis
docker cp $BACKUP_DIR/dump.rdb digital-lazy-redis:/data/
docker restart digital-lazy-redis

# 恢复上传文件
tar -xzf $BACKUP_DIR/uploads.tar.gz

echo "Restore completed from: $BACKUP_DIR"
```

## 安全配置

### 1. SSL/TLS 配置

```bash
# 生成SSL证书 (Let's Encrypt)
certbot --nginx -d your-domain.com
```

### 2. 防火墙配置

```bash
# UFW配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 5432/tcp  # 禁止外部访问数据库
ufw deny 6379/tcp  # 禁止外部访问Redis
ufw enable
```

### 3. 应用安全

- 定期更新依赖包
- 使用强密码和密钥
- 启用CORS保护
- 配置速率限制
- 定期安全审计

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查日志
   docker-compose logs backend
   
   # 检查端口占用
   netstat -tulpn | grep :8000
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres psql -U user -d digital_lazy_platform -c "SELECT 1;"
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   
   # 调整容器内存限制
   docker-compose up -d --scale backend=2
   ```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);

-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM tasks WHERE user_id = 'user123';
```

### 2. 缓存优化

```python
# Redis缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

### 3. 应用优化

- 启用Gzip压缩
- 使用CDN加速静态资源
- 配置数据库连接池
- 实施异步任务处理
- 优化SQL查询

## 更新和维护

### 滚动更新

```bash
# 更新后端服务
docker-compose pull backend
docker-compose up -d --no-deps backend

# 更新前端服务
docker-compose pull frontend
docker-compose up -d --no-deps frontend
```

### 健康检查

```bash
# 检查服务健康状态
curl -f http://localhost:8000/health

# 检查数据库连接
curl -f http://localhost:8000/health/db

# 检查Redis连接
curl -f http://localhost:8000/health/redis
```
