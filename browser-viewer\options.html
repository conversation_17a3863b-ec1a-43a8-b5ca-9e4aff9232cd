<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数懒智能代理 - 设置</title>
  <style>
    :root {
      --primary-color: #007AFF;
      --primary-hover: #0051D0;
      --primary-light: rgba(0, 122, 255, 0.1);
      --success-color: #30D158;
      --error-color: #FF453A;
      --warn-color: #FF9F0A;
      --paused-color: #8E8E93;
      
      /* Enhanced Apple-style color palette */
      --bg-main: #F2F2F7;
      --bg-secondary: #E5E5EA;
      --bg-panel-primary: rgba(255, 255, 255, 0.95);
      --bg-panel-secondary: rgba(248, 248, 252, 0.85);
      --bg-content-primary: rgba(255, 255, 255, 0.8);
      --bg-content-secondary: rgba(255, 255, 255, 0.6);
      --bg-input: rgba(255, 255, 255, 0.9);
      --bg-card: rgba(255, 255, 255, 0.75);
      --text-primary: #1D1D1F;
      --text-secondary: #6D6D70;
      --text-tertiary: #8E8E93;
      --text-accent: var(--primary-color);
      --border-color: rgba(0, 0, 0, 0.08);
      --border-light: rgba(0, 0, 0, 0.04);
      --border-strong: rgba(0, 0, 0, 0.12);
      --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
      --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
      --shadow-strong: 0 4px 16px rgba(0, 0, 0, 0.12);
      --shadow-primary: 0 2px 8px rgba(0, 122, 255, 0.15);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(145deg, #F2F2F7 0%, #E8E8ED 30%, #DEDEE3 70%, #D1D1D6 100%);
      color: var(--text-primary);
      line-height: 1.5;
      min-height: 100vh;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    .container {
      max-width: 920px;
      margin: 0 auto;
      padding: 1.5rem;
    }
    
    /* Header with enhanced visual hierarchy */
    .header {
      background: var(--bg-panel-primary);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      padding: 2rem;
      border-radius: 20px;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-strong);
      text-align: center;
      border: 1px solid var(--border-light);
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 6px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      border-radius: 0 0 6px 6px;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }
    
    .header h1 {
      color: var(--text-primary);
      margin-bottom: 0.5rem;
      font-size: 2rem;
      font-weight: 700;
      letter-spacing: -0.03em;
    }

    .header p {
      color: var(--text-secondary);
      font-size: 1rem;
      font-weight: 500;
      opacity: 0.9;
    }

    /* Settings sections with visual hierarchy */
    .settings-section {
      margin-bottom: 2rem;
    }

    .section-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;
      padding: 0 0.5rem;
    }

    .section-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
      color: white;
      box-shadow: var(--shadow-light);
    }

    .section-icon.primary { background: linear-gradient(135deg, #007AFF 0%, #0051D0 100%); }
    .section-icon.success { background: linear-gradient(135deg, var(--success-color) 0%, #28A745 100%); }
    .section-icon.warn { background: linear-gradient(135deg, var(--warn-color) 0%, #E09900 100%); }
    .section-icon.security { background: linear-gradient(135deg, #007B8A 0%, #005A6B 100%); }
    .section-icon.analytics { background: linear-gradient(135deg, #7B68EE 0%, #6A5ACD 100%); }

    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.02em;
    }
    
    .settings-panel {
      background: var(--bg-panel-primary);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 18px;
      box-shadow: var(--shadow-medium);
      overflow: hidden;
      border: 1px solid var(--border-light);
    }

    .settings-panel.secondary {
      background: var(--bg-panel-secondary);
    }
    
    .panel-content {
      padding: 1.5rem;
    }

    /* Enhanced grid layouts */
    .settings-grid {
      display: grid;
      gap: 1rem;
    }

    .settings-grid.primary {
      grid-template-columns: 2fr 1fr;
      gap: 1.25rem;
    }

    .settings-grid.primary-spaced {
      grid-template-columns: 2fr 1fr;
      gap: 1.25rem;
      margin-bottom: 1.5rem;
    }

    .settings-grid.two-column {
      grid-template-columns: 1fr 1fr;
      gap: 1.25rem;
    }

    .settings-grid.two-column-spaced {
      grid-template-columns: 1fr 1fr;
      gap: 1.25rem;
      margin-bottom: 1.5rem;
    }

    .settings-grid.three-column {
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }

    .settings-grid.compact {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }
    
    /* Enhanced setting items */
    .setting-item {
      background: var(--bg-content-primary);
      border-radius: 14px;
      padding: 1.25rem;
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-light);
      transition: all 0.3s ease;
      position: relative;
    }

    .setting-item:hover {
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(0, 122, 255, 0.02) 100%);
      border-color: rgba(0, 122, 255, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 2px 12px rgba(0, 122, 255, 0.1);
    }

    .setting-item.primary {
      background: var(--bg-content-primary);
      border-color: rgba(0, 122, 255, 0.15);
    }

    .setting-item.primary:hover {
      border-color: rgba(0, 122, 255, 0.3);
      box-shadow: var(--shadow-primary);
    }

    .setting-item.featured {
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(0, 122, 255, 0.02) 100%);
      border: 1px solid rgba(0, 122, 255, 0.2);
    }

    .setting-item.full-width {
      grid-column: 1 / -1;
    }

    .setting-item.span-two {
      grid-column: span 2;
    }
    
    .setting-label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: block;
      color: var(--text-primary);
      font-size: 0.9375rem;
      letter-spacing: -0.01em;
    }

    .setting-label.primary {
      color: var(--text-accent);
    }
    
    .setting-description {
      color: var(--text-secondary);
      font-size: 0.8125rem;
      margin-bottom: 1rem;
      line-height: 1.45;
      opacity: 0.9;
    }
    
    .setting-control {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .setting-control.vertical {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }

    .setting-control.inline {
      flex-wrap: wrap;
      gap: 0.5rem;
    }
    
    /* Enhanced form controls */
    input[type="text"], input[type="number"], select, textarea {
      padding: 0.75rem 1rem;
      border: 1px solid var(--border-color);
      border-radius: 10px;
      font-size: 0.8125rem;
      min-width: 120px;
      background: var(--bg-input);
      color: var(--text-primary);
      font-family: inherit;
      transition: all 0.2s ease;
      outline: none;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    input[type="text"]:focus, input[type="number"]:focus, select:focus, textarea:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px var(--primary-light), inset 0 1px 2px rgba(0, 0, 0, 0.03);
      background: rgba(255, 255, 255, 0.98);
      transform: translateY(-1px);
    }

    textarea {
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.75rem;
      resize: vertical;
      min-height: 90px;
      width: 100%;
      line-height: 1.5;
    }
    
    input[type="checkbox"] {
      width: 20px;
      height: 20px;
      accent-color: var(--primary-color);
      cursor: pointer;
      border-radius: 4px;
    }

    /* Apple-style toggle switch */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 52px;
      height: 32px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #E5E5EA;
      border: 1px solid var(--border-color);
      transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
      border-radius: 16px;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 28px;
      width: 28px;
      left: 2px;
      bottom: 1px;
      background: white;
      transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    input:checked + .toggle-slider {
      background: var(--success-color);
      border-color: var(--success-color);
      box-shadow: inset 0 1px 3px rgba(48, 209, 88, 0.2);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(20px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.15);
    }

    .toggle-slider:hover {
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(0, 122, 255, 0.1);
    }

    input:checked + .toggle-slider:hover {
      box-shadow: inset 0 1px 3px rgba(48, 209, 88, 0.3), 0 0 0 2px rgba(48, 209, 88, 0.15);
    }

    .toggle-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1.5rem;
      padding: 0;
      margin-bottom: 0.75rem;
    }

    .toggle-label {
      color: var(--text-primary);
      font-size: 0.9375rem;
      font-weight: 600;
      cursor: pointer;
      flex-grow: 1;
      letter-spacing: -0.01em;
    }

    .setting-item.toggle-item {
      padding: 1.5rem;
    }

    /* Enhanced compact controls */
    .compact-control {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .compact-control input[type="number"] {
      width: 90px;
      min-width: 90px;
      text-align: center;
      font-weight: 600;
    }

    .compact-control select {
      min-width: 140px;
    }

    .unit-label {
      font-size: 0.75rem;
      color: var(--text-tertiary);
      font-weight: 600;
      background: rgba(0, 0, 0, 0.04);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
    }

    .checkbox-wrapper {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem 0;
    }

    .checkbox-label {
      color: var(--text-primary);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
    }
    
    /* Enhanced buttons */
    .btn {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      font-size: 0.8125rem;
      font-weight: 600;
      transition: all 0.25s ease;
      outline: none;
      letter-spacing: -0.01em;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2);
      font-family: inherit;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .btn:hover::before {
      left: 100%;
    }
    
    .btn:hover {
      background: linear-gradient(135deg, var(--primary-hover) 0%, #003d82 100%);
      box-shadow: 0 4px 16px rgba(0, 122, 255, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .btn:active {
      transform: translateY(-1px);
      transition: transform 0.1s ease;
    }
    
    .btn.secondary {
      background: var(--bg-card);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-light);
    }
    
    .btn.secondary:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: var(--border-strong);
      box-shadow: var(--shadow-medium);
      transform: translateY(-2px);
    }

    .btn.small {
      padding: 0.5rem 1rem;
      font-size: 0.75rem;
      border-radius: 8px;
    }

    .btn.large {
      padding: 1rem 2rem;
      font-size: 0.9375rem;
      border-radius: 12px;
    }
    
    .actions {
      display: flex;
      justify-content: center;
      gap: 1.25rem;
      padding: 2rem;
      flex-wrap: wrap;
    }
    
    /* Enhanced status messages */
    .status-message {
      padding: 1rem 1.5rem;
      border-radius: 12px;
      margin: 1.5rem 0;
      text-align: center;
      display: none;
      font-weight: 500;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid;
    }
    
    .status-message.success {
      background: rgba(48, 209, 88, 0.1);
      color: var(--success-color);
      border-color: rgba(48, 209, 88, 0.2);
      box-shadow: 0 2px 12px rgba(48, 209, 88, 0.15);
    }
    
    .status-message.error {
      background: rgba(255, 69, 58, 0.1);
      color: var(--error-color);
      border-color: rgba(255, 69, 58, 0.2);
      box-shadow: 0 2px 12px rgba(255, 69, 58, 0.15);
    }

    /* Enhanced log area */
    .log-section {
      background: var(--bg-content-secondary);
      border-radius: 14px;
      padding: 1.25rem;
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-light);
      transition: all 0.3s ease;
      position: relative;
    }

    .log-section:hover {
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(0, 122, 255, 0.02) 100%);
      border-color: rgba(0, 122, 255, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 2px 12px rgba(0, 122, 255, 0.1);
    }

    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-light);
    }

    .log-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .log-controls {
      display: flex;
      gap: 0.75rem;
    }

    #privacyLog {
      background: var(--bg-input);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 10px;
      padding: 1.25rem;
      max-height: 300px;
      overflow-y: auto;
      font-size: 0.6875rem;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      border: 1px solid var(--border-color);
      color: var(--text-primary);
      line-height: 1.5;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
    }

    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.02);
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--text-tertiary);
      border-radius: 3px;
      transition: background 0.2s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--text-secondary);
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .header {
        padding: 1.5rem;
      }
      
      .panel-content {
        padding: 1.25rem;
      }

      .settings-grid.primary,
      .settings-grid.primary-spaced,
      .settings-grid.two-column,
      .settings-grid.two-column-spaced,
      .settings-grid.three-column {
        grid-template-columns: 1fr;
      }
      
      .actions {
        flex-direction: column;
        align-items: center;
      }
      
      .btn {
        min-width: 200px;
      }
    }

    @media (max-width: 600px) {
      .compact-control {
        flex-direction: column;
        align-items: stretch;
      }

      .compact-control input[type="number"],
      .compact-control select {
        width: 100%;
        min-width: auto;
      }

      .log-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .log-controls {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>数懒智能</h1>
      <p>个性化配置您的浏览代理体验</p>
    </div>
    
    <!-- Core Settings Section -->
    <div class="settings-section">
      <div class="section-header">
        <div class="section-icon primary">⚙️</div>
        <h2 class="section-title">核心设置</h2>
      </div>
      <div class="settings-panel">
        <div class="panel-content">
          <div class="settings-grid primary-spaced">
            <div class="setting-item featured">
              <label class="setting-label primary">智能并发上限</label>
              <div class="setting-description">根据您的设备性能智能调节同时执行浏览的页面数量</div>
              <div class="setting-control compact-control">
                <input type="number" id="maxConcurrentTasks" min="1" max="10" value="3">
                <span class="unit-label">个页面</span>
              </div>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">行为配置模式</label>
              <div class="setting-description">选择最适合您需求的浏览策略</div>
              <div class="setting-control">
                <select id="defaultBehaviorProfile">
                  <option value="stealth">🥷 隐秘模式</option>
                  <option value="normal" selected>👤 普通模式</option>
                  <option value="aggressive">🚀 快速浏览</option>
                </select>
              </div>
            </div>
          </div>

          <div class="setting-item toggle-item">
            <div class="toggle-wrapper">
              <label class="toggle-label" for="enableDistraction">🎭 启用人性化行为模拟</label>
              <label class="toggle-switch">
                <input type="checkbox" id="enableDistraction" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            <div class="setting-description">模拟真实用户的自然浏览行为，包括停顿、滚动等细节动作</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Section -->
    <div class="settings-section">
      <div class="section-header">
        <div class="section-icon success">🚀</div>
        <h2 class="section-title">性能优化</h2>
      </div>
      <div class="settings-panel">
        <div class="panel-content">
          <div class="settings-grid two-column-spaced">
            <div class="setting-item">
              <label class="setting-label">CPU保护阈值</label>
              <div class="setting-description">超过此使用率时自动降低任务强度</div>
              <div class="setting-control compact-control">
                <input type="number" id="cpuThreshold" min="10" max="100" value="70">
                <span class="unit-label">%</span>
              </div>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">内存保护阈值</label>
              <div class="setting-description">达到此用量时智能限制新任务</div>
              <div class="setting-control compact-control">
                <input type="number" id="memoryThreshold" min="100" max="4096" value="1024">
                <span class="unit-label">MB</span>
              </div>
            </div>
          </div>

          <div class="setting-item toggle-item">
            <div class="toggle-wrapper">
              <label class="toggle-label" for="adaptiveSpeed">🧠 启用AI智能调速</label>
              <label class="toggle-switch">
                <input type="checkbox" id="adaptiveSpeed" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            <div class="setting-description">基于系统实时负载和网络状况自动优化执行速度</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Output Settings Section -->
    <div class="settings-section">
      <div class="section-header">
        <div class="section-icon warn">📸</div>
        <h2 class="section-title">输出设置</h2>
      </div>
      <div class="settings-panel">
        <div class="panel-content">
          <div class="settings-grid compact">
            <div class="setting-item">
              <label class="setting-label">图片质量</label>
              <div class="setting-description">平衡质量与文件大小</div>
              <div class="setting-control compact-control">
                <input type="number" id="imageQuality" min="10" max="100" value="80">
                <span class="unit-label">%</span>
              </div>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">分块尺寸</label>
              <div class="setting-description">页面截图分割大小</div>
              <div class="setting-control compact-control">
                <input type="number" id="blockSize" min="256" max="2048" value="1024">
                <span class="unit-label">px</span>
              </div>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">输出格式</label>
              <div class="setting-description">截图保存格式</div>
              <div class="setting-control">
                <select id="imageFormat">
                  <option value="png">🖼️ PNG</option>
                  <option value="jpeg" selected>📷 JPEG</option>
                  <option value="webp">🌐 WebP</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Privacy & Security Section -->
    <div class="settings-section">
      <div class="section-header">
        <div class="section-icon security">🔒</div>
        <h2 class="section-title">隐私与安全</h2>
      </div>
      <div class="settings-panel secondary">
        <div class="panel-content">
          <div class="settings-grid">
            <div class="setting-item full-width">
              <label class="setting-label" for="proxyUrlBlacklist">🚫 URL黑名单</label>
              <div class="setting-description">这些URL将跳过处理，不执行浏览，支持通配符模式匹配</div>
              <div class="setting-control vertical">
                <textarea id="proxyUrlBlacklist" rows="2" placeholder="example.com&#10;https://*.dangerous-site.com"></textarea>
              </div>
            </div>

            <div class="setting-item full-width">
              <label class="setting-label" for="cookieDeniedDomains">🍪 Cookie隔离域名</label>
              <div class="setting-description">访问这些域名时将不会使用您的Cookie，使用独立的浏览环境，保护您的登录状态</div>
              <div class="setting-control vertical">
                <textarea id="cookieDeniedDomains" rows="2" placeholder="tracking-service.com&#10;https://*.your-social-media.com"></textarea>
              </div>
            </div>
            
            <div class="setting-item toggle-item full-width">
              <div class="toggle-wrapper">
                <label class="toggle-label" for="enableAuditLog">📋 启用透明度审计</label>
                <label class="toggle-switch">
                  <input type="checkbox" id="enableAuditLog" checked>
                  <span class="toggle-slider"></span>
                </label>
              </div>
              <div class="setting-description">详细记录插件的所有数据访问行为，确保操作透明可查</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Transparency Dashboard -->
    <div class="settings-section">
      <div class="section-header">
        <div class="section-icon analytics">📊</div>
        <h2 class="section-title">透明度仪表板</h2>
      </div>
      <div class="settings-panel secondary">
        <div class="panel-content">
          <div class="log-section">
            <div class="log-header">
              <div class="log-title">🔍 数据访问记录</div>
              <div class="log-controls">
                <button id="refreshLogBtn" class="btn small">刷新</button>
                <button id="clearLogBtn" class="btn secondary small">清空</button>
              </div>
            </div>
            <pre id="privacyLog">等待加载访问记录...</pre>
          </div>
        </div>
      </div>
    </div>
    
    <div class="status-message" id="statusMessage"></div>
    
    <div class="actions">
      <button class="btn large" id="saveBtn">💾 保存所有设置</button>
      <button class="btn secondary large" id="resetBtn">🔄 恢复默认配置</button>
    </div>
  </div>
  
  <script type="module" src="/src/ui/options/options.ts"></script>
</body>
</html>