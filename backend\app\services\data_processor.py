"""
数据处理服务
处理数据上传、存储、查询等功能
"""

import os
import hashlib
from typing import Optional, List, Dict, Any
from fastapi import UploadFile
from sqlalchemy.orm import Session
from datetime import datetime

from app.models.data import Data, DataType, DataStatus
from app.schemas.data import (
    DataResponse,
    DataQuery,
    DataUploadResponse,
    DataListResponse,
)
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class DataProcessorService:
    """数据处理服务类"""

    def __init__(self, db: Session):
        self.db = db

    async def upload_file(
        self, file: UploadFile, user_id: str, task_id: Optional[str] = None
    ) -> DataUploadResponse:
        """上传文件"""
        try:
            # 检查文件类型
            if not self._is_allowed_file_type(file.filename):
                raise ValueError(f"不支持的文件类型: {file.filename}")

            # 检查文件大小
            content = await file.read()
            if len(content) > settings.MAX_FILE_SIZE:
                raise ValueError(f"文件大小超过限制: {len(content)} bytes")

            # 计算文件校验和
            checksum = hashlib.sha256(content).hexdigest()

            # 生成文件路径
            file_path = self._generate_file_path(user_id, file.filename)
            full_path = os.path.join(settings.UPLOAD_DIR, file_path)

            # 确保目录存在
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # 保存文件
            with open(full_path, "wb") as f:
                f.write(content)

            # 创建数据记录
            data = Data(
                name=file.filename,
                description=f"上传的文件: {file.filename}",
                data_type=self._get_data_type_from_filename(file.filename),
                status=DataStatus.RAW,
                user_id=user_id,
                task_id=task_id,
                file_path=file_path,
                file_size=len(content),
                file_type=file.content_type,
                checksum=checksum,
                created_at=datetime.utcnow(),
            )

            self.db.add(data)
            self.db.commit()
            self.db.refresh(data)

            logger.info(f"File uploaded: {file.filename} by user {user_id}")

            return DataUploadResponse(
                data_id=data.id,
                file_name=file.filename,
                file_size=len(content),
                file_type=file.content_type,
                upload_time=data.created_at,
            )

        except Exception as e:
            logger.error(f"File upload error: {e}")
            self.db.rollback()
            raise

    async def get_user_data(
        self,
        user_id: str,
        page: int = 1,
        size: int = 20,
        task_id: Optional[str] = None,
        data_type: Optional[DataType] = None,
    ) -> DataListResponse:
        """获取用户数据列表"""
        try:
            query = self.db.query(Data).filter(Data.user_id == user_id)

            # 过滤条件
            if task_id:
                query = query.filter(Data.task_id == task_id)

            if data_type:
                query = query.filter(Data.data_type == data_type)

            # 排序
            query = query.order_by(Data.created_at.desc())

            # 分页
            total = query.count()
            data_list = query.offset((page - 1) * size).limit(size).all()

            return DataListResponse(
                data=[DataResponse.from_orm(item) for item in data_list],
                total=total,
                page=page,
                size=size,
            )

        except Exception as e:
            logger.error(f"Get user data error: {e}")
            raise

    async def get_data_detail(self, data_id: str, user_id: str) -> DataResponse:
        """获取数据详情"""
        try:
            data = (
                self.db.query(Data)
                .filter(Data.id == data_id, Data.user_id == user_id)
                .first()
            )

            if not data:
                raise ValueError("数据不存在")

            # 增加访问次数
            data.increment_access()
            self.db.commit()

            return DataResponse.from_orm(data)

        except Exception as e:
            logger.error(f"Get data detail error: {e}")
            raise

    async def query_data(self, query: DataQuery, user_id: str) -> DataListResponse:
        """查询数据"""
        try:
            db_query = self.db.query(Data).filter(Data.user_id == user_id)

            # 应用查询条件
            if query.data_type:
                db_query = db_query.filter(Data.data_type == query.data_type)

            if query.status:
                db_query = db_query.filter(Data.status == query.status)

            if query.task_id:
                db_query = db_query.filter(Data.task_id == query.task_id)

            if query.date_from:
                db_query = db_query.filter(Data.created_at >= query.date_from)

            if query.date_to:
                db_query = db_query.filter(Data.created_at <= query.date_to)

            if query.search_text:
                db_query = db_query.filter(
                    Data.name.contains(query.search_text)
                    | Data.description.contains(query.search_text)
                )

            # 排序
            db_query = db_query.order_by(Data.created_at.desc())

            # 分页
            total = db_query.count()
            data_list = (
                db_query.offset((query.page - 1) * query.size).limit(query.size).all()
            )

            return DataListResponse(
                data=[DataResponse.from_orm(item) for item in data_list],
                total=total,
                page=query.page,
                size=query.size,
            )

        except Exception as e:
            logger.error(f"Query data error: {e}")
            raise

    async def delete_data(self, data_id: str, user_id: str) -> bool:
        """删除数据"""
        try:
            data = (
                self.db.query(Data)
                .filter(Data.id == data_id, Data.user_id == user_id)
                .first()
            )

            if not data:
                return False

            # 删除文件
            if data.file_path:
                full_path = os.path.join(settings.UPLOAD_DIR, data.file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)

            # 删除数据库记录
            self.db.delete(data)
            self.db.commit()

            logger.info(f"Data deleted: {data_id}")
            return True

        except Exception as e:
            logger.error(f"Delete data error: {e}")
            self.db.rollback()
            return False

    def _is_allowed_file_type(self, filename: str) -> bool:
        """检查文件类型是否允许"""
        if not filename:
            return False

        ext = os.path.splitext(filename)[1].lower()
        return ext in settings.ALLOWED_FILE_TYPES

    def _get_data_type_from_filename(self, filename: str) -> DataType:
        """根据文件名推断数据类型"""
        ext = os.path.splitext(filename)[1].lower()

        if ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp"]:
            return DataType.IMAGE
        elif ext in [".txt", ".md", ".csv"]:
            return DataType.TEXT
        elif ext in [".pdf", ".doc", ".docx"]:
            return DataType.DOCUMENT
        else:
            return DataType.OTHER

    def _generate_file_path(self, user_id: str, filename: str) -> str:
        """生成文件存储路径"""
        date_str = datetime.utcnow().strftime("%Y/%m/%d")
        return f"{user_id}/{date_str}/{filename}"
