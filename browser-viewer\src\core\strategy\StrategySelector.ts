import { DefaultScreenshotStrategy } from './DefaultScreenshotStrategy';
import type { ExtractionStrategy } from './ExtractionStrategy';
import { JdProductStrategy } from './JdProductStrategy';

// 策略映射表，将域名或匹配规则映射到具体的策略类
const strategyMap: Array<[RegExp, new () => ExtractionStrategy]> = [
  [/^item\.jd\.com/, JdProductStrategy],
  [/^npcitem\.jd\.hk/, JdProductStrategy],
  // 可以在这里添加更多网站的规则
  // 例如：[/^www\.amazon\.com\/dp\//, AmazonProductStrategy],
];

/**
 * 策略选择器，根据URL选择合适的提取策略。
 */
export class StrategySelector {
  /**
   * 根据提供的URL选择一个提取策略。
   * @param url - 当前页面的URL。
   * @returns 返回一个具体的策略实例。如果没有找到匹配的特定策略，则返回默认的截图策略。
   */
  public static select(url: string): ExtractionStrategy {
    const { hostname, pathname } = new URL(url);
    const hostAndPath = `${hostname}${pathname}`;

    for (const [rule, StrategyClass] of strategyMap) {
      if (rule.test(hostAndPath)) {
        return new StrategyClass();
      }
    }
    
    // 如果没有匹配的规则，返回默认策略
    return new DefaultScreenshotStrategy();
  }
} 