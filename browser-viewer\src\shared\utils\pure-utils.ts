/**
 * 数懒孪生代理 - 纯工具函数
 *
 * 该文件包含不依赖任何浏览器扩展API的、可跨平台（包括Web Workers）使用的工具函数。
 */
import type { Point, Rectangle, TaskId, Timestamp } from '@/shared/types';

// ID生成工具
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

export function generateTaskId(): TaskId {
  return `task-${generateId()}`;
}

// 数据转换工具
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return self.btoa(binary);
}

export function dataUrlToBuffer(dataUrl: string): ArrayBuffer {
  if (typeof dataUrl !== 'string' || !dataUrl.startsWith('data:')) {
    throw new Error('Invalid data URL: must be a string starting with "data:".');
  }

  const dataUrlParts = dataUrl.split(',');
  if (dataUrlParts.length < 2 || !dataUrlParts[0] || !dataUrlParts[1]) {
    throw new Error('Invalid data URL: format is incorrect.');
  }

  const mimePart = dataUrlParts[0];
  const dataPart = dataUrlParts[1];
  
  const isBase64 = mimePart.includes(';base64');
  const decodedData = isBase64 ? atob(dataPart) : decodeURIComponent(dataPart);
  
  const buffer = new ArrayBuffer(decodedData.length);
  const view = new Uint8Array(buffer);

  for (let i = 0; i < decodedData.length; i++) {
    view[i] = decodedData.charCodeAt(i);
  }

  return buffer;
}

// 时间工具
export function getCurrentTimestamp(): Timestamp {
  return Date.now();
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else {
    return `${seconds}s`;
  }
}

// 数学工具
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

export function lerp(start: number, end: number, t: number): number {
  return start + (end - start) * t;
}

export function distance(p1: Point, p2: Point): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

// 贝塞尔曲线计算
export function quadraticBezier(t: number, p0: Point, p1: Point, p2: Point): Point {
  const x = (1 - t) * (1 - t) * p0.x + 2 * (1 - t) * t * p1.x + t * t * p2.x;
  const y = (1 - t) * (1 - t) * p0.y + 2 * (1 - t) * t * p1.y + t * t * p2.y;
  return { x, y };
}

export function generateBezierPath(
  start: Point,
  end: Point,
  steps: number = 50,
  randomnessFactor: number = 0.5
): Point[] {
  const path: Point[] = [];
  
  // 生成控制点，添加随机性
  const midX = (start.x + end.x) / 2;
  const controlX = midX + (Math.random() - 0.5) * 200 * randomnessFactor;
  const controlY = Math.min(start.y, end.y) - Math.random() * 100 * randomnessFactor;
  const control: Point = { x: controlX, y: controlY };
  
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;
    path.push(quadraticBezier(t, start, control, end));
  }
  
  return path;
}

// 几何工具
export function rectangleIntersects(r1: Rectangle, r2: Rectangle): boolean {
  return !(r1.x + r1.width < r2.x || 
            r2.x + r2.width < r1.x || 
            r1.y + r1.height < r2.y || 
            r2.y + r2.height < r1.y);
}

export function rectangleContains(rect: Rectangle, point: Point): boolean {
  return point.x >= rect.x && 
          point.x <= rect.x + rect.width &&
          point.y >= rect.y && 
          point.y <= rect.y + rect.height;
}

export function calculateBlockSize(viewportWidth: number, devicePixelRatio: number = 1): number {
  return Math.min(viewportWidth, 1024) * devicePixelRatio;
}

// 随机工具
export function randomDelay(min: number, max: number): Promise<void> {
  const delay = Math.random() * (max - min) + min;
  return new Promise(resolve => setTimeout(resolve, delay));
}

export function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

export function addRandomness(value: number, variance: number): number {
  return value + (Math.random() - 0.5) * 2 * variance * value;
}

// 缓动函数
export const easingFunctions = {
  linear: (t: number): number => t,
  easeIn: (t: number): number => t * t,
  easeOut: (t: number): number => t * (2 - t),
  easeInOut: (t: number): number => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t: number): number => t * t * t,
  easeOutCubic: (t: number): number => 1 - Math.pow(1 - t, 3),
  easeInOutCubic: (t: number): number => 
    t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
};

// 性能工具
export function throttle<T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

export function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

// 内存工具
export function estimateObjectSize(obj: unknown): number {
  if (obj === null || obj === undefined) {
    return 0;
  }
  try {
    const str = JSON.stringify(obj);
    return new Blob([str]).size;
  } catch (e) {
    return estimateNonSerializableObjectSize(obj, e);
  }
}

function estimateNonSerializableObjectSize(obj: unknown, error: unknown): number {
  if (!(error instanceof TypeError)) {
    return 0;
  }
  
  try {
    const fallbackStr = generateFallbackString(obj);
    return new Blob([fallbackStr]).size;
  } catch {
    return 0;
  }
}

function generateFallbackString(obj: unknown): string {
  if (obj == null) {
    return 'null';
  }
  
  if (typeof obj === 'bigint') {
    return obj.toString();
  }
  
  if (typeof obj === 'object') {
    return '[object]';
  }
  
  return String(obj as string | number | boolean);
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

// URL工具
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.href;
  } catch {
    return url;
  }
}

export function extractDomain(url: string): string {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
}

/**
 * 将通配符模式字符串转换为正则表达式。
 * @param pattern - 包含*通配符的模式。
 * @returns 用于匹配的正则表达式对象。
 */
export function wildcardToRegex(pattern: string): RegExp {
  // 引用自: https://stackoverflow.com/a/32402438
  const escapedPattern = pattern.replace(/[.+?^${}()|[\]\\]/g, '\\$&');
  return new RegExp(`^${escapedPattern.replace(/\*/g, '.*')}$`);
}

// 错误处理工具
export class AppError extends Error {
  public readonly context?: Record<string, unknown>;

  constructor(type: string, message: string, context?: Record<string, unknown>) {
    super(message);
    this.name = type;
    this.context = context;
  }
}

export function createError(type: string, message: string, context?: Record<string, unknown>): AppError {
  return new AppError(type, message, context);
}

// 日志工具
export interface Logger {
  debug: (message: string, ...args: unknown[]) => void;
  info: (message: string, ...args: unknown[]) => void;
  warn: (message: string, ...args: unknown[]) => void;
  error: (message: string, ...args: unknown[]) => void;
}

export function createLogger(prefix: string): Logger {
  const styles = {
    prefix: 'color: #3a86ff; font-weight: bold;',
    reset: 'color: inherit; font-weight: normal;',
  };
  return {
    debug: (message, ...args) => console.debug(`%c[${prefix}]%c ${message}`, styles.prefix, styles.reset, ...args),
    info: (message, ...args) => console.info(`%c[${prefix}]%c ${message}`, styles.prefix, styles.reset, ...args),
    warn: (message, ...args) => console.warn(`%c[${prefix}]%c ${message}`, styles.prefix, styles.reset, ...args),
    error: (message, ...args) => console.error(`%c[${prefix}]%c ${message}`, styles.prefix, styles.reset, ...args),
  };
}

// 类型守卫和对象工具
export function isRecord(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export function hasProperty<K extends string>(
  obj: Record<string, unknown>,
  key: K
): obj is Record<K, unknown> {
  return key in obj;
}

export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 处理日期
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    const arrCopy: unknown[] = [];
    for (let i = 0; i < obj.length; i++) {
      arrCopy[i] = deepClone(obj[i]);
    }
    return arrCopy as T;
  }
  
  // 处理通用对象
  const objCopy: Record<string, unknown> = {};
  for (const key in obj) {
    if (Object.hasOwn(obj, key)) {
      objCopy[key] = deepClone((obj as Record<string, unknown>)[key]);
    }
  }
  
  return objCopy as T;
} 