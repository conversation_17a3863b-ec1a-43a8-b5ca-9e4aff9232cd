"""
数据库连接和会话管理
使用SQLAlchemy ORM
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,  # 连接前检查
    pool_recycle=3600,  # 1小时回收连接
    echo=settings.DEBUG,  # 开发环境显示SQL
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables():
    """创建所有数据库表"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {e}")
        raise


def drop_tables():
    """删除所有数据库表 - 仅用于开发环境"""
    if not settings.DEBUG:
        raise RuntimeError("生产环境不允许删除表")

    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("数据库表已删除")
    except Exception as e:
        logger.error(f"数据库表删除失败: {e}")
        raise


def check_database_connection() -> bool:
    """检查数据库连接状态"""
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"数据库连接检查失败: {e}")
        return False


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal

    def get_session(self) -> Session:
        """获取新的数据库会话"""
        return self.SessionLocal()

    def close_session(self, session: Session):
        """关闭数据库会话"""
        try:
            session.close()
        except Exception as e:
            logger.error(f"关闭数据库会话失败: {e}")

    def execute_raw_sql(self, sql: str, params: dict = None):
        """执行原生SQL"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(sql, params or {})
                return result.fetchall()
        except Exception as e:
            logger.error(f"执行SQL失败: {e}")
            raise

    def get_table_info(self, table_name: str) -> dict:
        """获取表信息"""
        try:
            with self.engine.connect() as connection:
                # 获取表结构
                result = connection.execute(
                    f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                """
                )
                columns = result.fetchall()

                # 获取表大小
                size_result = connection.execute(
                    f"""
                    SELECT pg_size_pretty(pg_total_relation_size('{table_name}')) as size
                """
                )
                size = (
                    size_result.fetchone()[0] if size_result.rowcount > 0 else "Unknown"
                )

                return {
                    "table_name": table_name,
                    "columns": [dict(row) for row in columns],
                    "size": size,
                }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {}


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


# 数据库健康检查
def health_check() -> dict:
    """数据库健康检查"""
    try:
        is_connected = check_database_connection()

        if is_connected:
            # 获取连接池状态
            pool = engine.pool
            pool_status = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid(),
            }

            return {"status": "healthy", "connected": True, "pool_status": pool_status}
        else:
            return {
                "status": "unhealthy",
                "connected": False,
                "error": "无法连接到数据库",
            }
    except Exception as e:
        return {"status": "error", "connected": False, "error": str(e)}
