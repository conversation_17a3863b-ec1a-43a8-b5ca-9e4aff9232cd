# Claude Code memory and configuration files (KEEP THESE!)
!.claude/settings.local.json
!.claude-memory/
.claude-memory/tasks/*
!CLAUDE.md

# =============================================================================
# 操作系统特定文件
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# =============================================================================
# 编辑器和IDE特定文件
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.sublime-*

# =============================================================================
# Python 相关
# =============================================================================
# 字节码缓存
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.venv/
venv/
.env/
env/
ENV/
env.bak/
venv.bak/

# 测试和覆盖率
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
htmlcov/
.nox/
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# =============================================================================
# 浏览器相关文件 (Playwright & Browser-Use)
# =============================================================================
# 浏览器配置文件和缓存
.crawler_profile/
.browser_profile/
browser_profile/
playwright-browsers/
ms-playwright/

# 浏览器数据文件
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-shm
*.db-wal

# 浏览器扩展和配置
chrome_extension/
browser_extensions/

# =============================================================================
# 项目特定的运行时文件
# =============================================================================
# 爬虫结果和缓存
.crawler_results/
crawler_results/
results/
output/
downloads/
screenshots/
traces/

# 登录信息和敏感数据
login_info.json
credentials.json
cookies.json
auth_tokens.json
*.token
*.key
secrets/

# OCR 和图片处理结果
ocr_results.json
example_ocr_results.json
processed_images/
temp_images/

# 日志文件
*.log
logs/
log/

# 临时文件
tmp/
temp/
*.tmp
*.temp
.temp/

# =============================================================================
# 环境和配置文件
# =============================================================================
.env
!.env.example
config.local.json
settings.local.json

# =============================================================================
# Node.js 相关 (如果有前端组件)
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# =============================================================================
# 其他常见忽略项
# =============================================================================
# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 备份文件
*.bak
*.orig
*.backup

# 系统文件
.fuse_hidden*
.directory
.Trash-*
.nfs*

# PDF和其他文档
*.pdf
*.doc
*.docx
!docs/**/*.pdf

# 代码行数计算
.VSCodeCounter

# 测试覆盖率统计
coverage
