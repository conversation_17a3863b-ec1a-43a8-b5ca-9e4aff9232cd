# 数懒平台后端环境配置示例
# 复制此文件为 .env 并修改相应配置

# 基础配置
APP_NAME=数懒平台
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-change-in-production-please-use-a-strong-random-key
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/digital_lazy_platform
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# 消息队列配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
TASK_QUEUE_NAME=task_queue
DATA_QUEUE_NAME=data_queue

# 文件存储配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=[".jpg", ".jpeg", ".png", ".pdf", ".txt"]

# AI服务配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=4000

# 任务调度配置
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT_SECONDS=300
RETRY_MAX_ATTEMPTS=3
RETRY_DELAY_SECONDS=5

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# CORS配置
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]

# 浏览器插件配置
EXTENSION_AUTH_TIMEOUT=300
EXTENSION_HEARTBEAT_INTERVAL=30

# 数据处理配置
IMAGE_PROCESSING_QUALITY=85
TEXT_EXTRACTION_CONFIDENCE=0.8
DATA_RETENTION_DAYS=30

# 邮件配置（可选）
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 开发环境特殊配置
# 在生产环境中请删除或设置为false
ENABLE_DOCS=true
ENABLE_REDOC=true
