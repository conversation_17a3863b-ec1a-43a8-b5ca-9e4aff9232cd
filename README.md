# 数懒平台 (Digital Lazy Platform)

[![架构文档](https://img.shields.io/badge/架构文档-完成-green.svg)](./数懒平台系统架构方案.md)
[![技术栈](https://img.shields.io/badge/技术栈-TypeScript%2BReact%2BNestJS-blue.svg)](#技术栈)
[![开发状态](https://img.shields.io/badge/开发状态-插件完成%2C平台开发中-blue.svg)](#开发状态)

## 项目概述

数懒平台是基于现有browser-viewer浏览器插件项目构建的完整企业级智能浏览代理平台。该平台将原有的单机插件功能扩展为包含后端服务、前端管理界面和浏览器插件的完整解决方案。

### 核心组件

1. **后端服务集群** - 提供用户管理、任务调度、数据处理、AI服务等核心功能
2. **前端管理平台** - 提供用户界面、任务管理、数据可视化等管理功能  
3. **浏览器插件** - 基于browser-viewer项目，执行实际的浏览代理任务

## 架构特点

### 继承browser-viewer的优秀设计

- ✅ **智能任务调度**: TaskScheduler的槽位管理和同源亲和性调度算法
- ✅ **微服务架构**: 基于依赖注入的服务容器，职责分离清晰
- ✅ **类型安全通信**: TypedMessenger的消息验证和路由机制
- ✅ **安全隐私保护**: SecurityService的端到端加密和隐私控制
- ✅ **异步I/O解耦**: 高性能的并发处理和资源优化

### 企业级扩展能力

- 🚀 **云原生架构**: 容器化部署，支持弹性伸缩和高可用
- 🔧 **微服务治理**: 服务发现、负载均衡、熔断降级
- 📊 **数据驱动**: 完整的数据处理和分析能力
- 👁️ **可观测性**: 全链路监控、日志追踪、性能分析

## 技术栈

### 后端服务

- **框架**: Python + FastAPI
- **数据库**: PostgreSQL + Redis + MongoDB
- **消息队列**: Redis/RabbitMQ + Kafka
- **容器化**: Docker + Kubernetes
- **AI/ML**: OpenAI API + Transformers + OpenCV

### 前端平台

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design
- **状态管理**: Zustand
- **图表库**: ECharts

### 浏览器插件

- **基础**: browser-viewer项目 (TypeScript + Vite)
- **标准**: Manifest V3
- **UI框架**: Vue 3
- **状态管理**: RxJS
- **兼容性**: WebExtension Polyfill

## 项目结构

```tree
agent-crawl/
├── browser-viewer/                  # 浏览器插件 (基本完成)
│   ├── src/                        # 插件源码
│   ├── dist/                       # 构建输出
│   ├── 数懒浏览器代理-技术方案.md    # 插件技术方案
│   └── package.json
├── backend/                        # Python后端服务
│   ├── app/                        # 应用代码
│   │   ├── main.py                # FastAPI应用入口
│   │   ├── core/                  # 核心配置
│   │   ├── api/                   # API路由
│   │   ├── services/              # 业务服务
│   │   ├── models/                # 数据模型
│   │   ├── schemas/               # Pydantic模式
│   │   ├── utils/                 # 工具函数
│   │   └── workers/               # 后台任务
│   ├── tests/                     # 测试代码
│   ├── requirements.txt           # Python依赖
│   └── README.md
├── frontend/                       # React前端应用
│   ├── src/                       # 前端源码
│   │   ├── App.tsx               # 主应用组件
│   │   ├── components/           # 组件库
│   │   ├── pages/                # 页面组件
│   │   ├── stores/               # 状态管理
│   │   ├── services/             # API服务
│   │   ├── hooks/                # 自定义Hook
│   │   └── types/                # 类型定义
│   ├── package.json              # 前端依赖
│   └── README.md
├── docs/                          # 项目文档
│   ├── 数懒平台-系统架构方案.md     # 系统架构文档
│   ├── 数懒浏览器插件-技术方案.md   # 插件技术文档
│   └── 项目结构说明.md             # 详细结构说明
├── scripts/                       # 构建和部署脚本
├── docker/                        # Docker配置文件
└── README.md                      # 项目总览
```

## 开发状态

### ✅ 已完成

- [x] 系统架构设计
- [x] 技术选型确定
- [x] 实施路线图制定
- [x] **browser-viewer插件** (核心功能基本完成)
  - [x] 智能任务调度系统
  - [x] 人类行为模拟引擎
  - [x] 视觉处理系统
  - [x] 异常检测与恢复机制

### 🚧 进行中 (2025年7月启动)

- [ ] 项目团队组建和分工
- [ ] 开发环境和CI/CD搭建
- [ ] 后端微服务开发
- [ ] 前端管理平台开发

### 📋 计划中

- [ ] 插件与平台深度集成 (2025年9月)
- [ ] AI服务集成 (2025年10月)
- [ ] 性能优化和测试 (2025年11月)
- [ ] 生产部署和上线 (2025年12月)

## 实施计划

### 第一阶段：基础设施 (2周)

- 基础架构搭建
- 数据库设计
- API网关部署

### 第二阶段：核心服务 (3周)

- 用户认证服务
- 任务编排服务
- 数据处理服务

### 第三阶段：前端平台 (3周)

- 前端框架搭建
- 任务管理界面
- 数据可视化

### 第四阶段：插件集成 (3周)

- 插件认证集成
- 通信协议实现
- 数据同步机制

### 第五阶段：AI服务 (3周)

- 视觉识别服务
- 文本处理服务
- 模型管理服务

### 第六阶段：测试优化 (3周)

- 集成测试
- 性能优化
- 安全加固

### 第七阶段：部署上线 (2周)

- 生产环境部署
- 监控告警配置
- 用户培训文档

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- Docker >= 20.0.0
- Kubernetes >= 1.24
- PostgreSQL >= 14
- Redis >= 6.0

### 开发环境搭建

1. **克隆项目**

```bash
git clone <repository-url>
cd agent-crawl
```

2. **浏览器插件开发**

```bash
cd browser-viewer
npm install
npm run dev
```

3. **后端服务开发** (Python + FastAPI)

```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. **前端平台开发** (React + TypeScript)

```bash
cd frontend
npm install
npm run dev
```

## 文档

- 📖 [完整架构设计文档](./数懒平台系统架构方案.md)
- 🔧 [browser-viewer技术方案](./browser-viewer/数懒孪生代理-技术方案.md)
- 🚀 [API接口设计](./docs/api-design.md) (待创建)
- 💾 [数据库设计](./docs/database-design.md) (待创建)
- 🛠️ [部署运维手册](./docs/deployment-guide.md) (待创建)

## 贡献指南

1. 阅读[架构设计文档](./数懒平台系统架构方案.md)
2. 了解技术栈和开发规范
3. 按照实施路线图进行开发
4. 提交代码前进行测试和代码审查
5. 更新相关文档

## 许可证

[MIT License](./LICENSE)

## 联系方式

- **项目负责人**: [项目经理姓名]
- **技术负责人**: [技术负责人姓名]
- **架构师**: [架构师姓名]
- **项目邮箱**: [项目邮箱地址]

---

> 本项目严格按照[数懒平台系统架构方案](./数懒平台系统架构方案.md)进行开发，所有开发工作应遵循架构设计和技术规范。
