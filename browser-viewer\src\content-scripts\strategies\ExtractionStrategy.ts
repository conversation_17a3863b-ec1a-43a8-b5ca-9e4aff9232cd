/**
 * 提取策略接口
 * 定义了所有具体提取策略必须实现的契约。
 */
import { ServiceMessage } from '@/shared/types';

export interface ExtractionResult {
  success: boolean;
  data?: unknown;
  error?: string;
}

export interface ExtractionStrategy {
  /**
   * 执行提取策略。
   * @param window - 内容脚本执行的window对象。
   * @param taskId - 当前任务的ID。
   * @param sessionId - 当前捕捉会话的ID。
   * @returns 返回一个包含提取结果的对象。
   */
  execute(window: Window, taskId: string, sessionId: string): Promise<ExtractionResult>;
}

/**
 * 消息构建器接口，用于将策略结果转换为标准的服务消息。
 */
export interface StrategyMessageBuilder {
  (payload: { taskId: string; sessionId: string; result: ExtractionResult }): Omit<ServiceMessage<any>, 'id' | 'timestamp'>;
} 