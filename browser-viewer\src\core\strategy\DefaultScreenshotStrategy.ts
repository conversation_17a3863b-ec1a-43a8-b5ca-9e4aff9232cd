import { HumanBehaviorSimulator } from '@/core/behavior-engine/HumanBehaviorSimulator';
import { createLogger, Lo<PERSON> } from '@/shared/utils';
import { createContext, destroyContext, domToPng, type Context } from 'modern-screenshot';
import type { ExtractionResult, ExtractionStrategy } from './ExtractionStrategy';

const SCAN_CONSTANTS = {
  MAX_CAPTURE_PARTS: 50,
  MAX_CAPTURE_HEIGHT_PX: 40000,
} as const;

/**
 * 默认的提取策略，它执行全页截图，用于没有特定策略的网站。
 */
export class DefaultScreenshotStrategy implements ExtractionStrategy {
  private readonly _logger: Logger = createLogger('DefaultScreenshotStrategy');
  private _screenshotContext: Context | null = null;
  
  async execute(context: Window, taskId: string, sessionId: string): Promise<ExtractionResult> {
    this._logger.info('Executing default screenshot strategy...');
    
    const simulator = new HumanBehaviorSimulator('normal');
    let partIndex = 0;
    let capturedHeightPx = 0;

    try {
      this._screenshotContext = await createContext(context.document.body, {
        fetch: {
          requestInit: { credentials: 'include' },
          placeholderImage: 'data:image/png;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        },
        fetchFn: (url) => this._fetchFnForScreenshot(url, context.location.href),
        filter: (node: Node) => {
          if (node.nodeType !== Node.ELEMENT_NODE) return true;
          const element = node as Element;
          if (['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(element.tagName)) return false;
          if (element.tagName === 'IFRAME') return false;
          return true;
        },
      });

      const requestRegionCapture = async (region: { y: number, height: number, width: number }): Promise<void> => {
        if (partIndex >= SCAN_CONSTANTS.MAX_CAPTURE_PARTS || capturedHeightPx >= SCAN_CONSTANTS.MAX_CAPTURE_HEIGHT_PX) {
          throw new Error('Scan halted due to reaching limits.');
        }
        capturedHeightPx = region.y + region.height;
        await this._captureAndProcessRegion(taskId, sessionId, region);
        partIndex++;
      };

      await simulator.simulateFullPageScan(context, requestRegionCapture);
      
      return { success: true, data: 'Full page scan completed' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this._logger.error('Default screenshot strategy failed:', errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      if (this._screenshotContext) {
        destroyContext(this._screenshotContext);
        this._screenshotContext = null;
      }
    }
  }

  private async _captureAndProcessRegion(taskId: string, sessionId: string, region: { y: number, height: number, width: number }): Promise<void> {
    if (!this._screenshotContext) throw new Error('Screenshot context not initialized.');
    
    this._screenshotContext.width = region.width;
    this._screenshotContext.height = region.height;
    this._screenshotContext.style = { ...this._screenshotContext.style, transform: `translate(0, -${region.y}px)`, margin: '0' };
    
    const dataUrl = await domToPng(this._screenshotContext);
    if (!dataUrl?.startsWith('data:image')) throw new Error('Failed to generate a valid data URL.');
    
    // 发送截图数据到后台
    await chrome.runtime.sendMessage({
      type: 'CAPTURE_AND_PROCESS_VIEWPORT',
      payload: { taskId, sessionId, imageDataUrl: dataUrl, y: region.y, height: region.height, width: region.width },
    });
  }

  private async _fetchFnForScreenshot(url: string, referer: string): Promise<string | false> {
    if (!url.startsWith('http')) return false;
    
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'FETCH_RESOURCE_AS_DATA_URL',
        payload: { url, referer },
      });
      return response.success && response.data ? response.data : false;
    } catch (error) {
      this._logger.error('Error proxying resource for screenshot:', error);
      return false;
    }
  }
} 