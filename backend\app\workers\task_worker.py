"""
任务处理器
处理任务执行的后台工作
"""

import asyncio
from typing import Dict, Any
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.task import Task, TaskStatus
from app.services.task_orchestrator import TaskOrchestratorService
from app.utils.message_queue import get_task_message_queue
from app.utils.logger import get_logger

logger = get_logger(__name__)


class TaskWorker:
    """任务处理器"""

    def __init__(self):
        self.running = False

    async def start(self):
        """启动任务处理器"""
        if self.running:
            return

        self.running = True
        logger.info("Task worker started")

        # 订阅任务消息
        task_mq = await get_task_message_queue()
        await task_mq.subscribe("task.created", self.handle_task_created)
        await task_mq.subscribe("task.started", self.handle_task_started)
        await task_mq.subscribe("task.progress", self.handle_task_progress)
        await task_mq.subscribe("task.completed", self.handle_task_completed)

        # 启动消费者
        await task_mq.start_consumer()

    async def stop(self):
        """停止任务处理器"""
        self.running = False
        logger.info("Task worker stopped")

    async def handle_task_created(self, message: Dict[str, Any]):
        """处理任务创建消息"""
        try:
            task_id = message.get("task_id")
            user_id = message.get("user_id")
            config = message.get("config", {})

            logger.info(f"Processing task created: {task_id}")

            # 这里可以添加任务创建后的处理逻辑
            # 例如：验证配置、预处理数据等

            # 模拟任务处理
            await self._simulate_task_execution(task_id, user_id, config)

        except Exception as e:
            logger.error(f"Error handling task created: {e}")

    async def handle_task_started(self, message: Dict[str, Any]):
        """处理任务开始消息"""
        try:
            task_id = message.get("task_id")
            user_id = message.get("user_id")

            logger.info(f"Task started: {task_id}")

            # 这里可以添加任务开始时的处理逻辑

        except Exception as e:
            logger.error(f"Error handling task started: {e}")

    async def handle_task_progress(self, message: Dict[str, Any]):
        """处理任务进度消息"""
        try:
            task_id = message.get("task_id")
            progress = message.get("progress")

            logger.debug(f"Task progress: {task_id} - {progress}%")

            # 这里可以添加进度处理逻辑

        except Exception as e:
            logger.error(f"Error handling task progress: {e}")

    async def handle_task_completed(self, message: Dict[str, Any]):
        """处理任务完成消息"""
        try:
            task_id = message.get("task_id")
            status = message.get("status")
            result = message.get("result")

            logger.info(f"Task completed: {task_id} with status {status}")

            # 这里可以添加任务完成后的处理逻辑
            # 例如：清理资源、发送通知等

        except Exception as e:
            logger.error(f"Error handling task completed: {e}")

    async def _simulate_task_execution(
        self, task_id: str, user_id: str, config: Dict[str, Any]
    ):
        """模拟任务执行（实际项目中应该调用browser-viewer）"""
        try:
            db = next(get_db())
            orchestrator = TaskOrchestratorService(db)

            # 模拟任务执行过程
            await asyncio.sleep(1)  # 模拟启动时间

            # 更新进度
            for progress in [10, 30, 50, 70, 90, 100]:
                await orchestrator.update_task_progress(
                    task_id, progress, f"Processing... {progress}%"
                )
                await asyncio.sleep(2)  # 模拟处理时间

            # 完成任务
            result = {
                "processed_urls": config.get("urls", []),
                "success_count": len(config.get("urls", [])),
                "failure_count": 0,
                "extracted_data": {
                    "total_items": 10,
                    "data_types": ["text", "image", "link"],
                },
            }

            await orchestrator.complete_task(task_id, result)

            logger.info(f"Task simulation completed: {task_id}")

        except Exception as e:
            logger.error(f"Task simulation error: {e}")

            # 标记任务失败
            try:
                db = next(get_db())
                orchestrator = TaskOrchestratorService(db)
                await orchestrator.complete_task(task_id, error=str(e))
            except Exception as cleanup_error:
                logger.error(f"Error marking task as failed: {cleanup_error}")


# 全局任务处理器实例
_task_worker: TaskWorker = None


async def get_task_worker() -> TaskWorker:
    """获取任务处理器实例"""
    global _task_worker

    if _task_worker is None:
        _task_worker = TaskWorker()

    return _task_worker


async def start_task_worker():
    """启动任务处理器"""
    worker = await get_task_worker()
    await worker.start()


async def stop_task_worker():
    """停止任务处理器"""
    global _task_worker

    if _task_worker:
        await _task_worker.stop()
        _task_worker = None
