/**
 * Options页面逻辑
 */

import { PrivacyService } from '@/services/privacy-service';
import { BEHAVIOR_PROFILES, STORAGE_KEYS } from '@/shared/constants';
import type { AgentConfig, DataAccessEvent, PrivacySettings } from '@/shared/types';
import { createLogger, getStorageData, setStorageData } from '@/shared/utils';
import '../main.css';

type CaptureFormat = 'png' | 'jpeg' | 'webp';
function isCaptureFormat(value: string): value is CaptureFormat {
  return ['png', 'jpeg', 'webp'].includes(value);
}

class OptionsPage {
  private readonly _logger = createLogger('OptionsPage');
  private readonly _privacyService: PrivacyService;
  
  constructor() {
    this._privacyService = PrivacyService.getInstance();
    // 延迟初始化，避免在构造函数中执行异步操作
  }

  /**
   * 公共初始化方法
   */
  public async initializeOptions(): Promise<void> {
    await this._initialize();
  }
  
  private async _initialize(): Promise<void> {
    this._logger.info('Initializing options page');
    
    try {
      await this._loadSettings();
      this._setupEventListeners();
      await this._refreshPrivacyLog(); // 初始化时加载日志
      await this._initializePrivacySettings();
      this._logger.info('Options page initialized');
    } catch (error) {
      this._logger.error('Failed to initialize options page:', error);
      this._showMessage('初始化失败', 'error');
    }
  }
  
  private async _loadSettings(): Promise<void> {
    const defaultConfig: AgentConfig = {
      maxConcurrentTasks: 3,
      resourceThresholds: {
        CPU: 0.7,
        MEMORY: 1024,
        NETWORK: 5,
        FPS: 30
      },
      behaviorProfiles: BEHAVIOR_PROFILES,
      captureOptions: {
        quality: 80,
        blockSize: 1024,
        overlap: 50,
        format: 'jpeg'
      },
      permissions: {
        domains: [],
        cookies: true,
        localStorage: true,
        screenshots: true,
        automation: true
      },
      ai: {
        provider: 'default',
        model: 'default'
      }
    };
    
    const config = await getStorageData(STORAGE_KEYS.CONFIG, defaultConfig);
    this._populateForm(config);
    
    const defaultPrivacySettings: PrivacySettings = {
      cookieDeniedDomains: [],
      enableAuditLog: true,
      proxyUrlBlacklist: [],
    };
    const privacySettings = await getStorageData(STORAGE_KEYS.PRIVACY_SETTINGS, defaultPrivacySettings);
    this._populatePrivacyForm(privacySettings);
  }
  
  private _populateForm(config: AgentConfig): void {
    // 任务设置
    this._setValue('maxConcurrentTasks', config.maxConcurrentTasks);
    this._setValue('defaultBehaviorProfile', 'normal'); // 简化处理
    this._setChecked('enableDistraction', true); // 简化处理
    
    // 性能设置
    this._setValue('cpuThreshold', Math.round(config.resourceThresholds.CPU * 100));
    this._setValue('memoryThreshold', config.resourceThresholds.MEMORY);
    this._setChecked('adaptiveSpeed', true); // 简化处理
    
    // 捕获设置
    this._setValue('imageQuality', config.captureOptions.quality);
    this._setValue('blockSize', config.captureOptions.blockSize);
    this._setValue('imageFormat', config.captureOptions.format);
    
    // 隐私设置
    this._setChecked('allowCookies', config.permissions.cookies);
    this._setChecked('allowLocalStorage', config.permissions.localStorage);
    this._setChecked('allowScreenshots', config.permissions.screenshots);
  }

  private _populatePrivacyForm(settings: PrivacySettings): void {
    this._setTextareaValue('proxyUrlBlacklist', settings.proxyUrlBlacklist.join('\\n'));
    this._setTextareaValue('cookieDeniedDomains', settings.cookieDeniedDomains.join('\\n'));
    this._setChecked('enableAuditLog', settings.enableAuditLog);
  }
  
  private _setupEventListeners(): void {
    const saveBtn = document.getElementById('saveBtn');
    const resetBtn = document.getElementById('resetBtn');
    const refreshLogBtn = document.getElementById('refreshLogBtn');
    const clearLogBtn = document.getElementById('clearLogBtn');
    const savePrivacyBtn = document.getElementById('savePrivacySettings');
    
    saveBtn?.addEventListener('click', () => { void this._saveSettings(); });
    resetBtn?.addEventListener('click', () => { void this._resetSettings(); });
    refreshLogBtn?.addEventListener('click', () => { void this._refreshPrivacyLog(); });
    clearLogBtn?.addEventListener('click', () => { void this._clearPrivacyLog(); });
    savePrivacyBtn?.addEventListener('click', () => { void this._savePrivacySettings(); });
  }
  
  private async _saveSettings(): Promise<void> {
    try {
      const imageFormat = this._getStringValue('imageFormat');
      if (!isCaptureFormat(imageFormat)) {
        throw new Error(`Invalid image format: ${imageFormat}`);
      }
      
      const config: AgentConfig = {
        maxConcurrentTasks: this._getNumberValue('maxConcurrentTasks'),
        resourceThresholds: {
          CPU: this._getNumberValue('cpuThreshold') / 100,
          MEMORY: this._getNumberValue('memoryThreshold'),
          NETWORK: 5, // 固定值
          FPS: 30 // 固定值
        },
        behaviorProfiles: BEHAVIOR_PROFILES,
        captureOptions: {
          quality: this._getNumberValue('imageQuality'),
          blockSize: this._getNumberValue('blockSize'),
          overlap: 50, // 固定值
          format: imageFormat
        },
        permissions: {
          domains: [], // 简化处理
          cookies: this._getChecked('allowCookies'),
          localStorage: this._getChecked('allowLocalStorage'),
          screenshots: this._getChecked('allowScreenshots'),
          automation: true // 固定值
        },
        ai: {
          provider: 'default',
          model: 'default'
        }
      };
      
      await setStorageData(STORAGE_KEYS.CONFIG, config);

      const privacySettings: PrivacySettings = {
        proxyUrlBlacklist: this._getTextareaValue('proxyUrlBlacklist').split('\\n').filter(Boolean),
        cookieDeniedDomains: this._getTextareaValue('cookieDeniedDomains').split('\\n').filter(Boolean),
        enableAuditLog: this._getChecked('enableAuditLog'),
      };
      await setStorageData(STORAGE_KEYS.PRIVACY_SETTINGS, privacySettings);
      
      this._showMessage('设置已保存', 'success');
      
    } catch (error) {
      this._logger.error('Failed to save settings:', error);
      this._showMessage('保存失败', 'error');
    }
  }
  
  private async _resetSettings(): Promise<void> {
    try {
      await chrome.storage.local.remove(STORAGE_KEYS.CONFIG);
      await chrome.storage.local.remove(STORAGE_KEYS.PRIVACY_SETTINGS);
      await this._loadSettings();
      this._showMessage('设置已重置', 'success');
    } catch (error) {
      this._logger.error('Failed to reset settings:', error);
      this._showMessage('重置失败', 'error');
    }
  }
  
  private _setValue(id: string, value: string | number): void {
    const element = document.getElementById(id) as HTMLInputElement | HTMLSelectElement;
    if (element) {
      element.value = String(value);
    }
  }
  
  private _setChecked(id: string, checked: boolean): void {
    const element = document.getElementById(id) as HTMLInputElement;
    if (element) {
      element.checked = checked;
    }
  }
  
  private _setTextareaValue(id: string, value: string): void {
    const element = document.getElementById(id) as HTMLTextAreaElement;
    if (element) {
      element.value = value;
    }
  }
  
  private _getNumberValue(id: string): number {
    const element = document.getElementById(id) as HTMLInputElement;
    return element ? Number(element.value) : 0;
  }
  
  private _getStringValue(id: string): string {
    const element = document.getElementById(id) as HTMLInputElement | HTMLSelectElement;
    return element ? element.value : '';
  }
  
  private _getTextareaValue(id: string): string {
    const element = document.getElementById(id) as HTMLTextAreaElement;
    return element ? element.value : '';
  }
  
  private _getChecked(id: string): boolean {
    const element = document.getElementById(id) as HTMLInputElement;
    return element ? element.checked : false;
  }
  
  private _showMessage(message: string, type: 'success' | 'error'): void {
    const statusElement = document.getElementById('statusMessage');
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = `status-message ${type}`;
      statusElement.style.display = 'block';
      
      setTimeout(() => {
        statusElement.style.display = 'none';
      }, 3000);
    }
  }
  
  private async _refreshPrivacyLog(): Promise<void> {
    const logElement = document.getElementById('privacyLog');
    if (!logElement) return;

    try {
      logElement.textContent = '正在加载日志...';
      const logs = await this._privacyService.getPrivacyLog();
      if (logs.length === 0) {
        logElement.textContent = '暂无数据访问记录。';
        return;
      }
      logElement.textContent = logs.map(log => {
        const logWithTimestamp = log as DataAccessEvent & { timestamp: number };
        return `[${new Date(logWithTimestamp.timestamp).toLocaleString()}] [${log.category.toUpperCase()}] ${log.type} by ${log.accessedBy}` +
          (log.context ? ` - Context: ${JSON.stringify(log.context)}` : '');
      }).join('\\n');
    } catch (error) {
      this._logger.error('Failed to refresh privacy log:', error);
      logElement.textContent = '加载日志失败。';
    }
  }

  private async _clearPrivacyLog(): Promise<void> {
    if (!confirm('确定要清空所有隐私审计日志吗？此操作不可撤销。')) {
      return;
    }
    try {
      await this._privacyService.clearPrivacyLog();
      await this._refreshPrivacyLog();
      this._showMessage('日志已清空', 'success');
    } catch (error) {
      this._logger.error('Failed to clear privacy log:', error);
      this._showMessage('清空日志失败', 'error');
    }
  }

  /**
   * 隐私设置管理
   */
  private async _initializePrivacySettings(): Promise<void> {
    try {
      const settings = await this._privacyService.getPrivacySettings();
      
      // 如果页面有隐私设置元素，填充当前值
      const proxyBlacklistInput = document.getElementById('proxyBlacklist') as HTMLTextAreaElement;
      const cookieBlacklistInput = document.getElementById('cookieBlacklist') as HTMLTextAreaElement;
      const auditLogCheckbox = document.getElementById('enableAuditLog') as HTMLInputElement;
      
      if (proxyBlacklistInput) {
        proxyBlacklistInput.value = settings.proxyUrlBlacklist.join('\n');
      }
      
      if (cookieBlacklistInput) {
        cookieBlacklistInput.value = settings.cookieDeniedDomains.join('\n');
      }
      
      if (auditLogCheckbox) {
        auditLogCheckbox.checked = settings.enableAuditLog;
      }
      
      this._logger.info('Privacy settings initialized');
    } catch (error) {
      this._logger.error('Failed to initialize privacy settings:', error);
    }
  }

  /**
   * 保存隐私设置
   */
  private async _savePrivacySettings(): Promise<void> {
    try {
      const proxyBlacklistInput = document.getElementById('proxyBlacklist') as HTMLTextAreaElement;
      const cookieBlacklistInput = document.getElementById('cookieBlacklist') as HTMLTextAreaElement;
      const auditLogCheckbox = document.getElementById('enableAuditLog') as HTMLInputElement;
      
      const settings = {
        proxyUrlBlacklist: proxyBlacklistInput?.value.split('\n').filter(line => line.trim()) || [],
        cookieDeniedDomains: cookieBlacklistInput?.value.split('\n').filter(line => line.trim()) || [],
        enableAuditLog: auditLogCheckbox?.checked ?? true
      };
      
      await this._privacyService.updatePrivacySettings(settings);
      this._showMessage('隐私设置已保存', 'success');
      
      this._logger.info('Privacy settings saved successfully');
    } catch (error) {
      this._logger.error('Failed to save privacy settings:', error);
      this._showMessage('保存隐私设置失败', 'error');
    }
  }
}

// 初始化选项页面
document.addEventListener('DOMContentLoaded', () => {
  const optionsPage = new OptionsPage();
  // OptionsPage实例已初始化并自动设置事件监听器
  console.info('Options page initialized', optionsPage);
});