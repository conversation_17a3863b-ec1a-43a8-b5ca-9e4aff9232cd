/**
 * Image Processor Worker - 在独立线程中处理图像分段。
 *
 * 该Worker是一个功能完备的、独立的图像处理单元。它复制了 VisionService 的核心逻辑，
 * 包括结果缓存、内容后处理和健壮的错误处理，但不依赖任何浏览器特定API，
 * 确保其可以在受限的Worker环境中稳定运行。
 */
import type { AIConfig, ContentSegment, ImageSegment } from '@/shared/types';

// #region 独立工具函数与服务逻辑
// 为Worker创建一个独立的、无依赖的Logger
const logger = {
  info: (...args: unknown[]) => console.log('[ImageProcessorWorker]', ...args),
  warn: (...args: unknown[]) => console.warn('[ImageProcessorWorker]', ...args),
  error: (...args: unknown[]) => console.error('[ImageProcessorWorker]', ...args),
};

/**
 * 一个简单的、非加密的哈希函数，用于为ArrayBuffer生成一个相对唯一的缓存键。
 * @param buffer 输入的ArrayBuffer。
 * @returns 一个数字哈希值。
 */
function simpleHash(buffer: ArrayBuffer): number {
  const view = new Uint8Array(buffer);
  let hash = 0;
  const step = Math.max(1, Math.floor(view.length / 256));
  for (let i = 0; i < view.length; i += step) {
    const char = view[i];
    hash = ((hash << 5) - hash) + char;
    hash |= 0;
  }
  return hash;
}

/**
 * 清理AI模型返回的文本。
 * @param text 从AI模型获取的原始文本。
 * @returns 清理后的文本。
 */
function postProcessText(text: string): string {
  let processedText = text.trim();
  processedText = processedText.replace(/^```[\s\S]*?\n/, '').replace(/```$/, '');
  return processedText.trim();
}

/**
 * 将图像数据（ArrayBuffer）转换为Base64字符串。
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return self.btoa(binary);
}

/**
 * 使用纯粹的fetch调用AI服务，使Worker保持独立。
 */
async function callVisionAPI(imageSegment: ImageSegment, config: AIConfig): Promise<ContentSegment> {
  const base64Image = arrayBufferToBase64(imageSegment.data);
  const imageUrl = `data:${imageSegment.mimeType};base64,${base64Image}`;

  const requestBody = {
    model: config.model,
    messages: [
      {
        role: 'user',
        content: [
          { type: 'text', text: config.prompt },
          { type: 'image_url', image_url: { url: imageUrl } },
        ],
      },
    ],
    max_tokens: 1500,
  };

  const response = await fetch(config.apiEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorBody = await response.text();
    throw new Error(`AI API request failed with status ${response.status}: ${errorBody}`);
  }

  const result = await response.json() as {
    choices?: Array<{ message?: { content?: string }; finish_details?: { confidence?: number; } }>;
  };

  if (!result.choices || result.choices.length === 0 || !result.choices[0]?.message?.content) {
    throw new Error('无效的API响应结构。');
  }
  
  const rawText = result.choices[0].message.content;
  const extractedText = postProcessText(rawText);

  return {
    id: `content-${imageSegment.id}`,
    type: 'text',
    content: extractedText,
    position: imageSegment.position,
    confidence: result.choices[0].finish_details?.confidence ?? 0.9,
  };
}

// #endregion

// #region Worker 主体逻辑
// VisionService 的核心功能在 Worker 内部的实现
const cache = new Map<number, ContentSegment>();
logger.info('Image Processor Worker initialized with cache support.');

interface WorkerMessage {
  segment: ImageSegment;
  isDev: boolean;
  aiConfig: AIConfig; // 从主线程接收AI配置
}

self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { segment: imageSegment, isDev, aiConfig } = event.data;
  
  try {
    const cacheKey = simpleHash(imageSegment.data);
    if (cache.has(cacheKey)) {
      logger.info(`缓存命中 for segment ${imageSegment.id} (hash: ${cacheKey})`);
      const cachedSegment = cache.get(cacheKey);
      if (!cachedSegment) {
        throw new Error('Cache entry unexpectedly missing');
      }
      (self as DedicatedWorkerGlobalScope).postMessage({
          ...cachedSegment, 
          id: `content-${imageSegment.id}`,
          position: imageSegment.position 
      });
      return;
    }
    
    logger.info(`缓存未命中 for segment ${imageSegment.id} (hash: ${cacheKey}), 开始处理 (isDev: ${isDev})。`);

    let contentSegment: ContentSegment;
    
    if (isDev) {
      logger.info(`[DEV MODE] Skipping AI processing for segment ${imageSegment.id}`);
      contentSegment = {
        id: imageSegment.id,
        type: 'text',
        content: `[DEV MODE] Image segment ${imageSegment.id} captured. AI processing skipped.`,
        position: imageSegment.position,
        confidence: 1.0,
      };
    } else {
      logger.info(`Processing segment ${imageSegment.id} with AI model ${aiConfig.model}`);
      contentSegment = await callVisionAPI(imageSegment, aiConfig);
    }
    
    cache.set(cacheKey, contentSegment);
    (self as DedicatedWorkerGlobalScope).postMessage(contentSegment);
    logger.info(`图像分段 ${imageSegment.id} 处理完成并已发送结果。`);

  } catch (error) {
    logger.error(`处理图像分段 ${imageSegment.id} 时发生错误:`, error);
    // 错误处理：返回一个空的、低置信度的分段，以保证上游任务流程不中断
    const errorSegment: ContentSegment = {
      id: `content-${imageSegment.id}`,
      position: imageSegment.position,
      content: '',
      confidence: 0,
      type: 'text',
    };
    (self as DedicatedWorkerGlobalScope).postMessage(errorSegment);
  } finally {
    if (imageSegment.bitmap) {
      imageSegment.bitmap.close();
    }
  }
};

self.onerror = (error) => {
  logger.error('Worker中发生未捕获的错误:', error);
};
// #endregion 