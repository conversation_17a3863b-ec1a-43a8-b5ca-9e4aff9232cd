#!/bin/bash

# 数懒平台开发服务器停止脚本
# 用于停止所有开发服务

echo "🛑 停止数懒平台开发服务器..."

# 停止应用进程
stop_processes() {
    echo "停止应用进程..."
    
    # 停止FastAPI服务
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    
    # 停止Vite开发服务器
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    
    # 停止Node.js进程
    pkill -f "node.*vite" 2>/dev/null || true
    
    echo "✅ 应用进程已停止"
}

# 停止数据库容器（可选）
stop_databases() {
    if command -v docker &> /dev/null; then
        echo "停止数据库容器..."
        
        # 停止但不删除容器，以保留数据
        docker stop digital-lazy-postgres 2>/dev/null || true
        docker stop digital-lazy-redis 2>/dev/null || true
        
        echo "✅ 数据库容器已停止"
    fi
}

# 清理临时文件
cleanup_temp() {
    echo "清理临时文件..."
    
    # 清理Python缓存
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理Node.js缓存
    rm -rf frontend/.vite 2>/dev/null || true
    
    echo "✅ 临时文件已清理"
}

# 显示状态
show_status() {
    echo ""
    echo "📊 服务状态检查："
    
    # 检查端口占用
    if lsof -i :8000 >/dev/null 2>&1; then
        echo "   ❌ 端口 8000 仍被占用"
    else
        echo "   ✅ 端口 8000 已释放"
    fi
    
    if lsof -i :3000 >/dev/null 2>&1; then
        echo "   ❌ 端口 3000 仍被占用"
    else
        echo "   ✅ 端口 3000 已释放"
    fi
    
    # 检查Docker容器状态
    if command -v docker &> /dev/null; then
        if docker ps | grep -q "digital-lazy-postgres"; then
            echo "   🔄 PostgreSQL 容器仍在运行"
        else
            echo "   ⏹️  PostgreSQL 容器已停止"
        fi
        
        if docker ps | grep -q "digital-lazy-redis"; then
            echo "   🔄 Redis 容器仍在运行"
        else
            echo "   ⏹️  Redis 容器已停止"
        fi
    fi
}

# 强制停止（可选参数）
force_stop() {
    echo "🔥 强制停止所有相关进程..."
    
    # 强制杀死所有相关进程
    pkill -9 -f "uvicorn" 2>/dev/null || true
    pkill -9 -f "vite" 2>/dev/null || true
    pkill -9 -f "node.*3000" 2>/dev/null || true
    
    # 强制停止并删除容器
    if command -v docker &> /dev/null; then
        docker kill digital-lazy-postgres digital-lazy-redis 2>/dev/null || true
        docker rm digital-lazy-postgres digital-lazy-redis 2>/dev/null || true
    fi
    
    echo "✅ 强制停止完成"
}

# 重置环境（可选参数）
reset_environment() {
    echo "🔄 重置开发环境..."
    
    force_stop
    
    # 清理所有临时文件和缓存
    cleanup_temp
    
    # 清理日志文件
    rm -rf backend/logs/* 2>/dev/null || true
    
    # 清理上传文件（谨慎操作）
    read -p "是否清理上传文件？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf backend/uploads/* 2>/dev/null || true
        echo "✅ 上传文件已清理"
    fi
    
    echo "✅ 环境重置完成"
}

# 主函数
main() {
    case "${1:-stop}" in
        "stop")
            stop_processes
            cleanup_temp
            show_status
            ;;
        "stop-all")
            stop_processes
            stop_databases
            cleanup_temp
            show_status
            ;;
        "force")
            force_stop
            cleanup_temp
            show_status
            ;;
        "reset")
            reset_environment
            show_status
            ;;
        "help")
            echo "数懒平台开发服务器停止脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  stop      停止应用进程（默认）"
            echo "  stop-all  停止应用进程和数据库容器"
            echo "  force     强制停止所有进程和容器"
            echo "  reset     重置整个开发环境"
            echo "  help      显示此帮助信息"
            echo ""
            ;;
        *)
            echo "❌ 未知选项: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"

echo ""
echo "✅ 数懒平台开发服务器已停止"
echo ""
echo "💡 提示："
echo "   重新启动: ./start-dev.sh"
echo "   查看帮助: ./stop-dev.sh help"
echo ""
