/**
 * 任务管理页面
 * 任务列表、创建、编辑、监控
 */

import React, { useEffect, useState } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  Select,
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Tooltip,
  Popconfirm,
  message,
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  FilterOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import { useTaskStore, TaskStatus, TaskPriority } from '../../stores/taskStore'
import { CreateTaskData } from '../../types/task'

const { Title } = Typography
const { Option } = Select
const { TextArea } = Input

const TaskManagement: React.FC = () => {
  const navigate = useNavigate()
  const {
    tasks,
    loading,
    pagination,
    filters,
    fetchTasks,
    createTask,
    startTask,
    pauseTask,
    cancelTask,
    deleteTask,
    setFilters,
    setPagination,
  } = useTaskStore()

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [createForm] = Form.useForm()

  useEffect(() => {
    fetchTasks({
      page: pagination.current,
      size: pagination.pageSize,
      ...filters,
    })
  }, [fetchTasks, pagination.current, pagination.pageSize, filters])

  // 计算统计数据
  const stats = {
    total: tasks.length,
    running: tasks.filter(task => task.status === TaskStatus.RUNNING).length,
    completed: tasks.filter(task => task.status === TaskStatus.COMPLETED).length,
    failed: tasks.filter(task => task.status === TaskStatus.FAILED).length,
  }

  // 状态配置
  const statusConfig = {
    [TaskStatus.PENDING]: { color: 'default', text: '等待中' },
    [TaskStatus.RUNNING]: { color: 'processing', text: '运行中' },
    [TaskStatus.COMPLETED]: { color: 'success', text: '已完成' },
    [TaskStatus.FAILED]: { color: 'error', text: '失败' },
    [TaskStatus.CANCELLED]: { color: 'default', text: '已取消' },
    [TaskStatus.PAUSED]: { color: 'warning', text: '已暂停' },
  }

  // 优先级配置
  const priorityConfig = {
    [TaskPriority.LOW]: { color: 'default', text: '低' },
    [TaskPriority.NORMAL]: { color: 'blue', text: '普通' },
    [TaskPriority.HIGH]: { color: 'orange', text: '高' },
    [TaskPriority.URGENT]: { color: 'red', text: '紧急' },
  }

  // 表格列定义
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Button
          type="link"
          onClick={() => navigate(`/tasks/${record.id}`)}
          style={{ padding: 0 }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TaskStatus) => {
        const config = statusConfig[status]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: TaskPriority) => {
        const config = priorityConfig[priority]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '进度',
      dataIndex: 'progressPercentage',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: 'URL数量',
      key: 'urls',
      render: (record: any) => (
        <span>
          {record.processedUrls} / {record.totalUrls}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/tasks/${record.id}`)}
            />
          </Tooltip>
          
          {record.status === TaskStatus.PENDING && (
            <Tooltip title="启动任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStartTask(record.id)}
              />
            </Tooltip>
          )}
          
          {record.status === TaskStatus.RUNNING && (
            <Tooltip title="暂停任务">
              <Button
                type="text"
                icon={<PauseCircleOutlined />}
                onClick={() => handlePauseTask(record.id)}
              />
            </Tooltip>
          )}
          
          {[TaskStatus.RUNNING, TaskStatus.PAUSED].includes(record.status) && (
            <Tooltip title="取消任务">
              <Button
                type="text"
                icon={<StopOutlined />}
                onClick={() => handleCancelTask(record.id)}
              />
            </Tooltip>
          )}
          
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleDeleteTask(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除任务">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 处理任务操作
  const handleStartTask = async (taskId: string) => {
    try {
      await startTask(taskId)
      message.success('任务已启动')
    } catch (error) {
      message.error('启动任务失败')
    }
  }

  const handlePauseTask = async (taskId: string) => {
    try {
      await pauseTask(taskId)
      message.success('任务已暂停')
    } catch (error) {
      message.error('暂停任务失败')
    }
  }

  const handleCancelTask = async (taskId: string) => {
    try {
      await cancelTask(taskId)
      message.success('任务已取消')
    } catch (error) {
      message.error('取消任务失败')
    }
  }

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask(taskId)
      message.success('任务已删除')
    } catch (error) {
      message.error('删除任务失败')
    }
  }

  // 创建任务
  const handleCreateTask = async (values: any) => {
    try {
      const taskData: CreateTaskData = {
        name: values.name,
        description: values.description,
        config: {
          urls: values.urls.split('\n').filter((url: string) => url.trim()),
          maxPages: values.maxPages,
          delay: values.delay,
          timeout: values.timeout,
          screenshot: values.screenshot,
          extractText: values.extractText,
          extractLinks: values.extractLinks,
          extractImages: values.extractImages,
        },
        priority: values.priority,
      }

      await createTask(taskData)
      message.success('任务创建成功')
      setCreateModalVisible(false)
      createForm.resetFields()
    } catch (error) {
      message.error('创建任务失败')
    }
  }

  // 处理表格变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    })
  }

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>任务管理</Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="总任务数" value={stats.total} />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.running}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats.failed}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Select
                placeholder="状态筛选"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => setFilters({ status: value })}
              >
                {Object.entries(statusConfig).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
              
              <Select
                placeholder="优先级筛选"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => setFilters({ priority: value })}
              >
                {Object.entries(priorityConfig).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchTasks()}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                创建任务
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 任务表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          loading={loading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 创建任务模态框 */}
      <Modal
        title="创建新任务"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateTask}
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item name="description" label="任务描述">
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Form.Item
            name="urls"
            label="目标URL"
            rules={[{ required: true, message: '请输入目标URL' }]}
          >
            <TextArea
              rows={5}
              placeholder="请输入目标URL，每行一个"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                initialValue={TaskPriority.NORMAL}
              >
                <Select>
                  {Object.entries(priorityConfig).map(([key, config]) => (
                    <Option key={key} value={key}>
                      {config.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxPages"
                label="最大页面数"
                initialValue={10}
              >
                <Input type="number" min={1} max={1000} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="delay"
                label="延迟时间(秒)"
                initialValue={1}
              >
                <Input type="number" min={0} step={0.1} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeout"
                label="超时时间(秒)"
                initialValue={30}
              >
                <Input type="number" min={1} max={300} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建任务
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TaskManagement
