#!/bin/bash

# 数懒平台开发服务器启动脚本
# 用于快速启动开发环境

set -e

echo "🚀 启动数懒平台开发服务器..."

# 检查必要的工具
check_requirements() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 启动数据库服务
start_databases() {
    echo "🗄️  启动数据库服务..."
    
    if command -v docker &> /dev/null; then
        # 检查PostgreSQL容器是否存在
        if ! docker ps -a | grep -q "digital-lazy-postgres"; then
            echo "启动PostgreSQL容器..."
            docker run -d \
                --name digital-lazy-postgres \
                -e POSTGRES_DB=digital_lazy_platform \
                -e POSTGRES_USER=user \
                -e POSTGRES_PASSWORD=password \
                -p 5432:5432 \
                postgres:15
        else
            echo "启动现有PostgreSQL容器..."
            docker start digital-lazy-postgres
        fi
        
        # 检查Redis容器是否存在
        if ! docker ps -a | grep -q "digital-lazy-redis"; then
            echo "启动Redis容器..."
            docker run -d \
                --name digital-lazy-redis \
                -p 6379:6379 \
                redis:7-alpine
        else
            echo "启动现有Redis容器..."
            docker start digital-lazy-redis
        fi
        
        echo "等待数据库启动..."
        sleep 5
    else
        echo "⚠️  Docker未安装，请确保PostgreSQL和Redis已手动启动"
    fi
}

# 启动后端服务
start_backend() {
    echo "🐍 启动后端服务..."
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo "❌ 虚拟环境不存在，请先运行 ./scripts/dev-setup.sh"
        exit 1
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查环境配置
    if [ ! -f ".env" ]; then
        echo "⚠️  .env文件不存在，使用默认配置"
        cp .env.example .env
    fi
    
    # 运行数据库迁移
    echo "运行数据库迁移..."
    alembic upgrade head
    
    # 启动后端服务
    echo "启动FastAPI服务器..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    
    cd ..
    echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"
}

# 启动前端服务
start_frontend() {
    echo "⚛️  启动前端服务..."
    
    cd frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    
    # 检查环境配置
    if [ ! -f ".env.local" ]; then
        echo "创建前端环境配置..."
        cat > .env.local << EOF
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:8000
VITE_APP_NAME=数懒平台
VITE_APP_VERSION=1.0.0
EOF
    fi
    
    # 启动前端服务
    echo "启动Vite开发服务器..."
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"
}

# 等待服务启动
wait_for_services() {
    echo "⏳ 等待服务启动..."
    
    # 等待后端启动
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            echo "✅ 后端服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ 后端服务启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待前端启动
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✅ 前端服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ 前端服务启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 显示服务信息
show_info() {
    echo ""
    echo "🎉 数懒平台开发服务器启动完成！"
    echo ""
    echo "📱 访问地址："
    echo "   前端应用:  http://localhost:3000"
    echo "   后端API:   http://localhost:8000"
    echo "   API文档:   http://localhost:8000/docs"
    echo "   ReDoc:     http://localhost:8000/redoc"
    echo ""
    echo "🗄️  数据库："
    echo "   PostgreSQL: localhost:5432"
    echo "   Redis:      localhost:6379"
    echo ""
    echo "📋 默认账号："
    echo "   用户名: demo"
    echo "   密码:   123456"
    echo ""
    echo "🛑 停止服务: Ctrl+C 或运行 ./stop-dev.sh"
    echo ""
}

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # 停止其他相关进程
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    
    echo "✅ 服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    check_requirements
    start_databases
    start_backend
    start_frontend
    wait_for_services
    show_info
    
    # 保持脚本运行
    echo "按 Ctrl+C 停止所有服务..."
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"
