/**
 * 主题状态管理
 * 管理应用主题设置
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface ThemeState {
  // 状态
  isDarkMode: boolean
  primaryColor: string
  fontSize: number
  compactMode: boolean
  sidebarCollapsed: boolean

  // 操作
  toggleDarkMode: () => void
  setPrimaryColor: (color: string) => void
  setFontSize: (size: number) => void
  toggleCompactMode: () => void
  toggleSidebar: () => void
  resetTheme: () => void
}

const defaultTheme = {
  isDarkMode: false,
  primaryColor: '#1890ff',
  fontSize: 14,
  compactMode: false,
  sidebarCollapsed: false,
}

export const useThemeStore = create<ThemeState>()(
  persist(
    immer((set) => ({
      ...defaultTheme,

      // 切换深色模式
      toggleDarkMode: () => {
        set((state) => {
          state.isDarkMode = !state.isDarkMode
        })
      },

      // 设置主色调
      setPrimaryColor: (color: string) => {
        set((state) => {
          state.primaryColor = color
        })
      },

      // 设置字体大小
      setFontSize: (size: number) => {
        set((state) => {
          state.fontSize = Math.max(12, Math.min(18, size))
        })
      },

      // 切换紧凑模式
      toggleCompactMode: () => {
        set((state) => {
          state.compactMode = !state.compactMode
        })
      },

      // 切换侧边栏折叠状态
      toggleSidebar: () => {
        set((state) => {
          state.sidebarCollapsed = !state.sidebarCollapsed
        })
      },

      // 重置主题
      resetTheme: () => {
        set((state) => {
          Object.assign(state, defaultTheme)
        })
      },
    })),
    {
      name: 'theme-storage',
    }
  )
)
