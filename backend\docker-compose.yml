# 数懒平台开发环境 Docker Compose 配置
version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: digital-lazy-postgres
    environment:
      POSTGRES_DB: digital_lazy_platform
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - digital-lazy-network
    restart: unless-stopped

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: digital-lazy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - digital-lazy-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: digital-lazy-backend
    environment:
      - DATABASE_URL=****************************************/digital_lazy_platform
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-change-in-production
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - digital-lazy-network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # 任务处理器
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: digital-lazy-worker
    environment:
      - DATABASE_URL=****************************************/digital_lazy_platform
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - digital-lazy-network
    restart: unless-stopped
    command: python -m app.workers.task_worker

  # 数据库管理工具 (可选)
  adminer:
    image: adminer
    container_name: digital-lazy-adminer
    ports:
      - "8080:8080"
    networks:
      - digital-lazy-network
    restart: unless-stopped

  # Redis 管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: digital-lazy-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - digital-lazy-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  digital-lazy-network:
    driver: bridge
