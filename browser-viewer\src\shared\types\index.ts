/**
 * 数懒孪生代理 - 核心类型定义
 */

// 通用唯一标识符与时间戳
// 保留简单的类型别名以确保与现有代码的兼容性，同时提供语义价值
export type TaskId = string; // NOSONAR
export type TabId = number; // NOSONAR
export type Timestamp = number; // NOSONAR

// 任务相关类型
export type OutputFormat = 'text' | 'markdown' | 'json';

// 行为引擎相关类型
export type BehaviorSpeed = 'low' | 'medium' | 'high';
export type SimulatorSpeed = 'slow' | 'medium' | 'fast';

export interface TaskConfig {
  urls: string[];
  batchId?: string;
  behaviorProfile: 'stealth' | 'normal' | 'aggressive';
  outputFormat: OutputFormat;
  priority?: number;
  retries?: number;
  retryCount?: number;
  timeout?: number;
  continueOnError?: boolean;
  mode?: 'headless' | 'visual';
  renderMode?: RenderMode;
  captureSystem?: 'v1' | 'v2';
  captureOptions?: CaptureOptions;
}

export interface TaskMetrics {
  batchId: string;
  id: string;
  status: TaskStatus;
  startTime: number;
  endTime?: number;
  processedPages: number;
  totalPages: number;
  cpuUsage: number;
  memoryUsage: number;
  networkUsage: number;
  errors: TaskError[];
  config?: TaskConfig;
  tabPerformance?: PerformanceMetrics;
}

export const TaskStatus = {
  PENDING: 'pending',
  RUNNING: 'running', 
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

export type TaskStatus = typeof TaskStatus[keyof typeof TaskStatus];

export interface TaskError {
  type: string;
  message: string;
  timestamp: number;
  url?: string;
  stack?: string;
}

// 行为模拟相关类型
export interface ScrollOptions {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration: number;
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

export interface Point {
  x: number;
  y: number;
}

export interface BehaviorProfile {
  scrollSpeed: number;
  preClickDelay: { min: number; max: number }; // 点击前延迟范围
  postActionDelay: { min: number; max: number }; // 动作后延迟范围
  randomness: number;
  distractionRate: number;
  scrollPause: { min: number; max: number }; // 每次滚动后的暂停时间
}

// 视觉处理相关类型
export interface ImageSegment {
  id: string;
  position: Rectangle;
  data: ArrayBuffer;
  mimeType: 'image/jpeg' | 'image/png' | 'image/webp'; // 图像的MIME类型
  timestamp: number;
  bitmap?: ImageBitmap;
}

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ContentSegment {
  id: string;
  position: Rectangle;
  content: string;
  confidence: number;
  type: 'text' | 'image' | 'mixed';
}

export interface CaptureSession {
  sessionId: string;
  taskId: string;
  tabId: number;
  status: 'running' | 'completed' | 'failed';
  contentSegments: ContentSegment[];
  startTime: number;
  endTime?: number;
}

export interface CaptureOptions {
  quality: number;
  blockSize: number;
  overlap: number;
  format: 'png' | 'jpeg' | 'webp';
}

// 服务通信相关类型
export interface ServiceMessage<T = unknown> {
  id: string;
  type: string;
  target?: string;
  payload?: T;
  timestamp: number;
}

export interface ServiceResponse<T = unknown> {
  id: string;
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Simplified task representation for UI
export interface BatchTask {
  batchId: string;
  status: TaskStatus;
  config: TaskConfig;
  progress: {
    total: number;
    processed: number;
    succeeded: number;
    failed: number;
  };
  urlStatuses: UrlStatus[];
  createdAt: Timestamp;
  completedAt?: Timestamp;
}

export interface UrlStatus {
  taskId: TaskId;
  url: string;
  status: 'pending' | 'running' | 'failed' | 'succeeded';
}

// 异常处理相关类型
export interface ExceptionEvent {
  type: ExceptionType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  context: Record<string, unknown>;
  timestamp: number;
  taskId?: string;
  tabId?: number;
}

export const ExceptionType = {
  CAPTCHA_DETECTED: 'captcha_detected',
  REDIRECT_LOOP: 'redirect_loop',
  AUTH_EXPIRED: 'auth_expired',
  BEHAVIOR_ANOMALY: 'behavior_anomaly',
  NETWORK_ERROR: 'network_error',
  RESOURCE_EXHAUSTED: 'resource_exhausted',
  DOM_CHANGED: 'dom_changed'
} as const;

export type ExceptionType = typeof ExceptionType[keyof typeof ExceptionType];

// 页面状态快照，用于模式切换
export interface PageSnapshot {
  url: string;
  scrollPosition: Point;
}

// 页面维度信息
export interface PageDimensions {
  scrollWidth: number;
  scrollHeight: number;
  clientWidth: number;
  clientHeight: number;
}

// 通过类型代理，安全地将浏览器特定的类型引入共享域
import type { BrowserCookie } from './browser-specific';

// 通过类型代理，安全地将浏览器特定的类型引入共享域
export type Cookie = BrowserCookie;

// 状态快照相关类型
export interface TabState {
  timestamp: number;
  url: string;
  dom: string;
  scrollPosition: Point;
  cookies: Cookie[];
  formData: Record<string, string>;
  localStorage: Record<string, string>;
  sessionStorage: Record<string, string>;
}

// 性能监控相关类型
export interface PerformanceMetrics {
  cpu: number;
  memory: number;
  network: number;
  fps: number;
  latency: number;
  timestamp: number;
}

export interface ResourceThresholds {
  CPU: number;
  MEMORY: number;
  NETWORK: number;
  FPS: number;
}

// 渲染模式相关类型
export const RenderMode = {
  HEADLESS: 'headless',
  VISUAL: 'visual'
} as const;

export type RenderMode = typeof RenderMode[keyof typeof RenderMode];

export interface RenderingContext {
  mode: RenderMode;
  tabId?: number;
  offscreenDocument?: Document;
  maskElement?: HTMLElement;
}

// AI服务相关类型
export interface AIProcessingRequest {
  segments: ImageSegment[];
  priority: 'low' | 'normal' | 'high';
  model?: string;
  options?: Record<string, unknown>;
}

export interface AIProcessingResponse {
  segments: ContentSegment[];
  processingTime: number;
  model: string;
  confidence: number;
}

// 权限相关类型
export interface PermissionConfig {
  domains: string[];
  cookies: boolean;
  localStorage: boolean;
  screenshots: boolean;
  automation: boolean;
}

// 配置相关类型
export interface AgentConfig {
  maxConcurrentTasks: number;
  resourceThresholds: ResourceThresholds;
  behaviorProfiles: Record<string, BehaviorProfile>;
  captureOptions: CaptureOptions;
  permissions: PermissionConfig;
  ai: {
    provider: string;
    model: string;
    apiKey?: string;
    endpoint?: string;
  };
}

// 事件相关类型
export interface AgentEvent<T = unknown> {
  type: string;
  data: T;
  timestamp: number;
  source: string;
}

// 工作流相关类型
export interface WorkflowStep {
  id: string;
  type: 'navigate' | 'scroll' | 'click' | 'capture' | 'wait' | 'extract';
  params: Record<string, unknown>;
  condition?: string;
  timeout?: number;
}

export interface Workflow {
  id: string;
  name: string;
  steps: WorkflowStep[];
  config: TaskConfig;
  createdAt: number;
  updatedAt: number;
}

// 安全与隐私相关类型

/**
 * 数据访问事件的类别。
 */
export const DataAccessCategory = {
  COOKIE: 'cookie',
  STORAGE: 'storage', // localStorage, sessionStorage
  HISTORY: 'history',
  SCREEN_CAPTURE: 'screen_capture',
  USER_INPUT: 'user_input', // 表单数据
} as const;
export type DataAccessCategory = typeof DataAccessCategory[keyof typeof DataAccessCategory];

/**
 * 数据访问事件，用于隐私审计。
 */
export interface DataAccessEvent {
  // 事件类型
  type: 'read' | 'write' | 'delete';
  // 数据类别
  category: DataAccessCategory;
  // 访问者/模块
  accessedBy: string;
  // 用户是否同意
  userApproved: boolean;
  // 相关上下文，如URL或存储键
  context?: Record<string, unknown>;
}

/**
 * 用户隐私设置。
 */
export interface PrivacySettings {
  // 禁止同步Cookie的域名黑名单
  cookieDeniedDomains: string[];
  // 是否启用审计日志
  enableAuditLog: boolean;
  // 禁止执行代理的URL黑名单
  proxyUrlBlacklist: string[];
}

export interface ClickOptions {
  x: number;
  y: number;
}

export interface ActivatePayload {
  taskId: string;
  mode: 'headless' | 'visual';
}

export interface AIConfig {
  apiKey: string;
  apiEndpoint: string;
  model: string;
  prompt: string;
}