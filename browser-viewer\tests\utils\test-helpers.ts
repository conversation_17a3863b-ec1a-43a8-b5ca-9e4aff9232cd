/**
 * 测试辅助工具库
 * 提供通用的测试工具函数和工厂函数
 */

import type { Task } from '@/core/task-manager/Task';
import type { TaskConfig, TaskStatus } from '@/shared/types';
import { BehaviorSubject, Subject } from 'rxjs';
import { vi } from 'vitest';

// 等待函数
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 等待下一个事件循环
export function nextTick(): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, 0));
}

// 等待条件满足
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout = 5000,
  interval = 50
): Promise<void> {
  const start = Date.now();
  
  while (Date.now() - start < timeout) {
    const result = await condition();
    if (result) return;
    
    await sleep(interval);
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
}

// 等待异步操作完成
export async function waitForAsync<T>(
  asyncFn: () => Promise<T>,
  timeout = 5000
): Promise<T> {
  return Promise.race([
    asyncFn(),
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`Async operation timeout after ${timeout}ms`)), timeout)
    ),
  ]);
}

// 创建模拟的 TaskConfig
export function createMockTaskConfig(
  overrides: Partial<TaskConfig> = {},
): TaskConfig {
  return {
    urls: ['https://example.com'],
    behaviorProfile: 'normal',
    outputFormat: 'text',
    priority: 10,
    timeout: 30000,
    retryCount: 3,
    ...overrides,
  };
}

// 创建模拟的 Task 实例
export function createMockTask(config?: Partial<TaskConfig>): Task {
  const taskConfig = createMockTaskConfig(config);
  const statusSubject = new BehaviorSubject<TaskStatus>('pending');
  const eventsSubject = new Subject<any>();

  const mockTask = {
    id: `task-${Math.random().toString(36).substring(2, 11)}`,
    _id: `task-${Math.random().toString(36).substring(2, 11)}`,
    _config: taskConfig,
    config: taskConfig,
    getStatus: vi.fn().mockImplementation(() => statusSubject.value),
    getRenderMode: vi.fn().mockReturnValue('headless'),
    getCurrentUrl: vi.fn().mockReturnValue(undefined),
    getProcessedUrls: vi.fn().mockReturnValue([]),
    getRemainingUrls: vi.fn().mockReturnValue(taskConfig.urls),
    getExecutionTime: vi.fn().mockReturnValue(0),
    start: vi.fn().mockResolvedValue(undefined),
    pause: vi.fn().mockResolvedValue(undefined),
    resume: vi.fn().mockResolvedValue(undefined),
    cancel: vi.fn().mockResolvedValue(undefined),
    switchMode: vi.fn().mockResolvedValue(undefined),
    checkHealth: vi.fn().mockResolvedValue(true),
    serialize: vi.fn().mockReturnValue({}),
    applyThrottling: vi.fn(),
    status$: statusSubject,
    events$: eventsSubject,
    handleContentScriptReady: vi.fn(),
    // Add other properties of Task if needed for compilation
  };

  return mockTask as unknown as Task;
}

// 创建模拟的 DOM 元素
export function createMockElement(tagName: string, attributes: Record<string, string> = {}): HTMLElement {
  const element = document.createElement(tagName);
  
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
  
  return element;
}

// 创建模拟的图像数据
export function createMockImageData(width = 100, height = 100): ImageData {
  const data = new Uint8ClampedArray(width * height * 4);
  
  // 填充随机像素数据
  for (let i = 0; i < data.length; i += 4) {
    data[i] = Math.floor(Math.random() * 256);     // R
    data[i + 1] = Math.floor(Math.random() * 256); // G
    data[i + 2] = Math.floor(Math.random() * 256); // B
    data[i + 3] = 255;                             // A
  }
  
  return new ImageData(data, width, height);
}

// 创建模拟的 Blob
export function createMockBlob(content = 'mock content', type = 'text/plain'): Blob {
  return new Blob([content], { type });
}

// 创建模拟的 ArrayBuffer
export function createMockArrayBuffer(size = 1024): ArrayBuffer {
  const buffer = new ArrayBuffer(size);
  const view = new Uint8Array(buffer);
  
  // 填充随机数据
  for (let i = 0; i < size; i++) {
    view[i] = Math.floor(Math.random() * 256);
  }
  
  return buffer;
}

// 创建模拟的错误
export function createMockError(message = 'Mock error', code?: string): Error {
  const error = new Error(message);
  if (code) {
    (error as any).code = code;
  }
  return error;
}

// 模拟网络请求响应
export function createMockResponse(
  data: any = { success: true },
  status = 200,
  statusText = 'OK'
): Response {
  return new Response(JSON.stringify(data), {
    status,
    statusText,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 模拟事件
export function createMockEvent(type: string, data: any = {}): Event {
  const event = new Event(type);
  Object.assign(event, data);
  return event;
}

// 模拟 MessageEvent
export function createMockMessageEvent(data: any, origin = 'chrome-extension://test'): MessageEvent {
  return new MessageEvent('message', {
    data,
    origin,
    source: window,
  });
}

// Mock RxJS Observable
export function createMockObservable<T>(values: T[] = []) {
  const index = 0;
  
  return {
    subscribe: vi.fn().mockImplementation((observer: any) => {
      const subscription = {
        unsubscribe: vi.fn(),
        closed: false,
      };
      
      // 异步发送值
      setTimeout(() => {
        values.forEach(value => {
          if (!subscription.closed && observer.next) {
            observer.next(value);
          }
        });
        
        if (!subscription.closed && observer.complete) {
          observer.complete();
        }
      }, 0);
      
      return subscription;
    }),
    pipe: vi.fn().mockReturnThis(),
    toPromise: vi.fn().mockResolvedValue(values[values.length - 1]),
  };
}

// 性能测量工具
export class PerformanceMeasure {
  private readonly startTime: number;
  private readonly measurements: Map<string, number> = new Map();
  
  constructor() {
    this.startTime = performance.now();
  }
  
  mark(name: string): void {
    this.measurements.set(name, performance.now() - this.startTime);
  }
  
  getMeasurement(name: string): number | undefined {
    return this.measurements.get(name);
  }
  
  getAllMeasurements(): Record<string, number> {
    return Object.fromEntries(this.measurements);
  }
  
  getDuration(): number {
    return performance.now() - this.startTime;
  }
}

// 测试数据生成器
export class TestDataGenerator {
  static randomString(length = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  static randomNumber(min = 0, max = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
  
  static randomBoolean(): boolean {
    return Math.random() > 0.5;
  }
  
  static randomUrl(): string {
    const domains = ['example.com', 'test.org', 'demo.net', 'sample.io'];
    const paths = ['', '/page', '/section', '/item'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    const path = paths[Math.floor(Math.random() * paths.length)];
    
    return `https://${domain}${path}`;
  }
  
  static randomUrls(count = 5): string[] {
    return Array.from({ length: count }, () => this.randomUrl());
  }
}

// 断言辅助函数
export function expectEventually(
  condition: () => boolean | Promise<boolean>,
  timeout = 5000
): Promise<void> {
  return waitFor(condition, timeout);
}

// 模拟用户交互
export function simulateUserInteraction(element: HTMLElement, eventType = 'click'): void {
  const event = new Event(eventType, { bubbles: true, cancelable: true });
  element.dispatchEvent(event);
}

// 清理测试环境
export function cleanupTestEnvironment(): void {
  // 清理 JSDOM 环境
  document.body.innerHTML = '';
  document.head.innerHTML = '';

  // 清理 localStorage 和 sessionStorage
  localStorage.clear();
  sessionStorage.clear();

  // 重置所有 vitest mocks
  vi.restoreAllMocks();

  // 如果环境中存在定时器，也一并清理
  // （在 node 环境中可能需要额外的设置来模拟和清理）
}

// 导出所有工具
export const TestUtils = {
  sleep,
  nextTick,
  waitFor,
  waitForAsync,
  createMockTaskConfig,
  createMockTask,
  createMockElement,
  createMockImageData,
  createMockBlob,
  createMockArrayBuffer,
  createMockError,
  createMockResponse,
  createMockEvent,
  createMockMessageEvent,
  createMockObservable,
  PerformanceMeasure,
  TestDataGenerator,
  expectEventually,
  simulateUserInteraction,
  cleanupTestEnvironment,
};